// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'portal_user_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserPreferences _$UserPreferencesFromJson(Map<String, dynamic> json) =>
    UserPreferences(
      language: json['language'] as String? ?? 'en-US',
      notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
    );

Map<String, dynamic> _$UserPreferencesToJson(UserPreferences instance) =>
    <String, dynamic>{
      'language': instance.language,
      'notificationsEnabled': instance.notificationsEnabled,
    };

PortalUserData _$PortalUserDataFromJson(Map<String, dynamic> json) =>
    PortalUserData(
      uid: json['uid'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      displayName: json['displayName'] as String,
      bio: json['bio'] as String?,
      phoneNumber: json['phoneNumber'] as String,
      photoURL: json['photoURL'] as String?,
      invitationCode: json['invitationCode'] as String?,
      userType: json['userType'] as String? ?? '202',
      preferences: json['preferences'] == null
          ? null
          : UserPreferences.fromJson(
              json['preferences'] as Map<String, dynamic>),
      sid: json['sid'] as String?,
      isValid: json['isValid'] == null
          ? true
          : JsonUtil.boolFromJson(json['isValid']),
      isSynced: json['isSynced'] == null
          ? true
          : JsonUtil.boolFromJson(json['isSynced']),
    )
      ..id = (json['id'] as num?)?.toInt()
      ..name = json['name'] as String?
      ..createdBy = json['createdBy'] as String?
      ..createDate = (json['createDate'] as num?)?.toInt()
      ..updatedBy = json['updatedBy'] as String?
      ..updateDate = (json['updateDate'] as num?)?.toInt()
      ..reviewedBy = json['reviewedBy'] as String?
      ..reviewDate = (json['reviewDate'] as num?)?.toInt()
      ..approveStatus = JsonUtil.boolFromJson(json['approveStatus'])
      ..notes = json['notes'] as String?
      ..tags =
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList();

Map<String, dynamic> _$PortalUserDataToJson(PortalUserData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'createdBy': instance.createdBy,
      'createDate': instance.createDate,
      'updatedBy': instance.updatedBy,
      'updateDate': instance.updateDate,
      'reviewedBy': instance.reviewedBy,
      'reviewDate': instance.reviewDate,
      'approveStatus': JsonUtil.boolToJson(instance.approveStatus),
      'notes': instance.notes,
      'tags': instance.tags,
      'uid': instance.uid,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'displayName': instance.displayName,
      'bio': instance.bio,
      'phoneNumber': instance.phoneNumber,
      'photoURL': instance.photoURL,
      'invitationCode': instance.invitationCode,
      'userType': instance.userType,
      'preferences': instance.preferences,
    };
