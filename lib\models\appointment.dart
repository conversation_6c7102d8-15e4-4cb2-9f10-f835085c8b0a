import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/common/enum/appointment_enum.dart';
import 'base_full_model.dart';

part 'appointment.g.dart';

@JsonSerializable()
class CustomerInfo {
  String name;
  String? email;
  String? phoneNumber;

  CustomerInfo({
    required this.name,
    this.email,
    this.phoneNumber,
  });

  factory CustomerInfo.fromJson(Map<String, dynamic> json) =>
      _$CustomerInfoFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerInfoToJson(this);
}

@JsonSerializable()
class TimeInfo {
  String date;                    // YYYY-MM-DD
  String startTime;               // HH:MM
  String endTime;                 // HH:MM
  int duration;                   // 时长（分钟）
  String timezone;                // 时区

  TimeInfo({
    required this.date,
    required this.startTime,
    required this.endTime,
    required this.duration,
    required this.timezone,
  });

  factory TimeInfo.fromJson(Map<String, dynamic> json) =>
      _$TimeInfoFromJson(json);

  Map<String, dynamic> toJson() => _$TimeInfoToJson(this);
}

@JsonSerializable()
class ServiceInfo {
  String serviceName;
  ServiceCategory serviceCategory;
  ServiceBreed serviceBreed;
  int duration;
  double price;
  Currency currency;

  ServiceInfo({
    required this.serviceName,
    required this.serviceCategory,
    required this.serviceBreed,
    required this.duration,
    required this.price,
    required this.currency,
  });

  factory ServiceInfo.fromJson(Map<String, dynamic> json) =>
      _$ServiceInfoFromJson(json);

  Map<String, dynamic> toJson() => _$ServiceInfoToJson(this);
}

@JsonSerializable()
class StaffInfo {
  String staffName;
  String? staffEmail;

  StaffInfo({
    required this.staffName,
    this.staffEmail,
  });

  factory StaffInfo.fromJson(Map<String, dynamic> json) =>
      _$StaffInfoFromJson(json);

  Map<String, dynamic> toJson() => _$StaffInfoToJson(this);
}

/// Appointment - 预约
/// 存储在 Firestore collection "appointment"
/// 匹配Web端的实际数据结构
@JsonSerializable()
class Appointment extends BaseFullModel {
  String appointmentId;              // 预约唯一ID
  String storeId;                    // 店铺ID
  String customerId;                 // 客户ID
  String staffId;                    // 员工ID
  String serviceId;                  // 服务ID
  String status;                     // 预约状态 (confirmed, pending, cancelled, completed)
  String source;                     // 预约来源 (portal, app)
  TimeInfo timeInfo;                 // 时间信息
  CustomerInfo customerInfo;         // 客户信息快照
  ServiceInfo serviceInfo;           // 服务信息快照
  StaffInfo staffInfo;               // 员工信息快照
  String? notes;                     // 预约备注
  String? customerNotes;             // 客户备注
  List<String> remindersSent;        // 已发送的提醒列表

  Appointment({
    required this.appointmentId,
    required this.storeId,
    required this.customerId,
    required this.staffId,
    required this.serviceId,
    required this.status,
    required this.source,
    required this.timeInfo,
    required this.customerInfo,
    required this.serviceInfo,
    required this.staffInfo,
    this.notes,
    this.customerNotes,
    this.remindersSent = const [],
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.tags,
  });

  factory Appointment.fromJson(Map<String, dynamic> json) =>
      _$AppointmentFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$AppointmentToJson(this);

  Map<String, dynamic> toFirestoreData() {
    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      // 确保嵌套对象正确序列化
      if (value is TimeInfo) {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = value.toJson();
      } else if (value is CustomerInfo) {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = value.toJson();
      } else if (value is ServiceInfo) {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = value.toJson();
      } else if (value is StaffInfo) {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = value.toJson();
      } else {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
      }
    });
    return dbData;
  }

  static String get collection => 'appointment';

  bool canBeCancelled() {
    return status == 'confirmed' || status == 'pending';
  }

  bool canBeStarted() {
    return status == 'confirmed';
  }

  bool canBeCompleted() {
    return status == 'in_progress';
  }

  DateTime getAppointmentDateTime() {
    return DateTime.parse('${timeInfo.date} ${timeInfo.startTime}');
  }

  bool isUpcoming() {
    final appointmentTime = getAppointmentDateTime();
    return appointmentTime.isAfter(DateTime.now()) &&
           (status == 'confirmed' || status == 'pending');
  }

  bool isPast() {
    final appointmentTime = getAppointmentDateTime();
    return appointmentTime.isBefore(DateTime.now());
  }
}
