import { BaseModel, BaseModelImpl, JsonUtil } from './base_model';
import { 
  PetGender, 
  PetType, 
  PetVisibility, 
  Timestamp,
  Device,
  GeoFirePoint,
} from './types';

export enum CustomerSource {
  ONE_NATA = 'oneNata',
  PORTAL = 'portal',
  EXTERNAL_LINK = 'externalLink'
}


export interface Customer {
  customerAccount: UserAccount;
  customerData: UserData;
  pets?: Pet[];
}

/**
 * OneNata App 用户账户
 * 存储在 Firestore collection "UserAccount"
 */
export interface UserAccount extends BaseModel {
  fid: string; // Firebase user id
  phoneNumber?: string;
  email?: string;
  salt?: string;
  hashedCredential?: string;
  isEmailVerified?: boolean;
}

export class UserAccountImpl extends BaseModelImpl implements UserAccount {
  fid: string;
  phoneNumber?: string;
  email?: string;
  salt?: string;
  hashedCredential?: string;
  isEmailVerified?: boolean;

  constructor(data: Partial<UserAccount>) {
    super(data);
    this.fid = data.fid || '';
    this.phoneNumber = data.phoneNumber;
    this.email = data.email;
    this.salt = data.salt;
    this.hashedCredential = data.hashedCredential;
    this.isEmailVerified = data.isEmailVerified;
  }

  static fromJson(json: Record<string, unknown>): UserAccountImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new UserAccountImpl({
      ...baseModel,
      fid: JsonUtil.stringFromJson(json.fid) || '',
      phoneNumber: JsonUtil.stringFromJson(json.phoneNumber),
      email: JsonUtil.stringFromJson(json.email),
      salt: JsonUtil.stringFromJson(json.salt),
      hashedCredential: JsonUtil.stringFromJson(json.hashedCredential),
      isEmailVerified: JsonUtil.boolFromJson(json.isEmailVerified),
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      fid: this.fid,
      phoneNumber: JsonUtil.stringToJson(this.phoneNumber),
      email: JsonUtil.stringToJson(this.email),
      salt: JsonUtil.stringToJson(this.salt),
      hashedCredential: JsonUtil.stringToJson(this.hashedCredential),
      isEmailVerified: JsonUtil.boolToJson(this.isEmailVerified),
    };
  }
}

/**
 * OneNata App 用户数据
 * 存储在 Firestore collection "UserData"
 */
export interface UserData extends BaseModel {
  uid: string; // UserAccount sid
  avatar?: string;
  bio?: string;
  userType?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  photoURL?: string;
  pets?: Pet[];
}

export class UserDataImpl extends BaseModelImpl implements UserData {
  uid: string;
  avatar?: string;
  bio?: string;
  userType?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  photoURL?: string;
  pets?: Pet[];

  constructor(data: Partial<UserData>) {
    super(data);
    this.uid = data.uid || '';
    this.avatar = data.avatar;
    this.bio = data.bio;
    this.userType = data.userType;
    this.firstName = data.firstName;
    this.lastName = data.lastName;
    this.email = data.email;
    this.phoneNumber = data.phoneNumber;
    this.photoURL = data.photoURL;
    this.pets = data.pets;
  }

  static fromJson(json: Record<string, unknown>): UserDataImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new UserDataImpl({
      ...baseModel,
      uid: JsonUtil.stringFromJson(json.uid) || '',
      avatar: JsonUtil.stringFromJson(json.avatar),
      bio: JsonUtil.stringFromJson(json.bio),
      userType: JsonUtil.stringFromJson(json.userType),
      firstName: JsonUtil.stringFromJson(json.firstName),
      lastName: JsonUtil.stringFromJson(json.lastName),
      email: JsonUtil.stringFromJson(json.email),
      phoneNumber: JsonUtil.stringFromJson(json.phoneNumber),
      photoURL: JsonUtil.stringFromJson(json.photoURL),
      pets: json.pets as Pet[],
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      uid: this.uid,
      avatar: JsonUtil.stringToJson(this.avatar),
      bio: JsonUtil.stringToJson(this.bio),
      userType: JsonUtil.stringToJson(this.userType),
      firstName: JsonUtil.stringToJson(this.firstName),
      lastName: JsonUtil.stringToJson(this.lastName),
      email: JsonUtil.stringToJson(this.email),
      phoneNumber: JsonUtil.stringToJson(this.phoneNumber),
      photoURL: JsonUtil.stringToJson(this.photoURL),
      pets: this.pets,
    };
  }

  getFullName(): string {
    if (this.firstName && this.lastName) {
      return `${this.firstName} ${this.lastName}`.trim();
    }
    return this.firstName || this.lastName || 'unknown user';
  }

  getDisplayName(): string {
    return this.getFullName() || this.name || 'unknown user';
  }
}

/**
 * 宠物信息
 * 存储在 Firestore collection "pet"
 */
export interface Pet extends BaseModel {
  ownerId: string; // 当前主人 ID，所有历史主人将存储在 ownerId record 中
  name: string; // 宠物名称
  regId?: string; // 注册 ID
  breed?: string; // 品种
  gender?: PetGender;
  type?: PetType;
  avatar?: string;
  isLive?: boolean;
  birthday?: Timestamp; // 生日（毫秒时间戳）
  notes?: string; // 宠物备注
  attachedDevices?: Device[]; // 附加设备
  latestLocation?: GeoFirePoint; // 最新位置
  latestLocationAt?: Timestamp; // 最新位置时间
  latestBatteryLevel?: number; // 最新电池电量
  latestBatteryLevelAt?: Timestamp; // 最新电池电量时间
  latestStandbyTime?: number; // 最新待机时间（秒）
  latestStandbyTimeAt?: Timestamp; // 最新待机时间记录时间
  visibility?: PetVisibility; // 可见性设置
}

export class PetImpl extends BaseModelImpl implements Pet {
  ownerId: string;
  name: string;
  regId?: string;
  breed?: string;
  gender?: PetGender;
  type?: PetType;
  avatar?: string;
  isLive?: boolean;
  birthday?: Timestamp;
  notes?: string;
  attachedDevices?: Device[];
  latestLocation?: GeoFirePoint;
  latestLocationAt?: Timestamp;
  latestBatteryLevel?: number;
  latestBatteryLevelAt?: Timestamp;
  latestStandbyTime?: number;
  latestStandbyTimeAt?: Timestamp;
  visibility?: PetVisibility;

  constructor(data: Partial<Pet>) {
    super(data);
    this.ownerId = data.ownerId || '';
    this.name = data.name || '';
    this.regId = data.regId;
    this.breed = data.breed;
    this.gender = data.gender;
    this.type = data.type;
    this.avatar = data.avatar;
    this.isLive = data.isLive;
    this.birthday = data.birthday;
    this.notes = data.notes;
    this.attachedDevices = data.attachedDevices;
    this.latestLocation = data.latestLocation;
    this.latestLocationAt = data.latestLocationAt;
    this.latestBatteryLevel = data.latestBatteryLevel;
    this.latestBatteryLevelAt = data.latestBatteryLevelAt;
    this.latestStandbyTime = data.latestStandbyTime;
    this.latestStandbyTimeAt = data.latestStandbyTimeAt;
    this.visibility = data.visibility;
  }

  static fromJson(json: Record<string, unknown>): PetImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new PetImpl({
      ...baseModel,
      ownerId: JsonUtil.stringFromJson(json.ownerId) || '',
      name: JsonUtil.stringFromJson(json.name) || '',
      regId: JsonUtil.stringFromJson(json.regId),
      breed: JsonUtil.stringFromJson(json.breed),
      gender: json.gender as PetGender,
      type: json.type as PetType,
      avatar: JsonUtil.stringFromJson(json.avatar),
      isLive: JsonUtil.boolFromJson(json.isLive),
      birthday: json.birthday ? new Date(json.birthday as string | number) : undefined,
      notes: JsonUtil.stringFromJson(json.notes),
      attachedDevices: json.attachedDevices as Device[],
      latestLocation: json.latestLocation as GeoFirePoint,
      latestLocationAt: json.latestLocationAt ? new Date(json.latestLocationAt as string | number) : undefined,
      latestBatteryLevel: JsonUtil.numberFromJson(json.latestBatteryLevel),
      latestBatteryLevelAt: json.latestBatteryLevelAt ? new Date(json.latestBatteryLevelAt as string | number) : undefined,
      latestStandbyTime: JsonUtil.numberFromJson(json.latestStandbyTime),
      latestStandbyTimeAt: json.latestStandbyTimeAt ? new Date(json.latestStandbyTimeAt as string | number) : undefined,
      visibility: json.visibility as PetVisibility,
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      ownerId: this.ownerId,
      name: this.name,
      regId: JsonUtil.stringToJson(this.regId),
      breed: JsonUtil.stringToJson(this.breed),
      gender: this.gender,
      type: this.type,
      avatar: JsonUtil.stringToJson(this.avatar),
      isLive: JsonUtil.boolToJson(this.isLive),
      birthday: this.birthday ? new Date(this.birthday).getTime() : undefined,
      notes: JsonUtil.stringToJson(this.notes),
      attachedDevices: this.attachedDevices,
      latestLocation: this.latestLocation,
      latestLocationAt: this.latestLocationAt ? new Date(this.latestLocationAt).getTime() : undefined,
      latestBatteryLevel: this.latestBatteryLevel,
      latestBatteryLevelAt: this.latestBatteryLevelAt ? new Date(this.latestBatteryLevelAt).getTime() : undefined,
      latestStandbyTime: this.latestStandbyTime,
      latestStandbyTimeAt: this.latestStandbyTimeAt ? new Date(this.latestStandbyTimeAt).getTime() : undefined,
      visibility: this.visibility,
    };
  }

  getAge(): number | null {
    if (!this.birthday) return null;
    const birthDate = new Date(this.birthday);
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - birthDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.floor(diffDays / 365);
  }

  getAgeInMonths(): number | null {
    if (!this.birthday) return null;
    const birthDate = new Date(this.birthday);
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - birthDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.floor(diffDays / 30);
  }

  isAlive(): boolean {
    return this.isLive === true;
  }

  getDisplayName(): string {
    return this.name || '未命名宠物';
  }

  getBatteryStatus(): string {
    if (!this.latestBatteryLevel) return '未知';
    if (this.latestBatteryLevel > 50) return '良好';
    if (this.latestBatteryLevel > 20) return '中等';
    return '低电量';
  }

  getLocationStatus(): string {
    if (!this.latestLocation) return '位置未知';
    if (!this.latestLocationAt) return '位置过期';
    
    const now = new Date().getTime();
    const locationTime = new Date(this.latestLocationAt).getTime();
    const diffMinutes = Math.floor((now - locationTime) / (1000 * 60));
    
    if (diffMinutes < 5) return '实时位置';
    if (diffMinutes < 30) return '最近位置';
    if (diffMinutes < 60) return '1小时前';
    return '位置过期';
  }
}

/**
 * 客户服务类
 */
// export class CustomerService {
//   /**
//    * 从 API 响应创建用户账户列表
//    */
//   static createUserAccountsFromApiResponse(response: unknown[]): UserAccountImpl[] {
//     return response
//       .filter((item): item is Record<string, unknown> => 
//         typeof item === 'object' && item !== null
//       )
//       .map(item => UserAccountImpl.fromJson(item));
//   }

//   /**
//    * 从 API 响应创建用户数据列表
//    */
//   static createUserDataFromApiResponse(response: unknown[]): UserDataImpl[] {
//     return response
//       .filter((item): item is Record<string, unknown> => 
//         typeof item === 'object' && item !== null
//       )
//       .map(item => UserDataImpl.fromJson(item));
//   }

//   /**
//    * 从 API 响应创建宠物列表
//    */
//   static createPetsFromApiResponse(response: unknown[]): PetImpl[] {
//     return response
//       .filter((item): item is Record<string, unknown> => 
//         typeof item === 'object' && item !== null
//       )
//       .map(item => PetImpl.fromJson(item));
//   }

//   /**
//    * 搜索用户
//    */
//   static searchUsers(users: UserData[], query: string): UserData[] {
//     const lowerQuery = query.toLowerCase();
//     return users.filter(user => 
//       user.firstName?.toLowerCase().includes(lowerQuery) ||
//       user.lastName?.toLowerCase().includes(lowerQuery) ||
//       user.name?.toLowerCase().includes(lowerQuery) ||
//       user.bio?.toLowerCase().includes(lowerQuery)
//     );
//   }

//   /**
//    * 搜索宠物
//    */
//   static searchPets(pets: Pet[], query: string): Pet[] {
//     const lowerQuery = query.toLowerCase();
//     return pets.filter(pet => 
//       pet.name?.toLowerCase().includes(lowerQuery) ||
//       pet.breed?.toLowerCase().includes(lowerQuery) ||
//       pet.regId?.toLowerCase().includes(lowerQuery)
//     );
//   }

//   /**
//    * 根据主人 ID 获取宠物
//    */
//   static getPetsByOwner(pets: Pet[], ownerId: string): Pet[] {
//     return pets.filter(pet => pet.ownerId === ownerId);
//   }

//   /**
//    * 获取活跃宠物
//    */
//   static getAlivePets(pets: Pet[]): Pet[] {
//     return pets.filter(pet => pet.isLive === true);
//   }

//   /**
//    * 根据类型过滤宠物
//    */
//   static getPetsByType(pets: Pet[], type: PetType): Pet[] {
//     return pets.filter(pet => pet.type === type);
//   }

//   /**
//    * 获取低电量宠物
//    */
//   static getLowBatteryPets(pets: Pet[], threshold: number = 20): Pet[] {
//     return pets.filter(pet => 
//       pet.latestBatteryLevel !== undefined && 
//       pet.latestBatteryLevel < threshold
//     );
//   }

//   /**
//    * 获取位置过期的宠物
//    */
//   static getOutdatedLocationPets(pets: Pet[], hoursThreshold: number = 24): Pet[] {
//     const now = new Date().getTime();
//     const thresholdTime = hoursThreshold * 60 * 60 * 1000;
    
//     return pets.filter(pet => {
//       if (!pet.latestLocationAt) return true;
//       const locationTime = new Date(pet.latestLocationAt).getTime();
//       return (now - locationTime) > thresholdTime;
//     });
//   }
// } 