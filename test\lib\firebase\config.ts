import { initializeApp, getApps } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';
import { getDatabase, connectDatabaseEmulator } from 'firebase/database';
import { getFunctions, connectFunctionsEmulator } from 'firebase/functions';
import { logEmulatorStatus, EMULATOR_CONFIG } from './emulators';

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

// 初始化 Firebase
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];

// 初始化 Auth
export const auth = getAuth(app);

// 初始化 Firestore
export const db = getFirestore(app);

// 初始化 Storage
export const storage = getStorage(app);

// 初始化 Realtime Database
export const database = getDatabase(app);

// 初始化 Functions
export const functions = getFunctions(app);

// 在开发环境中连接到模拟器
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  console.log('🔥 正在连接 Firebase 模拟器...');
  
  // Auth 模拟器
  try {
    connectAuthEmulator(auth, `http://${EMULATOR_CONFIG.auth.host}:${EMULATOR_CONFIG.auth.port}`, { disableWarnings: true });
    console.log('✅ Auth 模拟器连接成功');
  } catch {
    console.log('⚠️ Auth emulator already connected');
  }
  
  // Firestore 模拟器
  try {
    connectFirestoreEmulator(db, EMULATOR_CONFIG.firestore.host, EMULATOR_CONFIG.firestore.port);
    console.log('✅ Firestore 模拟器连接成功');
  } catch {
    console.log('⚠️ Firestore emulator already connected');
  }

  // Storage 模拟器
  try {
    connectStorageEmulator(storage, EMULATOR_CONFIG.storage.host, EMULATOR_CONFIG.storage.port);
    console.log('✅ Storage 模拟器连接成功');
  } catch {
    console.log('⚠️ Storage emulator already connected');
  }

  // Realtime Database 模拟器
  try {
    connectDatabaseEmulator(database, EMULATOR_CONFIG.database.host, EMULATOR_CONFIG.database.port);
    console.log('✅ Database 模拟器连接成功');
  } catch {
    console.log('⚠️ Database emulator already connected');
  }

  // Functions 模拟器
  try {
    connectFunctionsEmulator(functions, EMULATOR_CONFIG.functions.host, EMULATOR_CONFIG.functions.port);
    console.log('✅ Functions 模拟器连接成功');
  } catch {
    console.log('⚠️ Functions emulator already connected');
  }

  // 延迟检查模拟器状态
  setTimeout(() => {
    logEmulatorStatus();
  }, 1000);
}

export default app; 