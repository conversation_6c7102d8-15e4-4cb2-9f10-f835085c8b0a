import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'base_full_model.dart';

part 'bookable_service.g.dart';

/// Bookable Service - 可预约服务
/// 存储在 Firestore collection "bookable-service"
@JsonSerializable()
class BookableService extends BaseFullModel {
  String storeId;                    // 店铺ID
  String serviceId;                  // 服务唯一ID
  String serviceName;                // 服务名称
  ServiceCategory serviceCategory;   // 服务类别
  ServiceBreed serviceBreed;         // 服务对象
  String? description;               // 服务描述
  StoreServiceStatus status;         // 服务状态
  List<String> staffIds;             // 提供此服务的员工ID列表
  int minDuration;                   // 最短服务时间（分钟，必须是15的倍数）
  int maxDuration;                   // 最长服务时间（分钟，必须是15的倍数）
  int defaultDuration;               // 默认服务时间（分钟，必须是15的倍数）
  double basePrice;                  // 基础价格
  Currency currency;                 // 货币单位
  int maxCapacityPerSlot;            // 每个时间段最大容量
  bool isOnlineBookingEnabled;       // 是否支持在线预约
  bool requiresApproval;             // 是否需要审批
  String? cancellationPolicy;       // 取消政策
  List<String>? servicePhotos;       // 服务照片
  int totalBookings;                 // 总预约数
  int completedBookings;             // 已完成预约数

  BookableService({
    required this.storeId,
    required this.serviceId,
    required this.serviceName,
    required this.serviceCategory,
    required this.serviceBreed,
    this.description,
    required this.status,
    required this.staffIds,
    required this.minDuration,
    required this.maxDuration,
    required this.defaultDuration,
    required this.basePrice,
    required this.currency,
    this.maxCapacityPerSlot = 1,
    this.isOnlineBookingEnabled = true,
    this.requiresApproval = false,
    this.cancellationPolicy,
    this.servicePhotos,
    this.totalBookings = 0,
    this.completedBookings = 0,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory BookableService.fromJson(Map<String, dynamic> json) =>
      _$BookableServiceFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$BookableServiceToJson(this);

  /// 从 Firestore 文档创建 BookableService 对象
  factory BookableService.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

    // 直接使用数据，不进行字段名转换（因为数据库已经是camelCase）
    final Map<String, dynamic> jsonData = Map<String, dynamic>.from(data);

    // 处理Timestamp字段转换
    if (jsonData['createdAt'] is Timestamp) {
      jsonData['createdAt'] = (jsonData['createdAt'] as Timestamp).toDate().toIso8601String();
    }
    if (jsonData['updatedAt'] is Timestamp) {
      jsonData['updatedAt'] = (jsonData['updatedAt'] as Timestamp).toDate().toIso8601String();
    }
    if (jsonData['create_date'] is Timestamp) {
      jsonData['createDate'] = (jsonData['create_date'] as Timestamp).millisecondsSinceEpoch;
    }
    if (jsonData['update_date'] is Timestamp) {
      jsonData['updateDate'] = (jsonData['update_date'] as Timestamp).millisecondsSinceEpoch;
    }

    // 字段映射：数据库字段名 -> 模型字段名
    if (jsonData['serviceName'] == null && jsonData['name'] != null) {
      jsonData['serviceName'] = jsonData['name'];
    }
    if (jsonData['serviceName'] == null) {
      jsonData['serviceName'] = 'Unknown Service';
    }

    // 处理duration字段 - 确保类型转换
    if (jsonData['minDuration'] == null && jsonData['duration'] != null) {
      final duration = _parseToInt(jsonData['duration'], 30);
      jsonData['minDuration'] = duration;
      jsonData['maxDuration'] = duration;
      jsonData['defaultDuration'] = duration;
    }
    if (jsonData['minDuration'] == null) {
      jsonData['minDuration'] = 30;
      jsonData['maxDuration'] = 60;
      jsonData['defaultDuration'] = 30;
    } else {
      // 确保现有的duration字段也是正确的类型
      jsonData['minDuration'] = _parseToInt(jsonData['minDuration'], 30);
      jsonData['maxDuration'] = _parseToInt(jsonData['maxDuration'], 60);
      jsonData['defaultDuration'] = _parseToInt(jsonData['defaultDuration'], 30);
    }

    // 处理status字段
    if (jsonData['status'] == null && jsonData['serviceStatus'] != null) {
      jsonData['status'] = jsonData['serviceStatus'];
    }
    if (jsonData['status'] == null) {
      jsonData['status'] = 'active';
    }

    // 处理serviceCategory字段
    if (jsonData['serviceCategory'] == null) {
      jsonData['serviceCategory'] = 'grooming';
    }

    // 处理serviceBreed字段
    if (jsonData['serviceBreed'] == null) {
      jsonData['serviceBreed'] = 'Dog';
    }

    // 处理basePrice字段 - 确保类型转换
    if (jsonData['basePrice'] == null) {
      jsonData['basePrice'] = 0.0;
    } else {
      jsonData['basePrice'] = _parseToDouble(jsonData['basePrice'], 0.0);
    }

    // 处理currency字段
    if (jsonData['currency'] == null) {
      jsonData['currency'] = 'CAD';
    }

    // 处理staffIds字段
    if (jsonData['staffIds'] == null) {
      jsonData['staffIds'] = <String>[];
    }

    // 处理数字字段的类型转换
    jsonData['totalBookings'] = _parseToInt(jsonData['totalBookings'], 0);
    jsonData['completedBookings'] = _parseToInt(jsonData['completedBookings'], 0);
    jsonData['maxCapacityPerSlot'] = _parseToInt(jsonData['maxCapacityPerSlot'], 1);

    // 处理布尔字段
    if (jsonData['isOnlineBookingEnabled'] == null) {
      jsonData['isOnlineBookingEnabled'] = true;
    }
    if (jsonData['requiresApproval'] == null) {
      jsonData['requiresApproval'] = false;
    }

    // 处理其他必需字段
    if (jsonData['serviceId'] == null) {
      jsonData['serviceId'] = doc.id;
    }
    if (jsonData['storeId'] == null) {
      jsonData['storeId'] = 'unknown';
    }

    // 设置文档ID - 不设置id字段，因为它期望int类型，而doc.id是字符串
    // jsonData['id'] = doc.id; // 注释掉，让它保持null

    return BookableService.fromJson(jsonData);
  }

  /// 辅助方法：安全地将值转换为int
  static int _parseToInt(dynamic value, int defaultValue) {
    if (value == null) return defaultValue;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      final parsed = int.tryParse(value);
      return parsed ?? defaultValue;
    }
    return defaultValue;
  }

  /// 辅助方法：安全地将值转换为double
  static double _parseToDouble(dynamic value, double defaultValue) {
    if (value == null) return defaultValue;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      final parsed = double.tryParse(value);
      return parsed ?? defaultValue;
    }
    return defaultValue;
  }

  /// 转换为 Firestore 数据格式
  Map<String, dynamic> toFirestoreData() {
    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });
    
    return dbData;
  }

  /// 复制对象
  BookableService copyWith({
    String? storeId,
    String? serviceId,
    String? serviceName,
    ServiceCategory? serviceCategory,
    ServiceBreed? serviceBreed,
    String? description,
    StoreServiceStatus? status,
    List<String>? staffIds,
    int? minDuration,
    int? maxDuration,
    int? defaultDuration,
    double? basePrice,
    Currency? currency,
    int? maxCapacityPerSlot,
    bool? isOnlineBookingEnabled,
    bool? requiresApproval,
    String? cancellationPolicy,
    List<String>? servicePhotos,
    int? totalBookings,
    int? completedBookings,
    int? id,
    String? sid,
    String? name,
    bool? isValid,
    bool? isSynced,
    String? createdBy,
    int? createDate,
    String? updatedBy,
    int? updateDate,
    String? reviewedBy,
    int? reviewDate,
    String? notes,
    List<String>? tags,
  }) {
    return BookableService(
      storeId: storeId ?? this.storeId,
      serviceId: serviceId ?? this.serviceId,
      serviceName: serviceName ?? this.serviceName,
      serviceCategory: serviceCategory ?? this.serviceCategory,
      serviceBreed: serviceBreed ?? this.serviceBreed,
      description: description ?? this.description,
      status: status ?? this.status,
      staffIds: staffIds ?? this.staffIds,
      minDuration: minDuration ?? this.minDuration,
      maxDuration: maxDuration ?? this.maxDuration,
      defaultDuration: defaultDuration ?? this.defaultDuration,
      basePrice: basePrice ?? this.basePrice,
      currency: currency ?? this.currency,
      maxCapacityPerSlot: maxCapacityPerSlot ?? this.maxCapacityPerSlot,
      isOnlineBookingEnabled: isOnlineBookingEnabled ?? this.isOnlineBookingEnabled,
      requiresApproval: requiresApproval ?? this.requiresApproval,
      cancellationPolicy: cancellationPolicy ?? this.cancellationPolicy,
      servicePhotos: servicePhotos ?? this.servicePhotos,
      totalBookings: totalBookings ?? this.totalBookings,
      completedBookings: completedBookings ?? this.completedBookings,
      id: id ?? this.id,
      sid: sid ?? this.sid,
      name: name ?? this.name,
      isValid: isValid ?? this.isValid,
      isSynced: isSynced ?? this.isSynced,
      createdBy: createdBy ?? this.createdBy,
      createDate: createDate ?? this.createDate,
      updatedBy: updatedBy ?? this.updatedBy,
      updateDate: updateDate ?? this.updateDate,
      reviewedBy: reviewedBy ?? this.reviewedBy,
      reviewDate: reviewDate ?? this.reviewDate,
      notes: notes ?? this.notes,
      tags: tags ?? this.tags,
    );
  }

  /// 创建新的 BookableService 对象
  static BookableService create({
    required String storeId,
    required String serviceId,
    required String serviceName,
    required ServiceCategory serviceCategory,
    required ServiceBreed serviceBreed,
    String? description,
    StoreServiceStatus status = StoreServiceStatus.active,
    List<String> staffIds = const [],
    int minDuration = 15,
    int maxDuration = 120,
    int defaultDuration = 60,
    double basePrice = 0.0,
    Currency currency = Currency.cad,
    int maxCapacityPerSlot = 1,
    bool isOnlineBookingEnabled = true,
    bool requiresApproval = false,
    String? cancellationPolicy,
    List<String>? servicePhotos,
    int totalBookings = 0,
    int completedBookings = 0,
  }) {
    return BookableService(
      sid: uuid.v4(),
      storeId: storeId,
      serviceId: serviceId,
      serviceName: serviceName,
      serviceCategory: serviceCategory,
      serviceBreed: serviceBreed,
      description: description,
      status: status,
      staffIds: staffIds,
      minDuration: minDuration,
      maxDuration: maxDuration,
      defaultDuration: defaultDuration,
      basePrice: basePrice,
      currency: currency,
      maxCapacityPerSlot: maxCapacityPerSlot,
      isOnlineBookingEnabled: isOnlineBookingEnabled,
      requiresApproval: requiresApproval,
      cancellationPolicy: cancellationPolicy,
      servicePhotos: servicePhotos,
      totalBookings: totalBookings,
      completedBookings: completedBookings,
      isValid: true,
    );
  }

  /// 检查服务是否可用
  bool get isAvailable {
    return status == StoreServiceStatus.active && 
           isOnlineBookingEnabled && 
           staffIds.isNotEmpty;
  }

  /// 获取格式化的价格字符串
  String get formattedPrice {
    return '${currency.code} \$${basePrice.toStringAsFixed(2)}';
  }

  /// 获取格式化的时长字符串
  String get formattedDuration {
    if (minDuration == maxDuration) {
      return '${defaultDuration} min';
    } else {
      return '${minDuration}-${maxDuration} min';
    }
  }

  @override
  String toString() {
    return 'BookableService(serviceId: $serviceId, serviceName: $serviceName, storeId: $storeId)';
  }
}
