import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:onenata_app/views/page/profile/pet_setting_info_page.dart';

import 'views/page/auth/auth_pages_i.dart';
import 'views/page/home/<USER>';
import 'views/page/profile/profile_pages_i.dart';
import 'views/page/page_i.dart';
import 'views/page/store_detail_page.dart';
import 'views/page/reward/rewards_page.dart';
import 'views/page/appointment/upcoming_appointment_page.dart';
import 'views/page/test/store_selection_test_page.dart';

class GlobalRouters {

  static const rootPage = "/root";
  static const petDetailPage = "/petDetail";
  static const signUpPage = "/signUp";
  static const signUpEmailPage = "/signUpEmail";
  static const logInPage = "/logIn";
  static const logInEmailPage = "/logInEmail";
  static const selectPetPage = "/selectPet";
  static const celebrationPage = "/celebration";
  static const homeMapPage = "/homeMap";
  static const petProfilePage = "/petProfile";
  static const petSettingPage = "/petSetting";
  static const userSettingPage = "/userSetting";
  static const userSettingNamePage = "/userSettingName";
  static const userSettingAddressPage = "/userSettingAddress";
  static const userSettingEmailPage = "/userSettingEmail";
  static const userSettingPasswordPage = "/userSettingPassword";
  static const userSettingContactPage = "/userSettingContact";
  static const linkEmailPage = "/linkEmail";
  static const storeDetailPage = "/storeDetail";
  static const rewardsPage = "/rewards";
  static const upcomingAppointmentPage = "/upcoming_appointment";
  static const storeSelectionTestPage = "/store_selection_test";

  static List<GetPage> appRouter = [
    GetPage(name: rootPage, page: () => RootPage()),
    GetPage(name: signUpPage, page: () => SignUpPage()),
    GetPage(name: signUpEmailPage, page: () => SignUpEmailPage()),
    GetPage(name: logInPage, page: () => LogInPage()),
    GetPage(name: logInEmailPage, page: () => LogInEmailPage()),
    GetPage(name: selectPetPage, page: () => SelectPetPage()),
    GetPage(name: petDetailPage, page: () => PetDetailPage()),
    GetPage(name: homeMapPage, page: () => MapPage()),
    GetPage(name: celebrationPage, page: () => CelebrationPage()),
    GetPage(name: petProfilePage, page: () => PetProfilePage()),
    GetPage(name: petSettingPage, page: () => PetSettingInfoPage()),
    GetPage(name: userSettingPage, page: () => UserSettingPage()),
    GetPage(name: userSettingNamePage, page: () => UserSettingNamePage()),
    GetPage(name: userSettingAddressPage, page: () => UserSettingAddressPage()),
    GetPage(name: userSettingEmailPage, page: () => UserSettingEmailPage()),
    GetPage(name: userSettingPasswordPage, page: () => UserSettingPasswordPage()),
    GetPage(name: userSettingContactPage, page: () => UserSettingContactPage()),
    GetPage(name: linkEmailPage, page: () => LinkEmailPage()),
    GetPage(name: storeDetailPage, page: () => StoreDetailPage()),
    GetPage(name: rewardsPage, page: () => RewardsPage()),
    GetPage(name: upcomingAppointmentPage, page: () => UpcomingAppointmentPage()),
    GetPage(name: storeSelectionTestPage, page: () => StoreSelectionTestPage()),
  ];

  static void offUntilRootPage() {
    Get.offUntil(
      createRoute(RootPage()),
      (route) => route.settings.name == rootPage,
    );
  }

  static Route createRoute(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(-1.0, 0.0); // 修改为从左往右
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        final tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );
        final offsetAnimation = animation.drive(tween);

        return SlideTransition(
          position: offsetAnimation,
          child: child,
        );
      },
    );
  }
}
