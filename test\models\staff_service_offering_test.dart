import 'package:flutter_test/flutter_test.dart';
import 'package:onenata_app/models/models_i.dart';

void main() {
  group('StaffServiceOffering Model Tests', () {
    late StaffServiceOffering testService;

    setUp(() {
      testService = StaffServiceOffering(
        uid: 'test_user_id',
        storeId: 'test_store_id',
        serviceName: 'Dog Grooming',
        serviceCategory: 'grooming',
        serviceBreed: 'dog',
        serviceStatus: 'active',
        serviceDuration: 90,
        serviceAmount: 75.50,
        serviceAmountCurrency: 'CAD',
        serviceCount: 25,
        servicePhotos: ['photo1.jpg', 'photo2.jpg'],
        sid: 'test_service_id',
        isValid: true,
        isSynced: true,
      );
    });

    test('should create StaffServiceOffering with required fields', () {
      expect(testService.uid, equals('test_user_id'));
      expect(testService.storeId, equals('test_store_id'));
      expect(testService.serviceName, equals('Dog Grooming'));
      expect(testService.serviceCategory, equals('grooming'));
      expect(testService.serviceBreed, equals('dog'));
      expect(testService.serviceStatus, equals('active'));
      expect(testService.serviceDuration, equals(90));
      expect(testService.serviceAmount, equals(75.50));
      expect(testService.serviceAmountCurrency, equals('CAD'));
      expect(testService.serviceCount, equals(25));
      expect(testService.servicePhotos.length, equals(2));
    });

    test('should create with default values correctly', () {
      final defaultService = StaffServiceOffering(
        uid: 'test_uid',
        storeId: 'test_store_id',
        serviceName: 'Basic Service',
      );

      expect(defaultService.serviceCategory, equals('grooming'));
      expect(defaultService.serviceBreed, equals('dog'));
      expect(defaultService.serviceStatus, equals('active'));
      expect(defaultService.serviceDuration, equals(60));
      expect(defaultService.serviceAmount, equals(0.0));
      expect(defaultService.serviceAmountCurrency, equals('CAD'));
      expect(defaultService.serviceCount, equals(0));
      expect(defaultService.servicePhotos, isEmpty);
    });

    test('should check if service is active correctly', () {
      expect(testService.isActive(), isTrue);

      testService.serviceStatus = 'inactive';
      expect(testService.isActive(), isFalse);

      testService.serviceStatus = 'pending';
      expect(testService.isActive(), isFalse);

      testService.serviceStatus = 'active';
      expect(testService.isActive(), isTrue);
    });

    test('should get price display correctly', () {
      expect(testService.getPriceDisplay(), equals('\$75.50 CAD'));

      testService.serviceAmount = 100.0;
      testService.serviceAmountCurrency = 'USD';
      expect(testService.getPriceDisplay(), equals('\$100.00 USD'));

      testService.serviceAmount = 50.0;
      expect(testService.getPriceDisplay(), equals('\$50.00 USD'));
    });

    test('should get duration display correctly', () {
      // 90 minutes = 1h 30m
      expect(testService.getDurationDisplay(), equals('1h 30m'));

      // 60 minutes = 1h
      testService.serviceDuration = 60;
      expect(testService.getDurationDisplay(), equals('1h'));

      // 30 minutes = 30m
      testService.serviceDuration = 30;
      expect(testService.getDurationDisplay(), equals('30m'));

      // 120 minutes = 2h
      testService.serviceDuration = 120;
      expect(testService.getDurationDisplay(), equals('2h'));

      // 75 minutes = 1h 15m
      testService.serviceDuration = 75;
      expect(testService.getDurationDisplay(), equals('1h 15m'));

      // 0 minutes = 0m
      testService.serviceDuration = 0;
      expect(testService.getDurationDisplay(), equals('0m'));
    });

    test('should copy with new values correctly', () {
      final copied = testService.copyWith(
        serviceName: 'Cat Grooming',
        serviceBreed: 'cat',
        serviceAmount: 65.0,
        serviceDuration: 60,
      );

      expect(copied.uid, equals('test_user_id')); // unchanged
      expect(copied.storeId, equals('test_store_id')); // unchanged
      expect(copied.serviceName, equals('Cat Grooming'));
      expect(copied.serviceBreed, equals('cat'));
      expect(copied.serviceAmount, equals(65.0));
      expect(copied.serviceDuration, equals(60));
      expect(copied.serviceCategory, equals('grooming')); // unchanged
      expect(copied.serviceStatus, equals('active')); // unchanged
    });

    test('should convert to/from JSON correctly', () {
      final json = testService.toJson();
      final fromJson = StaffServiceOffering.fromJson(json);

      expect(fromJson.uid, equals(testService.uid));
      expect(fromJson.storeId, equals(testService.storeId));
      expect(fromJson.serviceName, equals(testService.serviceName));
      expect(fromJson.serviceCategory, equals(testService.serviceCategory));
      expect(fromJson.serviceBreed, equals(testService.serviceBreed));
      expect(fromJson.serviceStatus, equals(testService.serviceStatus));
      expect(fromJson.serviceDuration, equals(testService.serviceDuration));
      expect(fromJson.serviceAmount, equals(testService.serviceAmount));
      expect(fromJson.serviceAmountCurrency, equals(testService.serviceAmountCurrency));
      expect(fromJson.serviceCount, equals(testService.serviceCount));
      expect(fromJson.servicePhotos.length, equals(testService.servicePhotos.length));
    });

    test('should convert to Firestore data correctly', () {
      final firestoreData = testService.toFirestoreData();

      expect(firestoreData['uid'], equals('test_user_id'));
      expect(firestoreData['store_id'], equals('test_store_id'));
      expect(firestoreData['service_name'], equals('Dog Grooming'));
      expect(firestoreData['service_category'], equals('grooming'));
      expect(firestoreData['service_breed'], equals('dog'));
      expect(firestoreData['service_status'], equals('active'));
      expect(firestoreData['service_duration'], equals(90));
      expect(firestoreData['service_amount'], equals(75.50));
      expect(firestoreData['service_amount_currency'], equals('CAD'));
      expect(firestoreData['service_count'], equals(25));
      expect(firestoreData['service_photos'], isA<List>());
    });

    test('should have correct collection name', () {
      expect(StaffServiceOffering.collection, equals('employee-service'));
    });

    test('should handle edge cases in duration display', () {
      // Test very long duration
      testService.serviceDuration = 300; // 5 hours
      expect(testService.getDurationDisplay(), equals('5h'));

      // Test odd minutes
      testService.serviceDuration = 127; // 2h 7m
      expect(testService.getDurationDisplay(), equals('2h 7m'));

      // Test single minute
      testService.serviceDuration = 1;
      expect(testService.getDurationDisplay(), equals('1m'));
    });

    test('should handle edge cases in price display', () {
      // Test zero price
      testService.serviceAmount = 0.0;
      expect(testService.getPriceDisplay(), equals('\$0.00 CAD'));

      // Test very high price
      testService.serviceAmount = 999.99;
      expect(testService.getPriceDisplay(), equals('\$999.99 CAD'));

      // Test price with many decimal places (should round to 2)
      testService.serviceAmount = 75.999;
      expect(testService.getPriceDisplay(), equals('\$76.00 CAD'));
    });

    test('should handle empty service photos correctly', () {
      final serviceWithoutPhotos = StaffServiceOffering(
        uid: 'test_uid',
        storeId: 'test_store_id',
        serviceName: 'Basic Service',
        servicePhotos: [],
      );

      expect(serviceWithoutPhotos.servicePhotos, isEmpty);

      final json = serviceWithoutPhotos.toJson();
      final fromJson = StaffServiceOffering.fromJson(json);
      expect(fromJson.servicePhotos, isEmpty);
    });
  });
}
