import { PortalUserData } from '../models/portal-user';
import { 
  StoreAccount, 
  StoreInfo
} from '../models/store';
import { 
  UserType, 
  StoreStatus, 
  StoreVerifiedStatus,
  BusinessType,
  StoreRole,
  ServiceCategory,
  ServiceBreed,
  StoreServiceStatus,
  // Currency
} from '../models/types';
import { 
  Address,
  AddressValidationResult,
  GeoPlace
} from '../models/place';
import { FirestoreDocument } from '../firebase/services/firestore';
import { FirestoreService } from "../firebase/services";
import { v4 as uuidv4 } from 'uuid';
import { moveTempFilesToStore } from "../firebase/services";
import { ProgressCallback } from '../types/common';
import staffService from './staff_services';
import GoogleMapsService from './google_map_services';

// Store Service interfaces
export interface StoreServiceData {
  sid: string;
  serviceId: string;
  storeId: string;
  serviceCategory: ServiceCategory;
  serviceBreed: ServiceBreed;
  description?: string;
  status: StoreServiceStatus;
  // 提供该类别服务的staff IDs
  staffIds: string[];
  // 该服务的佣金比例 (0.1 = 10%)
  commission: number;
  // 是否允许在线预约
  isOnlineBookingEnabled: boolean;
  // 是否需要审批
  requiresApproval: boolean;
  // 取消政策
  cancellationPolicy?: string;
  // 服务照片
  servicePhotos: string[];
  // 总预约数
  totalBookings: number;
  // 已完成预约数
  completedBookings: number;
  // 创建信息
  created_by: string;
  create_date: Date;
  updated_by: string;
  update_date: Date;
  isValid: boolean;
  isSynced: boolean;
}

export interface CreateStoreServiceData {
  serviceCategory: ServiceCategory;
  serviceBreed: ServiceBreed;
  description?: string;
  staffIds: string[];
  commission: number;
  isOnlineBookingEnabled: boolean;
  requiresApproval?: boolean;
  cancellationPolicy?: string;
}

export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ServiceListResponse<T> extends ServiceResponse<T[]> {
  total?: number;
}

export interface CreateStoreData {
  storeName: string;
  businessType: BusinessType;
  description?: string;
  phone: string;
  email: string;
  website?: string;
  currentAddress: {
    addressLine1: string;
    addressLine2?: string;
    city: string;
    province: string;
    country: string;
    postCode: string;
  };
  services: {
    grooming: boolean;
    boarding: boolean;
    veterinary: boolean;
    training: boolean;
    retail: boolean;
  };
  businessHours?: {
    [key: string]: {
      open: string;
      close: string;
      closed: boolean;
    };
  };
  googlePlaceId?: string;
  avatarUrl?: string;
  storePhotos?: string[];
  // Address validation result from form step
  addressValidationResult?: AddressValidationResult;
  // Place data from Google Maps
  placeData?: GeoPlace;
}

// 使用联合类型来处理 Firestore 文档
export type StoreAccountDocument = StoreAccount & FirestoreDocument;
export type StoreInfoDocument = StoreInfo & FirestoreDocument;

export interface StoreListItem extends StoreInfoDocument {
  accountInfo: StoreAccountDocument;
}

class StoreService {
  private readonly STORE_ACCOUNT_COLLECTION = 'store-account';
  private readonly STORE_INFO_COLLECTION = 'store-info';
  private readonly PORTAL_USER_DATA_COLLECTION = 'portal-user-data';

  /**
   * Validate store address during form input
   * This method is called during the address entry step of store creation
   * @param address Address object from form input
   */
  async validateStoreAddress(address: Address): Promise<AddressValidationResult> {
    try {
      const validationResult = await GoogleMapsService.validateAddress(address);
      
      return validationResult;
    } catch (error) {
      console.error('Error validating store address:', error);
      return {
        isValid: false,
        hasGooglePlace: false,
        error: error instanceof Error ? error.message : 'Failed to validate address'
      };
    }
  }

  /**
   * Search for business places using Google Places autocomplete
   * This method is called when user types in the address search field
   * @param query Search query string
   */
  async searchBusinessPlaces(query: string): Promise<{
    success: boolean;
    data?: Array<{
      placeId: string;
      description: string;
      mainText: string;
      secondaryText: string;
    }>;
    error?: string;
  }> {
    try {
      const result = await GoogleMapsService.searchBusinessPlaces(query);
      return result;
    } catch (error) {
      console.error('Error searching business places:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to search business places'
      };
    }
  }

  /**
   * Get address details from a Google Place ID
   * This method is called when user selects a place from search results
   * @param placeId Google Place ID
   */
  async getAddressFromGooglePlace(placeId: string): Promise<{
    success: boolean;
    data?: {
      address: Address;
      placeId: string;
      formattedAddress: string;
      location?: { lat: number; lng: number };
      businessName?: string;
      validationResult: AddressValidationResult;
    };
    error?: string;
  }> {
    try {
      const placeResult = await GoogleMapsService.getAddressFromPlace(placeId);
      
      if (!placeResult.success || !placeResult.data) {
        return {
          success: false,
          error: placeResult.error || 'Failed to get place details'
        };
      }

      // Create validation result for the Google Place
      const validationResult: AddressValidationResult = {
        isValid: true,
        hasGooglePlace: true,
        googlePlaceId: placeResult.data.placeId,
        formattedAddress: placeResult.data.formattedAddress,
        location: placeResult.data.location,
        message: 'Address found in Google Maps'
      };

      return {
        success: true,
        data: {
          ...placeResult.data,
          validationResult
        }
      };
    } catch (error) {
      console.error('Error getting address from Google Place:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get address from Google Place'
      };
    }
  }

  /**
   * Create store with enhanced address handling
   * Supports both Google Places and manual address input
   */
  async createStoreWithSmartAddress(
    userData: PortalUserData, 
    storeData: CreateStoreData,
    addressSource: 'google_place' | 'manual',
    onProgress?: ProgressCallback
  ): Promise<{
    success: boolean;
    data?: string; // store ID
    error?: string;
  }> {
    // Use the existing createStore method with enhanced address validation
    return this.createStore(userData, storeData, onProgress);
  }

  /**
   * Check if user has permission to create stores
   */
  async canCreateStore(userDataSid: string): Promise<boolean> {
    try {
      const userDoc = await FirestoreService.getById(this.PORTAL_USER_DATA_COLLECTION, userDataSid);
      if (!userDoc.success || !userDoc.data) {
        return false;
      }

      const userType = userDoc.data.userType as UserType;
      return userType === UserType.PETSTORE_OWNER || 
             userType === UserType.ONENATA_ADMIN ||
             userType === UserType.PETSTORE_BUSINESS;
    } catch (error) {
      console.error('Error checking store creation permission:', error);
      return false;
    }
  }

  /**
   * Create new store with address validation and place creation
   * This process includes:
   * 1. Create store account and info
   * 2. Create GeoPlace and save to Firestore (only if store creation succeeds)
   */
  async createStore(userData: PortalUserData, storeData: CreateStoreData, onProgress?: ProgressCallback): Promise<{
    success: boolean;
    data?: string; // store ID
    error?: string;
  }> {
    const totalSteps = 9; // Increased steps for address validation and place creation
    let currentStep = 0;
    
    const updateProgress = (message: string) => {
      currentStep++;
      onProgress?.(currentStep, totalSteps, message);
    };

    let createdStoreId: string | null = null;
    let geoPlaceCreated = false;

    try {
      // Step 1: Check permissions
      if (!userData.sid) {
        return {
          success: false,
          error: 'User data not found'
        };
      }

      console.log('userData to create store:', userData);

      const canCreate = await this.canCreateStore(userData.sid || '');
      if (!canCreate) {
        return {
          success: false,
          error: 'User does not have permission to create stores'
        };
      }

      // Step 2: Validate address if not already validated
      updateProgress('Validating store address...');
      let addressValidationResult = storeData.addressValidationResult;
      
      if (!addressValidationResult) {
        addressValidationResult = await this.validateStoreAddress(storeData.currentAddress);
        
        if (!addressValidationResult.isValid) {
          return {
            success: false,
            error: `Address validation failed: ${addressValidationResult.error || 'Invalid address'}`
          };
        }
      }

      // Step 3: Generate store ID
      updateProgress('Generating store ID...');
      const storeId = uuidv4();
      createdStoreId = storeId;

      // Step 4: Create StoreAccount
      updateProgress('Creating store account...');
      const storeAccount = {
        sid: storeId,
        name: storeData.storeName,
        ownerId: userData.uid,
        storeName: storeData.storeName,
        storeVerifiedStatus: StoreVerifiedStatus.PENDING,
        storeStatus: StoreStatus.ACTIVE,
        googlePlaceId: addressValidationResult.googlePlaceId || '',
        isValid: true,
        isSynced: true,
        created_by: userData.uid,
        updated_by: userData.uid,
        tags: []
      };

      console.log('storeAccount to create store:', storeAccount); 

      // Step 5: Create StoreInfo
      updateProgress('Creating store info...');
      const storeInfoSid = uuidv4();

      const storeInfo = {
        sid: storeInfoSid,
        name: storeData.storeName,
        storeId: storeId,
        currentAddress: storeData.currentAddress,
        phone: storeData.phone,
        email: storeData.email,
        website: storeData.website,
        businessType: storeData.businessType,
        description: storeData.description,
        services: storeData.services,
        businessHours: storeData.businessHours,
        avatarUrl: storeData.avatarUrl,
        storePhotos: storeData.storePhotos,
        appointmentOpen: false,
        hasSpecialEvent: false,
        staffs: [userData.uid], // Store owner automatically becomes staff
        customerList: [],
        isValid: true,
        isSynced: true,
        created_by: userData.uid,
        updated_by: userData.uid,
        tags: []
      };

      // Step 6: Process temporary uploaded photos
      updateProgress('Processing temporary photos...');
      let finalAvatarUrl = storeData.avatarUrl;
      let finalStorePhotos = storeData.storePhotos;

      try {
        // Move avatar to permanent location
        if (storeData.avatarUrl && storeData.avatarUrl.includes('/temp/')) {
          const movedAvatars = await moveTempFilesToStore([storeData.avatarUrl], storeId, 'avatar');
          finalAvatarUrl = movedAvatars[0];
        }

        // Move store photos to permanent location
        if (storeData.storePhotos && storeData.storePhotos.length > 0) {
          const tempPhotos = storeData.storePhotos.filter(photo => photo.includes('/temp/'));
          const normalPhotos = storeData.storePhotos.filter(photo => !photo.includes('/temp/'));
          
          if (tempPhotos.length > 0) {
            const movedPhotos = await moveTempFilesToStore(tempPhotos, storeId, 'photos');
            finalStorePhotos = [...normalPhotos, ...movedPhotos];
          }
        }

        // Update storeInfo with final photo URLs
        storeInfo.avatarUrl = finalAvatarUrl;
        storeInfo.storePhotos = finalStorePhotos;
      } catch (error) {
        console.error('Error moving temp files:', error);
        // If moving fails, continue with original URLs
      }

      // Step 7: Save to database
      updateProgress('Creating store account...');
      const accountResult = await FirestoreService.createWithId(
        this.STORE_ACCOUNT_COLLECTION, 
        storeId, 
        storeAccount
      );

      if (!accountResult.success) {
        return {
          success: false,
          error: 'Failed to create store account'
        };
      }

      updateProgress('Creating store info...');
      const infoResult = await FirestoreService.createWithId(
        this.STORE_INFO_COLLECTION, 
        storeInfoSid, 
        storeInfo
      );

      if (!infoResult.success) {
        // If StoreInfo creation fails, delete the created StoreAccount
        await FirestoreService.delete(this.STORE_ACCOUNT_COLLECTION, storeId);
        return {
          success: false,
          error: 'Failed to create store info'
        };
      }

      // Step 8: Create staff info for store owner
      const staffData = {
        email: '',
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        phoneNumber: userData.phoneNumber || '',
        role: StoreRole.STORE_OWNER,
      };

      const updateStaffInfoResult = await staffService.createStaffInfoAndSchedule(
        userData.uid, 
        userData.sid, 
        storeId, 
        staffData, 
        userData.uid, 
        onProgress
      );

      if (!updateStaffInfoResult.success) {
        return {
          success: false,
          error: 'Failed to update staff info'
        };
      }

      updateProgress('User updated successfully');

      // Step 9: Create and save GeoPlace (only after successful store creation)
      updateProgress('Creating place information...');
      try {
        // Create GeoPlace object
        const geoPlaceResult = await GoogleMapsService.createGeoPlace(
          storeData.currentAddress,
          addressValidationResult,
          storeId
        );

        if (geoPlaceResult.success && geoPlaceResult.data) {
          // Save GeoPlace to Firestore
          const savePlaceResult = await GoogleMapsService.saveGeoPlaceToFirestore(geoPlaceResult.data);
          
          if (savePlaceResult.success) {
            geoPlaceCreated = true;
            console.log('GeoPlace created and saved successfully for store:', storeId);
          } else {
            console.warn('Failed to save GeoPlace to Firestore:', savePlaceResult.error);
            // Continue with store creation even if place creation fails
          }
        } else {
          console.warn('Failed to create GeoPlace:', geoPlaceResult.error);
          // Continue with store creation even if place creation fails
        }
      } catch (error) {
        console.error('Error creating GeoPlace:', error);
        // Continue with store creation even if place creation fails
      }

      updateProgress('Store created successfully');
      
      return {
        success: true,
        data: storeId
      };

    } catch (error) {
      console.error('Error creating store:', error);
      
      // Cleanup: If store creation failed after partial creation, clean up
      if (createdStoreId) {
        try {
          await FirestoreService.delete(this.STORE_ACCOUNT_COLLECTION, createdStoreId);
          await FirestoreService.delete(this.STORE_INFO_COLLECTION, createdStoreId);
          
          // Also clean up place if it was created
          if (geoPlaceCreated) {
            // Note: We would need a method to delete place by store ID
            // This is left for future implementation if needed
          }
        } catch (cleanupError) {
          console.error('Error during cleanup:', cleanupError);
        }
      }
      
      return {
        success: false,
        error: 'Failed to create store'
      };
    }
  }

  /**
   * 获取用户的所有店铺
   */
  async getUserStores(userId: string): Promise<{
    success: boolean;
    data?: StoreListItem[];
    error?: string;
  }> {
    console.log('getUserStores:  ', userId);
    try {
      // 获取用户拥有的店铺账户
      const accountsResult = await FirestoreService.getMany<FirestoreDocument>(
        this.STORE_ACCOUNT_COLLECTION,
        {
          where: [{ field: 'ownerId', operator: '==', value: userId }],
          orderBy: [{ field: 'createdAt', direction: 'desc' }]
        }
      );

      if (!accountsResult.success || !accountsResult.data) {
        return {
          success: false,
          data: [],
          error: 'No stores found'
        };
      }

      // 获取对应的店铺信息
      const storeList: StoreListItem[] = [];
      
      for (const account of accountsResult.data) {
        // 确保 docId 是字符串类型
        const storeId = (account as FirestoreDocument & { sid?: string }).sid || account.id;
        if (!storeId || typeof storeId !== 'string') {
          console.warn('Invalid store ID:', storeId);
          continue;
        }

        const infoResult = await FirestoreService.getDocByQueryField<FirestoreDocument>(
          this.STORE_INFO_COLLECTION, 
          'storeId',
          storeId
        );

        if (infoResult.success && infoResult.data) {
          storeList.push({
            ...infoResult.data,
            accountInfo: account
          } as StoreListItem);
        }
      }

      return {
        success: true,
        data: storeList
      };

    } catch (error) {
      console.error('Error getting user stores:', error);
      return {
        success: false,
        error: 'Failed to get user stores'
      };
    }
  }

  /**
   * 获取单个店铺详情
   */
  async getStoreDetails(storeId: string): Promise<{
    success: boolean;
    data?: StoreListItem;
    error?: string;
  }> {
    try {
      const accountResult = await FirestoreService.getById<FirestoreDocument>(
        this.STORE_ACCOUNT_COLLECTION, 
        storeId
      );

      const infoResult = await FirestoreService.getDocByQueryField<FirestoreDocument>(
        this.STORE_INFO_COLLECTION, 
        'storeId',
        storeId
      );

      if (!accountResult.success || !infoResult.success || 
          !accountResult.data || !infoResult.data) {
        return {
          success: false,
          error: 'Store not found'
        };
      }

      return {
        success: true,
        data: {
          ...infoResult.data,
          accountInfo: accountResult.data
        } as StoreListItem
      };

    } catch (error) {
      console.error('Error getting store details:', error);
      return {
        success: false,
        error: 'Failed to get store details'
      };
    }
  }





  /**
   * 更新店铺信息
   */
  async updateStore(storeId: string, updates: Partial<CreateStoreData>): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const accountUpdates: Record<string, unknown> = {};
      const infoUpdates: Record<string, unknown> = {};
      console.log('updates', updates);
      // 分离需要更新的字段
      if (updates.storeName) {
        accountUpdates.storeName = updates.storeName;
        accountUpdates.name = updates.storeName;
      }

      if (updates.googlePlaceId) {
        accountUpdates.googlePlaceId = updates.googlePlaceId;
      }

      // 其他字段更新到StoreInfo
      Object.keys(updates).forEach(key => {
        if (key !== 'storeName' && key !== 'googlePlaceId' && key !== 'placeData') {
          infoUpdates[key] = updates[key as keyof CreateStoreData];
        }
      });

      // 添加更新者信息
      accountUpdates.updatedBy = storeId;
      infoUpdates.updatedBy = storeId;

      // 更新数据库
      if (Object.keys(accountUpdates).length > 1) { // 除了updatedBy还有其他字段
        const accountResult = await FirestoreService.updateByQueryField(
          this.STORE_ACCOUNT_COLLECTION, 
          'sid', 
          storeId, 
          accountUpdates
        );
        
        if (!accountResult.success) {
          console.error('Failed to update store account:', accountResult.error);
          return {
            success: false,
            error: 'Failed to update store account'
          };
        }
      }

      if (Object.keys(infoUpdates).length > 1) { // 除了updatedBy还有其他字段
        const infoResult = await FirestoreService.updateByQueryField(
          this.STORE_INFO_COLLECTION, 
          'storeId', 
          storeId, 
          infoUpdates
        );
        
        if (!infoResult.success) {
          return {
            success: false,
            error: 'Failed to update store info'
          };
        }
      }

      // Handle place data separately if provided
      if (updates.placeData) {
        try {
          // Import PlaceService dynamically to avoid circular dependency
          const { default: PlaceService } = await import('./place_service');
          
          const placeResult = await PlaceService.createOrUpdatePlaceFromGoogle({
            placeId: updates.placeData.GMapPlaceId || '',
            name: updates.placeData.name || '',
            formattedAddress: updates.placeData.formattedAddress || '',
            location: updates.placeData.location || { lat: 0, lng: 0 },
            address: {
              addressLine1: updates.placeData.postalAddress?.addressLines?.[0] || '',
              addressLine2: updates.placeData.postalAddress?.addressLines?.[1] || '',
              city: updates.placeData.postalAddress?.locality || '',
              province: updates.placeData.postalAddress?.administrativeArea || '',
              country: updates.placeData.postalAddress?.regionCode || '',
              postCode: updates.placeData.postalAddress?.postalCode || ''
            },
            businessName: updates.placeData.name,
            website: updates.placeData.internationalPhoneNumber, // This should be website, but using phone for now
            phoneNumber: updates.placeData.internationalPhoneNumber,
            businessStatus: updates.placeData.businessStatus,
            rating: updates.placeData.rating,
            types: updates.placeData.types,
            openingHours: updates.placeData.regularOpeningHours
          }, storeId);

          if (!placeResult.success) {
            console.warn('Failed to update place data:', placeResult.error);
          }
        } catch (placeError) {
          console.error('Error updating place data:', placeError);
          // Don't fail the store update if place update fails
        }
      }

      return {
        success: true
      };

    } catch (error) {
      console.error('Error updating store:', error);
      return {
        success: false,
        error: 'Failed to update store'
      };
    }
  }

  /**
   * 删除店铺
   */
  async deleteStore(storeId: string, ownerId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // 验证所有权
      const storeResult = await this.getStoreDetails(storeId);
      if (!storeResult.success || !storeResult.data) {
        return {
          success: false,
          error: 'Store not found'
        };
      }

      if (storeResult.data.accountInfo.ownerId !== ownerId) {
        return {
          success: false,
          error: 'Unauthorized to delete this store'
        };
      }

      // 删除店铺信息和账户
      const infoDeleteResult = await FirestoreService.delete(
        this.STORE_INFO_COLLECTION, 
        storeId
      );

      const accountDeleteResult = await FirestoreService.delete(
        this.STORE_ACCOUNT_COLLECTION, 
        storeId
      );

      if (!infoDeleteResult.success || !accountDeleteResult.success) {
        return {
          success: false,
          error: 'Failed to delete store'
        };
      }

      return {
        success: true
      };

    } catch (error) {
      console.error('Error deleting store:', error);
      return {
        success: false,
        error: 'Failed to delete store'
      };
    }
  }

  /**
   * 获取店铺统计信息
   */
  async getStoreStats(userDataSid: string): Promise<{
    success: boolean;
    data?: {
      totalStores: number;
      activeStores: number;
      pendingStores: number;
      rejectedStores: number;
    };
    error?: string;
  }> {
    try {
      const storesResult = await this.getUserStores(userDataSid);
      
      if (!storesResult.success || !storesResult.data) {
        return {
          success: true,
          data: {
            totalStores: 0,
            activeStores: 0,
            pendingStores: 0,
            rejectedStores: 0
          }
        };
      }

      const stores = storesResult.data;
      const stats = {
        totalStores: stores.length,
        activeStores: stores.filter(s => s.accountInfo.storeVerifiedStatus === StoreVerifiedStatus.APPROVED).length,
        pendingStores: stores.filter(s => s.accountInfo.storeVerifiedStatus === StoreVerifiedStatus.PENDING).length,
        rejectedStores: stores.filter(s => s.accountInfo.storeVerifiedStatus === StoreVerifiedStatus.REJECTED).length
      };

      return {
        success: true,
        data: stats
      };

    } catch (error) {
      console.error('Error getting store stats:', error);
      return {
        success: false,
        error: 'Failed to get store statistics'
      };
    }
  }

  /**
   * 管理员获取所有店铺（用于审核）
   */
  async getAllStores(options?: {
    status?: StoreVerifiedStatus;
    limit?: number;
    offset?: number;
  }): Promise<{
    success: boolean;
    data?: StoreListItem[];
    total?: number;
    error?: string;
  }> {
    try {
      // 构建查询条件
      const queryOptions: {
        orderBy: Array<{ field: string; direction: 'asc' | 'desc' }>;
        where?: Array<{ field: string; operator: '==' | '!=' | '<' | '<=' | '>' | '>=' | 'in' | 'not-in' | 'array-contains' | 'array-contains-any'; value: unknown }>;
        limit?: number;
      } = {
        orderBy: [{ field: 'createdAt', direction: 'desc' }]
      };

      if (options?.status) {
        queryOptions.where = [{ field: 'storeVerifiedStatus', operator: '==', value: options.status }];
      }

      if (options?.limit) {
        queryOptions.limit = options.limit;
      }

      // 获取店铺账户
      const accountsResult = await FirestoreService.getMany<FirestoreDocument>(
        this.STORE_ACCOUNT_COLLECTION,
        queryOptions
      );

      if (!accountsResult.success || !accountsResult.data) {
        return {
          success: true,
          data: [],
          total: 0
        };
      }

      // 获取对应的店铺信息
      const storeList: StoreListItem[] = [];
      
      for (const account of accountsResult.data) {
        const storeId = (account as FirestoreDocument & { sid?: string }).sid || account.id;
        if (!storeId || typeof storeId !== 'string') {
          console.warn('Invalid store ID:', storeId);
          continue;
        }

        const infoResult = await FirestoreService.getDocByQueryField<FirestoreDocument>(
          this.STORE_INFO_COLLECTION, 
          'storeId',
          storeId
        );

        if (infoResult.success && infoResult.data) {
          storeList.push({
            ...infoResult.data,
            accountInfo: account
          } as StoreListItem);
        }
      }

      return {
        success: true,
        data: storeList,
        total: storeList.length
      };

    } catch (error) {
      console.error('Error getting all stores:', error);
      return {
        success: false,
        error: 'Failed to get all stores'
      };
    }
  }

  /**
   * 更新店铺审核状态（管理员专用）
   */
  async updateStoreVerificationStatus(
    storeId: string, 
    status: StoreVerifiedStatus,
    adminUserId: string,
    reason?: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const updates: Record<string, unknown> = {
        storeVerifiedStatus: status,
        updatedBy: adminUserId,
        updatedAt: new Date()
      };

      if (reason) {
        updates.verificationReason = reason;
      }

      const result = await FirestoreService.update(
        this.STORE_ACCOUNT_COLLECTION,
        storeId,
        updates
      );

      if (!result.success) {
        return {
          success: false,
          error: 'Failed to update store verification status'
        };
      }

      return {
        success: true
      };

    } catch (error) {
      console.error('Error updating store verification status:', error);
      return {
        success: false,
        error: 'Failed to update store verification status'
      };
    }
  }

  /**
   * 获取全局店铺统计（管理员专用）
   */
  async getAllStoresStats(): Promise<{
    success: boolean;
    data?: {
      totalStores: number;
      pendingStores: number;
      approvedStores: number;
      rejectedStores: number;
      totalUsers: number;
      notificationCount: number;
    };
    error?: string;
  }> {
    try {
      // 获取所有店铺账户
      const accountsResult = await FirestoreService.getMany<FirestoreDocument>(
        this.STORE_ACCOUNT_COLLECTION,
        {}
      );

      if (!accountsResult.success || !accountsResult.data) {
        return {
          success: true,
          data: {
            totalStores: 0,
            pendingStores: 0,
            approvedStores: 0,
            rejectedStores: 0,
            totalUsers: 0,
            notificationCount: 0
          }
        };
      }

      const stores = accountsResult.data;
      const stats = {
        totalStores: stores.length,
        pendingStores: stores.filter(s => s.storeVerifiedStatus === StoreVerifiedStatus.PENDING).length,
        approvedStores: stores.filter(s => s.storeVerifiedStatus === StoreVerifiedStatus.APPROVED).length,
        rejectedStores: stores.filter(s => s.storeVerifiedStatus === StoreVerifiedStatus.REJECTED).length,
        totalUsers: 0, // 这里可以后续添加用户统计逻辑
        notificationCount: 0 // 这里可以后续添加通知统计逻辑
      };

      return {
        success: true,
        data: stats
      };

    } catch (error) {
      console.error('Error getting all stores stats:', error);
      return {
        success: false,
        error: 'Failed to get all stores statistics'
      };
    }
  }

  /**
   * 获取店铺创建者的用户信息
   */
  async getStoreOwnerInfo(ownerId: string): Promise<{
    success: boolean;
    data?: {
      displayName: string;
      firstName: string;
      lastName: string;
      email: string;
      phoneNumber?: string;
      userType: string;
      photoURL?: string;
      createdAt: Date;
    };
    error?: string;
  }> {
    try {
      // 获取用户数据
      const userDataResult = await FirestoreService.getMany<FirestoreDocument>(
        this.PORTAL_USER_DATA_COLLECTION,
        {
          where: [{ field: 'uid', operator: '==', value: ownerId }],
          limit: 1
        }
      );

      if (!userDataResult.success || !userDataResult.data || userDataResult.data.length === 0) {
        return {
          success: false,
          error: 'User data not found'
        };
      }

      const userData = userDataResult.data[0];
      
      // 获取用户账户信息（邮箱等）
      const userAccountResult = await FirestoreService.getMany<FirestoreDocument>(
        'portal-user-account',
        {
          where: [{ field: 'sid', operator: '==', value: ownerId }],
          limit: 1
        }
      );

      const userAccount = userAccountResult.success && userAccountResult.data && userAccountResult.data.length > 0 
        ? userAccountResult.data[0] 
        : null;

      return {
        success: true,
        data: {
          displayName: (userData.displayName as string) || '',
          firstName: (userData.firstName as string) || '',
          lastName: (userData.lastName as string) || '',
          email: (userAccount?.email as string) || '',
          phoneNumber: (userData.phoneNumber as string) || (userAccount?.phoneNumber as string),
          userType: (userData.userType as string) || '',
          photoURL: userData.photoURL as string,
          createdAt: userData.createdAt ? new Date(userData.createdAt as unknown as string) : new Date()
        }
      };

    } catch (error) {
      console.error('Error getting store owner info:', error);
      return {
        success: false,
        error: 'Failed to get store owner information'
      };
    }
  }

  // ==================== Appointment Management ====================

  /**
   * Enable/Disable appointment functionality for store
   */
  async toggleAppointmentStatus(
    storeId: string,
    isOpen: boolean,
    updatedBy: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const result = await FirestoreService.updateByQueryField(
        this.STORE_INFO_COLLECTION,
        'storeId',
        storeId,
        {
          appointmentOpen: isOpen,
          updated_by: updatedBy,
          update_date: new Date()
        }
      );

      if (result.success) {
        return {
          success: true
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to update appointment status'
        };
      }

    } catch (error) {
      console.error('Error toggling appointment status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update appointment status'
      };
    }
  }

  /**
   * Check if store has appointment functionality enabled
   */
  async isAppointmentEnabled(storeId: string): Promise<{
    success: boolean;
    data?: boolean;
    error?: string;
  }> {
    try {
      const storeResult = await this.getStoreDetails(storeId);
      
      if (!storeResult.success || !storeResult.data) {
        return {
          success: false,
          error: 'Store not found'
        };
      }

      return {
        success: true,
        data: storeResult.data.appointmentOpen || false
      };

    } catch (error) {
      console.error('Error checking appointment status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to check appointment status'
      };
    }
  }

  /**
   * Get appointment system statistics for store
   */
  async getAppointmentStats(storeId: string): Promise<{
    success: boolean;
    data?: {
      isEnabled: boolean;
      totalAppointments: number;
      upcomingAppointments: number;
      completedAppointments: number;
      totalCustomers: number;
      activeServices: number;
      staffWithSchedules: number;
    };
    error?: string;
  }> {
    try {
      // Check if appointments are enabled
      const enabledResult = await this.isAppointmentEnabled(storeId);
      const isEnabled = enabledResult.success && enabledResult.data;

      if (!isEnabled) {
        return {
          success: true,
          data: {
            isEnabled: false,
            totalAppointments: 0,
            upcomingAppointments: 0,
            completedAppointments: 0,
            totalCustomers: 0,
            activeServices: 0,
            staffWithSchedules: 0
          }
        };
      }

      // Import appointment service to get stats
      const { default: appointmentService } = await import('./appointment_service');

      // Get appointment statistics
      const appointmentsList = await appointmentService.getStoreAppointments(storeId);
      const appointments = appointmentsList.data || [];

      const upcomingAppointments = appointments.filter(apt => 
        apt.status === 'confirmed' || apt.status === 'draft'
      ).length;

      const completedAppointments = appointments.filter(apt => 
        apt.status === 'completed'
      ).length;

      // Get bookable services
      const servicesResult = await this.getStoreServices(storeId);
      const activeServices = servicesResult.data?.filter(service => 
        service.status === StoreServiceStatus.ACTIVE && 
        service.isOnlineBookingEnabled
      ).length || 0;

      // Get customers
      const { default: customerService } = await import('./customer_service');
      const customersResult = await customerService.getCustomersByStore(storeId);
      const totalCustomers = customersResult.data?.length || 0;

      // Get staff with schedules (simplified count)
      const { default: staffService } = await import('./staff_services');
      const staffResult = await staffService.getStoreStaff(storeId);
      const staffWithSchedules = staffResult.data?.filter(staff => 
        staff.schedule && staff.schedule.active
      ).length || 0;

      return {
        success: true,
        data: {
          isEnabled: true,
          totalAppointments: appointments.length,
          upcomingAppointments,
          completedAppointments,
          totalCustomers,
          activeServices,
          staffWithSchedules
        }
      };

    } catch (error) {
      console.error('Error getting appointment stats:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get appointment statistics'
      };
    }
  }

  /**
   * 管理员修改店铺基本信息
   */
  async updateStoreInfoByAdmin(
    storeId: string,
    updates: {
      name?: string;
      phone?: string;
      email?: string;
      description?: string;
      businessType?: string;
      currentAddress?: {
        addressLine1: string;
        addressLine2?: string;
        city: string;
        province: string;
        country: string;
        postCode: string;
      };
      services?: {
        grooming: boolean;
        boarding: boolean;
        veterinary: boolean;
        training: boolean;
        retail: boolean;
      };
    },
    adminUserId: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // 获取店铺信息文档ID
      const infoResult = await FirestoreService.getDocByQueryField<FirestoreDocument>(
        this.STORE_INFO_COLLECTION,
        'storeId',
        storeId
      );

      if (!infoResult.success || !infoResult.data) {
        return {
          success: false,
          error: 'Store info not found'
        };
      }

      const infoDocId = infoResult.data.id;
      if (!infoDocId) {
        return {
          success: false,
          error: 'Store info document ID not found'
        };
      }

      // 准备更新数据
      const updateData: Record<string, unknown> = {
        ...updates,
        updatedBy: adminUserId,
        updatedAt: new Date()
      };

      // 如果更新了店铺名称，同时更新store-account中的name字段
      if (updates.name) {
        const accountResult = await FirestoreService.update(
          this.STORE_ACCOUNT_COLLECTION,
          storeId,
          {
            name: updates.name,
            storeName: updates.name,
            updatedBy: adminUserId,
            updatedAt: new Date()
          }
        );

        if (!accountResult.success) {
          return {
            success: false,
            error: 'Failed to update store account'
          };
        }
      }

      // 更新店铺信息
      const result = await FirestoreService.update(
        this.STORE_INFO_COLLECTION,
        infoDocId,
        updateData
      );

      if (!result.success) {
        return {
          success: false,
          error: 'Failed to update store info'
        };
      }

      return {
        success: true
      };

    } catch (error) {
      console.error('Error updating store info by admin:', error);
      return {
        success: false,
        error: 'Failed to update store information'
      };
    }
  }

  /**
   * 让店铺歇业/恢复营业
   */
  async toggleStoreStatus(
    storeId: string,
    status: StoreStatus,
    adminUserId: string,
    reason?: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const updates: Record<string, unknown> = {
        storeStatus: status,
        updatedBy: adminUserId,
        updatedAt: new Date()
      };

      if (reason) {
        updates.statusChangeReason = reason;
      }

      const result = await FirestoreService.update(
        this.STORE_ACCOUNT_COLLECTION,
        storeId,
        updates
      );

      if (!result.success) {
        return {
          success: false,
          error: 'Failed to update store status'
        };
      }

      return {
        success: true
      };

    } catch (error) {
      console.error('Error toggling store status:', error);
      return {
        success: false,
        error: 'Failed to update store status'
      };
    }
  }

  /**
   * 获取通知数量统计
   */
  async getNotificationStats(): Promise<{
    success: boolean;
    data?: {
      totalNotifications: number;
      unreadNotifications: number;
      todayNotifications: number;
      notificationTypes: {
        store_approval: number;
        order: number;
        review: number;
        system: number;
      };
    };
    error?: string;
  }> {
    try {
      // 获取所有通知
      const notificationsResult = await FirestoreService.getMany<FirestoreDocument>(
        'notifications',
        {}
      );

      if (!notificationsResult.success || !notificationsResult.data) {
        return {
          success: true,
          data: {
            totalNotifications: 0,
            unreadNotifications: 0,
            todayNotifications: 0,
            notificationTypes: {
              store_approval: 0,
              order: 0,
              review: 0,
              system: 0
            }
          }
        };
      }

      const notifications = notificationsResult.data;
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const stats = {
        totalNotifications: notifications.length,
        unreadNotifications: notifications.filter(n => !n.read).length,
        todayNotifications: notifications.filter(n => {
          const createdAt = n.createdAt ? new Date(n.createdAt as unknown as string) : new Date();
          return createdAt >= today;
        }).length,
        notificationTypes: {
          store_approval: notifications.filter(n => n.type === 'store_approval').length,
          order: notifications.filter(n => n.type === 'order').length,
          review: notifications.filter(n => n.type === 'review').length,
          system: notifications.filter(n => n.type === 'system').length
        }
      };

      return {
        success: true,
        data: stats
      };

    } catch (error) {
      console.error('Error getting notification stats:', error);
      return {
        success: false,
        error: 'Failed to get notification statistics'
      };
    }
  }

  /**
   * 获取系统概览统计（管理员专用）
   */
  async getSystemOverviewStats(): Promise<{
    success: boolean;
    data?: {
      totalUsers: number;
      totalStores: number;
      totalOrders: number;
      totalRevenue: number;
      activeUsers: number;
      systemHealth: 'good' | 'warning' | 'critical';
    };
    error?: string;
  }> {
    try {
      // 获取用户统计
      const usersResult = await FirestoreService.getMany<FirestoreDocument>(
        this.PORTAL_USER_DATA_COLLECTION,
        {}
      );

      // 获取店铺统计
      const storesResult = await FirestoreService.getMany<FirestoreDocument>(
        this.STORE_ACCOUNT_COLLECTION,
        {}
      );

      const stats = {
        totalUsers: usersResult.success ? usersResult.data?.length || 0 : 0,
        totalStores: storesResult.success ? storesResult.data?.length || 0 : 0,
        totalOrders: 0, // 这里可以后续添加订单统计
        totalRevenue: 0, // 这里可以后续添加收入统计
        activeUsers: 0, // 这里可以后续添加活跃用户统计
        systemHealth: 'good' as const // 这里可以后续添加系统健康检查
      };

      return {
        success: true,
        data: stats
      };

    } catch (error) {
      console.error('Error getting system overview stats:', error);
      return {
        success: false,
        error: 'Failed to get system overview statistics'
      };
    }
  }

  /**
   * 批量操作店铺
   */
  async batchUpdateStores(
    storeIds: string[],
    updates: {
      storeVerifiedStatus?: StoreVerifiedStatus;
      storeStatus?: StoreStatus;
      verificationReason?: string;
    },
    adminUserId: string
  ): Promise<{
    success: boolean;
    results: Array<{
      storeId: string;
      success: boolean;
      error?: string;
    }>;
    error?: string;
  }> {
    try {
      const results = [];

      for (const storeId of storeIds) {
        try {
          const updateData: Record<string, unknown> = {
            ...updates,
            updatedBy: adminUserId,
            updatedAt: new Date()
          };

          const result = await FirestoreService.update(
            this.STORE_ACCOUNT_COLLECTION,
            storeId,
            updateData
          );

          results.push({
            storeId,
            success: result.success,
            error: result.error
          });
        } catch {
          results.push({
            storeId,
            success: false,
            error: 'Failed to update store'
          });
        }
      }

      return {
        success: true,
        results
      };

    } catch (error) {
      console.error('Error batch updating stores:', error);
      return {
        success: false,
        results: [],
        error: 'Failed to batch update stores'
      };
    }
  }

  /**
   * 获取店铺审核历史
   */
  async getStoreVerificationHistory(storeId: string): Promise<{
    success: boolean;
    data?: Array<{
      status: StoreVerifiedStatus;
      reason?: string;
      updatedBy: string;
      updatedAt: Date;
    }>;
    error?: string;
  }> {
    try {
      console.log('getStoreVerificationHistory', storeId);
      // Implementation for getting store verification history
      return {
        success: true,
        data: []
      };
    } catch (error) {
      console.error('Error getting store verification history:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get verification history'
      };
    }
  }

  // ==================== Store Service Management ====================

  /**
   * Create store service
   */
  async createStoreService(
    storeId: string,
    serviceData: CreateStoreServiceData,
    createdBy: string
  ): Promise<ServiceResponse<string>> {
    try {
      // Validate commission
      if (serviceData.commission < 0 || serviceData.commission > 1) {
        return {
          success: false,
          error: 'Commission must be between 0 and 1 (0% to 100%)'
        };
      }

      // Validate staffIds
      if (serviceData.staffIds.length === 0) {
        return {
          success: false,
          error: 'At least one staff member must be selected'
        };
      }

      const serviceId = uuidv4();
      const service: Partial<StoreServiceData> = {
        sid: serviceId,
        serviceId,
        storeId,
        serviceCategory: serviceData.serviceCategory,
        serviceBreed: serviceData.serviceBreed,
        description: serviceData.description,
        status: StoreServiceStatus.INACTIVE, // Start as inactive, requires manual activation
        staffIds: serviceData.staffIds,
        commission: serviceData.commission,
        isOnlineBookingEnabled: serviceData.isOnlineBookingEnabled,
        requiresApproval: serviceData.requiresApproval || false,
        cancellationPolicy: serviceData.cancellationPolicy || '',
        servicePhotos: [],
        totalBookings: 0,
        completedBookings: 0,
        created_by: createdBy,
        create_date: new Date(),
        updated_by: createdBy,
        update_date: new Date(),
        isValid: true,
        isSynced: true
      };


      console.log('start create store service', service);
      

      const storeInfoResult = await FirestoreService.getDocByQueryField(
        this.STORE_INFO_COLLECTION,
        'storeId',
        storeId
      );
      
      const storeInfoId = storeInfoResult.data?.sid;



      const result = await FirestoreService.createWithId(
        `${this.STORE_INFO_COLLECTION}/${storeInfoId}/store-services`,
        serviceId,
        service
      );

      if (result.success) {
        return {
          success: true,
          data: serviceId,
          message: 'Store service created successfully'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to create store service'
        };
      }
    } catch (error) {
      console.error('Create store service error:', error);
      return {
        success: false,
        error: 'Failed to create store service'
      };
    }
  }

  /**
   * Get store services
   */
  async getStoreServices(storeId: string): Promise<ServiceListResponse<StoreServiceData>> {
    try {

      const storeInfoResult = await FirestoreService.getDocByQueryField(
        this.STORE_INFO_COLLECTION,
        'storeId',
        storeId
      );

      const storeInfoId = storeInfoResult.data?.sid;

      const servicesResult = await FirestoreService.getMany<FirestoreDocument>(
        `${this.STORE_INFO_COLLECTION}/${storeInfoId}/store-services`,
        {
          orderBy: [{ field: 'create_date', direction: 'desc' }]
        }
      );

      console.log('servicesResult', servicesResult);

      if (servicesResult.success && servicesResult.data) {
        return {
          success: true,
          data: servicesResult.data as unknown as StoreServiceData[],
          total: servicesResult.data.length,
          message: 'Store services retrieved successfully'
        };
      }

      return {
        success: false,
        error: servicesResult.error || 'Failed to get store services'
      };

    } catch (error) {
      console.error('Error getting store services:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get store services'
      };
    }
  }

  /**
   * Get store service by ID
   */
  async getStoreServiceById(storeId: string, serviceId: string): Promise<ServiceResponse<StoreServiceData>> {
    try {
      const storeInfoResult = await FirestoreService.getDocByQueryField(
        this.STORE_INFO_COLLECTION,
        'storeId',
        storeId
      );

      const storeInfoId = storeInfoResult.data?.sid;

      const serviceResult = await FirestoreService.getById<FirestoreDocument>(
        `${this.STORE_INFO_COLLECTION}/${storeInfoId}/store-services`,
        serviceId
      );

      if (serviceResult.success && serviceResult.data) {
        return {
          success: true,
          data: serviceResult.data as unknown as StoreServiceData,
          message: 'Store service retrieved successfully'
        };
      }

      return {
        success: false,
        error: serviceResult.error || 'Service not found'
      };

    } catch (error) {
      console.error('Error getting store service by ID:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get store service'
      };
    }
  }

  /**
   * Update store service booking statistics
   */
  async updateStoreServiceBookingStats(
    storeId: string,
    serviceId: string,
    incrementBookings: boolean = true,
    incrementCompleted: boolean = false,
    updatedBy: string
  ): Promise<ServiceResponse<void>> {
    try {
      const storeInfoResult = await FirestoreService.getDocByQueryField(
        this.STORE_INFO_COLLECTION,
        'storeId',
        storeId
      );

      const storeInfoId = storeInfoResult.data?.sid;

      // Get current service data
      const serviceResult = await FirestoreService.getById<FirestoreDocument>(
        `${this.STORE_INFO_COLLECTION}/${storeInfoId}/store-services`,
        serviceId
      );

      if (!serviceResult.success || !serviceResult.data) {
        return {
          success: false,
          error: 'Service not found'
        };
      }

      const currentService = serviceResult.data as unknown as StoreServiceData;
      const updateData: Record<string, unknown> = {
        updated_by: updatedBy,
        update_date: new Date()
      };

      if (incrementBookings) {
        updateData.totalBookings = (currentService.totalBookings || 0) + 1;
      }

      if (incrementCompleted) {
        updateData.completedBookings = (currentService.completedBookings || 0) + 1;
      }

      const result = await FirestoreService.update(
        `${this.STORE_INFO_COLLECTION}/${storeInfoId}/store-services`,
        serviceId,
        updateData
      );

      if (result.success) {
        return {
          success: true,
          message: 'Store service statistics updated successfully'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to update store service statistics'
        };
      }

    } catch (error) {
      console.error('Error updating store service booking stats:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update store service statistics'
      };
    }
  }

  /**
   * Update store service
   */
  async updateStoreService(
    storeId: string,
    serviceId: string,
    updates: Partial<CreateStoreServiceData>,
    updatedBy: string
  ): Promise<ServiceResponse<void>> {
    try {
      const updateData = {
        ...updates,
        updated_by: updatedBy,
        update_date: new Date()
      };

      const storeInfoResult = await FirestoreService.getDocByQueryField(

        this.STORE_INFO_COLLECTION,
        'storeId',
        storeId
      );

      const storeInfoId = storeInfoResult.data?.sid;

      const result = await FirestoreService.update( 
        `${this.STORE_INFO_COLLECTION}/${storeInfoId}/store-services`,
        serviceId,
        updateData
      );

      if (result.success) {
        return {
          success: true,
          message: 'Store service updated successfully'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to update store service'
        };
      }

    } catch (error) {
      console.error('Error updating store service:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update store service'
      };
    }
  }

  /**
   * Delete store service
   */
  async deleteStoreService(
    storeId: string,
    serviceId: string
  ): Promise<ServiceResponse<void>> {
    try {

      const storeInfoResult = await FirestoreService.getDocByQueryField(

        this.STORE_INFO_COLLECTION,
        'storeId',
        storeId
      );

      const storeInfoId = storeInfoResult.data?.sid;

      const result = await FirestoreService.delete(
        `${this.STORE_INFO_COLLECTION}/${storeInfoId}/store-services`,
        serviceId
      );

      if (result.success) {
        return {
          success: true,
          message: 'Store service deleted successfully'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to delete store service'
        };
      }

    } catch (error) {
      console.error('Error deleting store service:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete store service'
      };
    }
  }

  /**
   * Toggle store service status
   */
  async toggleStoreServiceStatus(
    storeId: string,
    serviceId: string,
    isActive: boolean,
    updatedBy: string
  ): Promise<ServiceResponse<void>> {
    try {
      const status = isActive ? StoreServiceStatus.ACTIVE : StoreServiceStatus.INACTIVE;
      
      const storeInfoResult = await FirestoreService.getDocByQueryField(

        this.STORE_INFO_COLLECTION,
        'storeId',
        storeId
      );

      const storeInfoId = storeInfoResult.data?.sid;

      const result = await FirestoreService.update(
        `${this.STORE_INFO_COLLECTION}/${storeInfoId}/store-services`,
        serviceId,
        {
          status,
          updated_by: updatedBy,
          update_date: new Date()
        }
      );

      if (result.success) {
        return {
          success: true,
          message: `Store service ${isActive ? 'activated' : 'deactivated'} successfully`
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to toggle store service status'
        };
      }

    } catch (error) {
      console.error('Error toggling store service status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to toggle store service status'
      };
    }
  }

  /**
   * Sync staff services to store services
   * This method creates or updates store services based on staff services
   */
  async syncStaffServicesToStoreServices(
    storeId: string,
    staffId: string,
    staffServiceId: string,
    updatedBy: string
  ): Promise<ServiceResponse<string>> {
    try {
      // Get staff member

      console.log('start sync staff services to store services', storeId, staffId, staffServiceId, updatedBy);

      const storeInfoResult = await FirestoreService.getDocByQueryField(

        this.STORE_INFO_COLLECTION,
        'storeId',
        storeId
      );

      const storeInfoId = storeInfoResult.data?.sid;

      const staffResult = await staffService.getStaffMember(staffId, storeId);
      if (!staffResult.success || !staffResult.data) {
        return {
          success: false,
          error: 'Staff member not found'
        };
      }

      const staffMember = staffResult.data;

      // Get staff service
      const staffServiceResult = await staffService.getStaffServiceById(staffMember, staffServiceId);
      if (!staffServiceResult.success || !staffServiceResult.data) {
        return {
          success: false,
          error: 'Staff service not found'
        };
      }

      const staffServiceData = staffServiceResult.data;

      console.log('staffServiceData', staffServiceData);

      // Check if store service already exists for this category and breed
      const existingServicesResult = await this.getStoreServices(storeInfoId as string);
      if (existingServicesResult.success && existingServicesResult.data) {
        const existingService = existingServicesResult.data.find(service => 
          service.serviceCategory === staffServiceData.serviceCategory &&
          service.serviceBreed === staffServiceData.serviceBreed
        );

        if (existingService) {
          // Update existing service to include this staff
          if (!existingService.staffIds.includes(staffId)) {
            const updatedStaffIds = [...existingService.staffIds, staffId];
            await this.updateStoreService(
              storeId,
              existingService.serviceId,
              { staffIds: updatedStaffIds },
              updatedBy
            );
          }
          return {
            success: true,
            data: existingService.serviceId,
            message: 'Staff added to existing store service'
          };
        }
      }

      // Create new store service
      const storeServiceData: CreateStoreServiceData = {
        serviceCategory: staffServiceData.serviceCategory,
        serviceBreed: staffServiceData.serviceBreed,
        description: staffServiceData.description,
        staffIds: [staffId],
        commission: staffServiceData.serviceAmount, // Assuming commission is the service amount
        isOnlineBookingEnabled: false, // Start disabled
        requiresApproval: false
      };

      const createResult = await this.createStoreService(storeId, storeServiceData, updatedBy);
      
      if (createResult.success) {
        return {
          success: true,
          data: createResult.data!,
          message: 'New store service created from staff service'
        };
      } else {
        return {
          success: false,
          error: createResult.error || 'Failed to create store service'
        };
      }

    } catch (error) {
      console.error('Error syncing staff service to store service:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to sync staff service to store service'
      };
    }
  }
}






export const storeService = new StoreService();

export default new StoreService(); 
