import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'base_full_model.dart';

part 'store_account.g.dart';

/// Store Account - 店铺账户
/// 存储在 Firestore collection "store-account"
@JsonSerializable()
class StoreAccount extends BaseFullModel {
  String ownerId;                    // 店主 ID
  String storeName;                  // 店铺名称
  StoreVerifiedStatus? storeVerifiedStatus; // 店铺验证状态
  StoreStatus storeStatus;           // 店铺状态
  String googlePlaceId;              // Google Place ID
  String? salt;                      // 密码盐值
  String? hashedCredential;          // 店铺密码哈希

  StoreAccount({
    required this.ownerId,
    required this.storeName,
    this.storeVerifiedStatus,
    required this.storeStatus,
    required this.googlePlaceId,
    this.salt,
    this.hashedCredential,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  /// 从 Firestore 文档创建 StoreAccount 对象
  factory StoreAccount.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    
    // 转换snake_case到camelCase
    final Map<String, dynamic> jsonData = {};
    data.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });
    
    // 设置文档ID
    jsonData['sid'] = doc.id;
    
    return StoreAccount.fromJson(jsonData);
  }

  factory StoreAccount.fromJson(Map<String, dynamic> json) =>
      _$StoreAccountFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$StoreAccountToJson(this);

  Map<String, dynamic> toFirestoreData() {
    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });
    return dbData;
  }

  /// 复制对象
  StoreAccount copyWith({
    String? ownerId,
    String? storeName,
    StoreVerifiedStatus? storeVerifiedStatus,
    StoreStatus? storeStatus,
    String? googlePlaceId,
    String? salt,
    String? hashedCredential,
    int? id,
    String? sid,
    String? name,
    bool? isValid,
    bool? isSynced,
    String? createdBy,
    int? createDate,
    String? updatedBy,
    int? updateDate,
    String? reviewedBy,
    int? reviewDate,
    String? notes,
    List<String>? tags,
  }) {
    return StoreAccount(
      ownerId: ownerId ?? this.ownerId,
      storeName: storeName ?? this.storeName,
      storeVerifiedStatus: storeVerifiedStatus ?? this.storeVerifiedStatus,
      storeStatus: storeStatus ?? this.storeStatus,
      googlePlaceId: googlePlaceId ?? this.googlePlaceId,
      salt: salt ?? this.salt,
      hashedCredential: hashedCredential ?? this.hashedCredential,
      id: id ?? this.id,
      sid: sid ?? this.sid,
      name: name ?? this.name,
      isValid: isValid ?? this.isValid,
      isSynced: isSynced ?? this.isSynced,
      createdBy: createdBy ?? this.createdBy,
      createDate: createDate ?? this.createDate,
      updatedBy: updatedBy ?? this.updatedBy,
      updateDate: updateDate ?? this.updateDate,
      reviewedBy: reviewedBy ?? this.reviewedBy,
      reviewDate: reviewDate ?? this.reviewDate,
      notes: notes ?? this.notes,
      tags: tags ?? this.tags,
    );
  }

  static String get collection => 'store-account';

  /// 检查店铺是否已验证
  bool get isVerified => storeVerifiedStatus == StoreVerifiedStatus.approved;

  /// 检查店铺是否激活
  bool get isActive => storeStatus == StoreStatus.active;

  /// 检查店铺是否可以接受预约
  bool get canAcceptAppointments => isActive && isVerified;
}
