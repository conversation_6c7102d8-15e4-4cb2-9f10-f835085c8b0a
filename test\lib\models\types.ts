/**
 * 通用类型定义和枚举
 */

import { CustomerSource } from './customer';

// 时间戳类型
export type Timestamp = number | Date;

// 地址接口
export interface Address {
  addressLine1: string;
  addressLine2?: string;
  addressLine3?: string;
  city: string;
  province: string;
  country: string;
  postCode: string;
}

// 地理位置点
export interface GeoFirePoint {
  latitude: number;
  longitude: number;
  geohash?: string;
}

// 地理邮政地址
export interface GeoPostalAddress extends Address {
  coordinates?: GeoFirePoint;
}

// 设备信息
export interface Device {
  deviceId: string;
  deviceType: string;
  deviceModel?: string;
  batteryLevel?: number;
  lastSeen?: Timestamp;
  isActive: boolean;
}

// 工作时间
export interface WorkTime {
  weekday: 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | 'Sunday';
  startTime: string; // HH:MM 格式
  endTime: string;   // HH:MM 格式
  open: boolean;
}

// 营业时间
export interface BusinessHours {
  [key: string]: {
    open: string;
    close: string;
    closed: boolean;
  };
}

// 服务类型
export interface Services {
  grooming: boolean;
  boarding: boolean;
  veterinary: boolean;
  training: boolean;
  retail: boolean;
}

// 产品属性
export interface ProductAttributes {
  color?: string;
  size?: string;
  weight?: string;
  material?: string;
  [key: string]: string | undefined;
}

// 支付方式详情
export interface PaymentMethodDetails {
  type: 'stripe' | 'paypal' | 'cash' | 'pos' | 'bank_transfer';
  provider: string;
  providerPaymentId?: string;
  card?: {
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
  };
  bank?: {
    accountNumber: string;
    routingNumber: string;
    bankName: string;
  };
  details?: Record<string, unknown>;
}

// 订单商品项
export interface OrderItem {
  productId: string;
  name: string;
  quantity: number;
  price: number;
  subtotal: number;
}

// 用户偏好设置
export interface UserPreferences {
  notificationsEnabled?: boolean;
  language?: string;
  theme?: 'light' | 'dark' | 'system';
  emailNotifications?: boolean;
  smsNotifications?: boolean;
}

// 枚举定义
export enum UserType {
  ONENATA_ADMIN = '101',
  ONENATA_STAFF = '102',
  PETSTORE_BUSINESS = '201',
  PETSTORE_STAFF = '202',
  PETSTORE_OWNER = '203',
  PETSTORE_ADMIN = '204',
  PETSTORE_CUSTOMER_FROM_PORTAL = '205'
}

export enum AuthChannel {
  EMAIL = 'email',
  PHONE = 'phone',
  GOOGLE = 'google',
  APPLE = 'apple',
  FACEBOOK = 'facebook'
}

export enum PetGender {
  MALE = 'male',
  FEMALE = 'female',
  UNKNOWN = 'unknown'
}

export enum PetType {
  DOG = 'dog',
  CAT = 'cat',
  BIRD = 'bird',
  FISH = 'fish',
  RABBIT = 'rabbit',
  HAMSTER = 'hamster',
  OTHER = 'other'
}

export enum PetVisibility {
  PUBLIC = 'public',
  FRIENDS = 'friends',
  PRIVATE = 'private'
}

export enum PublishVisibility {
  PUBLIC = 'public',
  FRIENDS = 'friends',
  PRIVATE = 'private'
}

export enum StoreRole {
  STORE_OWNER = 'store-owner',
  STORE_ADMIN = 'store-admin',
  STORE_STAFF = 'store-staff'
}

export enum StoreVerifiedStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export enum StoreStatus {
  ACTIVE = 'active',
  TEMP_CLOSED = 'temp-closed',
  DEACTIVATED = 'deactivated'
}

export enum BusinessType {
  ONLINE_STORE = 'online_store',
  FAMILY_BASED_BUSINESS = 'family_based_business',
  RETAIL_BUSINESS = 'retail_business',
  COMMERCIAL_BUSINESS = 'commercial_business',
  PET_STORE = 'pet_store',
  VETERINARY_CLINIC = 'veterinary_clinic',
  PET_GROOMING = 'pet_grooming',
  PET_HOTEL = 'pet_hotel',
  PET_TRAINING = 'pet_training',
  COMPREHENSIVE_SERVICE = 'comprehensive_service',
  FRANCHISE = 'franchise',
  MOBILE_SERVICE = 'mobile_service',
  OTHER = 'other'
}

export enum ServiceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DEACTIVATED = 'deactivated'
}

export enum ProductStatus {
  ACTIVE = 'Active',
  DEACTIVATED = 'deactivated'
}

export enum InventoryStatus {
  IN_STOCK = 'InStock',
  OUT_OF_STOCK = 'OutOfStock',
  BACKORDER = 'Backorder',
  PREORDER = 'Preorder'
}

export enum TransactionStatus {
  DRAFT = 'draft',
  CONFIRMED = 'confirmed',
  CANCELLED = 'cancelled',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed',
  LAPSED = 'lapsed'
}

export enum PaymentStatus {
  PAID = 'paid',
  UNPAID = 'unpaid',
  PENDING = 'pending',
  FAILED = 'failed',
  REFUNDED = 'refunded'
}

export enum PaymentMethodType {
  ONLINE = 'online',
  IN_STORE = 'in-store'
}

export enum OrderType {
  SUBSCRIPTIONS = 'subscriptions',
  PRODUCTS = 'products',
  ONENATA_PLAN = 'onenata-plan',
  OTHER = 'other'
}

export enum OrderStatus {
  PENDING = 'pending',
  REJECTED = 'rejected',
  COMPLETED = 'completed',
  REFUNDED = 'refunded'
}

export enum DeliveryStatus {
  PREPARE_ORDER = 'prepare_order',
  READY_FOR_CUSTOMER = 'ready_for_customer',
  PICKED_UP = 'picked_up',
  OUT_OF_DELIVERY = 'out_of_delivery',
  DELIVERED = 'delivered'
}

export enum EventType {
  ADOPTION = 'adoption',
  PROMOTION = 'promotion',
  TRAINING = 'training',
  HEALTHCARE = 'healthcare',
  COMPETITION = 'competition',
  SOCIAL = 'social',
  OTHER = 'other'
}

export enum EventStatus {
  ACTIVE = 'active',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
  INACTIVE = 'inactive',
  PENDING = 'pending'
}

export enum ServiceCategory {
  GROOMING = 'grooming',
  BOARDING = 'boarding',
  VETERINARY = 'veterinary',
  TRAINING = 'training',
  DAY_CARE = 'day_care',
  MOBILE_SERVICE = 'mobile_service',
  WASH = 'wash',
  OTHER = 'other'
}

export enum ServiceBreed {
  DOG = 'Dog',
  CAT = 'Cat',
  OTHER = 'Other'
}

export enum Currency {
  CAD = 'CAD',
  USD = 'USD',
  EUR = 'EUR',
  GBP = 'GBP'
}

// 常量定义
export const DEFAULT_CURRENCY = Currency.CAD;
export const DEFAULT_LANGUAGE = 'en';
export const DEFAULT_TIMEZONE = 'America/Toronto';

// 工具函数
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidPhoneNumber(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
  return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
}

export function formatCurrency(amount: number, currency: Currency = DEFAULT_CURRENCY): string {
  return new Intl.NumberFormat('en-CA', {
    style: 'currency',
    currency: currency
  }).format(amount);
}

export function formatTimestamp(timestamp: Timestamp): string {
  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
  return date.toISOString();
}

export function parseTimestamp(timestamp: string | number | Date): Date {
  if (timestamp instanceof Date) return timestamp;
  if (typeof timestamp === 'string') return new Date(timestamp);
  return new Date(timestamp);
}

// ============= Appointment System Types =============

// 预约状态
export enum AppointmentStatus {
  DRAFT = 'draft',                    // 草稿
  CONFIRMED = 'confirmed',            // 已确认
  IN_PROGRESS = 'in_progress',        // 进行中
  COMPLETED = 'completed',            // 已完成
  CANCELLED = 'cancelled',            // 已取消
  NO_SHOW = 'no_show',                // 未到场
  LAPSED = 'lapsed'                   // 已过期
}

// 预约来源
export enum AppointmentSource {
  PORTAL = 'portal',                  // Portal 后台
  ONENATA_APP = 'onenata_app',        // OneNata App
  EXTERNAL_LINK = 'external_link'     // 外部链接
}

// 店铺服务状态
export enum StoreServiceStatus {
  ACTIVE = 'active',                  // 激活
  INACTIVE = 'inactive',              // 未激活
  DISABLED = 'disabled'               // 禁用
}

// 时间块状态
export enum TimeSlotStatus {
  AVAILABLE = 'available',            // 可用
  BOOKED = 'booked',                  // 已预约
  BLOCKED = 'blocked',                // 被阻止
  HOLIDAY = 'holiday'                 // 假期
}

// 客户状态
export enum CustomerStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BLOCKED = 'blocked'
}

// 通知类型
export enum NotificationType {
  APPOINTMENT_CONFIRMED = 'appointment_confirmed',
  APPOINTMENT_REMINDER = 'appointment_reminder',
  APPOINTMENT_CANCELLED = 'appointment_cancelled',
  APPOINTMENT_COMPLETED = 'appointment_completed',
  STAFF_ASSIGNED = 'staff_assigned',
  SERVICE_UPDATED = 'service_updated'
}

// 时间块接口 (15分钟为一个块)
export interface TimeSlot {
  startTime: string;                  // HH:MM 格式
  endTime: string;                    // HH:MM 格式
  status: TimeSlotStatus;
  appointmentId?: string;             // 如果已预约，关联的预约ID
  blockedReason?: string;             // 如果被阻止，原因说明
}

// 可用时间段
export interface AvailableTimeSlot {
  date: string;                       // YYYY-MM-DD 格式
  timeSlots: TimeSlot[];
  staffId: string;
  serviceId: string;
}

// 预约时间信息
export interface AppointmentTimeInfo {
  date: string;                       // YYYY-MM-DD 格式
  startTime: string;                  // HH:MM 格式
  endTime: string;                    // HH:MM 格式
  duration: number;                   // 分钟数
  timeSlots: string[];                // 占用的时间块 ['09:00', '09:15', '09:30']
}

// 客户信息接口
export interface CustomerInfo {
  customerId: string;
  customerSource: CustomerSource;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  email?: string;
  phoneNumber?: string;
  address?: Address;
  notes?: string;
  petIds?: string[];                  // 关联的宠物ID
}

// 店铺服务接口
export interface StoreServiceInfo {
  serviceId: string;
  serviceName: string;
  serviceCategory: ServiceCategory;
  serviceBreed: ServiceBreed;
  isActive: boolean;
  staffIds: string[];                 // 提供此服务的员工ID列表
  minDuration: number;                // 最短服务时间（分钟）
  maxDuration: number;                // 最长服务时间（分钟）
  basePrice: number;                  // 基础价格
  currency: Currency;
}

// 预约通知设置
export interface AppointmentNotificationSettings {
  emailEnabled: boolean;
  smsEnabled: boolean;
  fcmEnabled: boolean;
  reminderHours: number[];            // 提前提醒时间，如 [24, 2] 表示提前24小时和2小时提醒
}
