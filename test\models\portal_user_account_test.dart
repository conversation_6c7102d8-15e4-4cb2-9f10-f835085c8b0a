import 'package:flutter_test/flutter_test.dart';
import 'package:onenata_app/models/models_i.dart';

void main() {
  group('PortalUserAccount Model Tests', () {
    late PortalUserAccount testAccount;

    setUp(() {
      testAccount = PortalUserAccount(
        fid: 'test_firebase_id',
        email: '<EMAIL>',
        phoneNumber: '+****************',
        isEmailVerified: true,
        needChangePassword: false,
        sid: 'test_account_id',
        isValid: true,
        isSynced: true,
      );
    });

    test('should create PortalUserAccount with required fields', () {
      expect(testAccount.fid, equals('test_firebase_id'));
      expect(testAccount.email, equals('<EMAIL>'));
      expect(testAccount.phoneNumber, equals('+****************'));
      expect(testAccount.isEmailVerified, isTrue);
      expect(testAccount.needChangePassword, isFalse);
    });

    test('should validate email format correctly', () {
      // Valid email
      expect(testAccount.isValidEmail(), isTrue);

      // Invalid email
      testAccount.email = 'invalid-email';
      expect(testAccount.isValidEmail(), isFalse);

      testAccount.email = 'test@';
      expect(testAccount.isValidEmail(), isFalse);

      testAccount.email = '@example.com';
      expect(testAccount.isValidEmail(), isFalse);
    });

    test('should validate phone number format correctly', () {
      // Valid phone number
      expect(testAccount.isValidPhoneNumber(), isTrue);

      // Invalid phone number
      testAccount.phoneNumber = '************';
      expect(testAccount.isValidPhoneNumber(), isFalse);

      testAccount.phoneNumber = '+1 ************';
      expect(testAccount.isValidPhoneNumber(), isFalse);

      testAccount.phoneNumber = '(*************';
      expect(testAccount.isValidPhoneNumber(), isFalse);
    });

    test('should check login capability correctly', () {
      // Can login when email is verified and valid
      expect(testAccount.canLogin(), isTrue);

      // Cannot login when email is not verified
      testAccount.isEmailVerified = false;
      expect(testAccount.canLogin(), isFalse);

      // Cannot login when email is invalid
      testAccount.isEmailVerified = true;
      testAccount.email = 'invalid-email';
      expect(testAccount.canLogin(), isFalse);
    });

    test('should get clean phone number correctly', () {
      expect(testAccount.getCleanPhoneNumber(), equals('***********'));

      testAccount.phoneNumber = '+****************';
      expect(testAccount.getCleanPhoneNumber(), equals('***********'));
    });

    test('should convert to/from JSON correctly', () {
      final json = testAccount.toJson();
      final fromJson = PortalUserAccount.fromJson(json);

      expect(fromJson.fid, equals(testAccount.fid));
      expect(fromJson.email, equals(testAccount.email));
      expect(fromJson.phoneNumber, equals(testAccount.phoneNumber));
      expect(fromJson.isEmailVerified, equals(testAccount.isEmailVerified));
      expect(fromJson.needChangePassword, equals(testAccount.needChangePassword));
    });

    test('should convert to Firestore data correctly', () {
      final firestoreData = testAccount.toFirestoreData();

      expect(firestoreData['fid'], equals('test_firebase_id'));
      expect(firestoreData['email'], equals('<EMAIL>'));
      expect(firestoreData['phone_number'], equals('+****************'));
      expect(firestoreData['is_email_verified'], isTrue);
      expect(firestoreData['need_change_password'], isFalse);
    });

    test('should convert to Firestore data format correctly', () {
      final firestoreData = testAccount.toFirestoreData();

      expect(firestoreData['fid'], equals('test_firebase_id'));
      expect(firestoreData['email'], equals('<EMAIL>'));
      expect(firestoreData['phone_number'], equals('+****************'));
      expect(firestoreData['is_email_verified'], isTrue);
      expect(firestoreData['need_change_password'], isFalse);
    });

    test('should have correct collection name', () {
      expect(PortalUserAccount.collection, equals('portal-user-account'));
    });

    test('should handle default values correctly', () {
      final defaultAccount = PortalUserAccount(
        fid: 'test_fid',
        email: '<EMAIL>',
        phoneNumber: '+****************',
      );

      expect(defaultAccount.isEmailVerified, isFalse);
      expect(defaultAccount.needChangePassword, isTrue);
      expect(defaultAccount.isValid, isTrue);
      expect(defaultAccount.isSynced, isTrue);
    });
  });
}
