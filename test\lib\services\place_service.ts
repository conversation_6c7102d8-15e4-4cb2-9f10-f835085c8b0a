/* eslint-disable @typescript-eslint/no-explicit-any */
import { GeoPlace, BusinessStatus } from '../models/place';
import { FirestoreService } from "../firebase/services";
import { v4 as uuidv4 } from 'uuid';

/**
 * Place Service Class
 * Handles all place-related operations including CRUD operations with Firestore
 */
export class PlaceService {
  private static PLACE_COLLECTION = 'place';

  /**
   * Create a new place in Firestore
   * @param placeData Place data to create
   * @param storeId Associated store ID
   */
  static async createPlace(
    placeData: Partial<GeoPlace>,
    storeId: string
  ): Promise<{
    success: boolean;
    data?: GeoPlace;
    error?: string;
  }> {
    try {
      const ONPlaceId = uuidv4();
      
      const place: GeoPlace = {
        sid: ONPlaceId,
        ONPlaceId: ONPlaceId,
        GMapPlaceId: placeData.GMapPlaceId,
        name: placeData.name || '',
        internationalPhoneNumber: placeData.internationalPhoneNumber,
        types: placeData.types || [],
        allowsDogs: placeData.allowsDogs,
        formattedAddress: placeData.formattedAddress,
        displayName: placeData.displayName,
        postalAddress: placeData.postalAddress,
        businessStatus: placeData.businessStatus || BusinessStatus.OPERATIONAL,
        location: placeData.location,
        regularOpeningHours: placeData.regularOpeningHours,
        photos: placeData.photos,
        rating: placeData.rating,
        reviews: placeData.reviews,
        storeId: storeId,
        created_by: storeId,
        updated_by: storeId,
        create_date: new Date(),
        update_date: new Date()
      };

      const result = await FirestoreService.createWithId(
        PlaceService.PLACE_COLLECTION,
        ONPlaceId,
        place as unknown as Record<string, unknown>
      );

      if (result.success) {
        return {
          success: true,
          data: place
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to create place'
        };
      }
    } catch (error) {
      console.error('Error creating place:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create place'
      };
    }
  }

  /**
   * Update an existing place in Firestore
   * @param placeId Place ID to update
   * @param placeData Updated place data
   * @param storeId Store ID for audit trail
   */
  static async updatePlace(
    placeId: string,
    placeData: Partial<GeoPlace>,
    storeId: string
  ): Promise<{
    success: boolean;
    data?: GeoPlace;
    error?: string;
  }> {
    try {
      const updateData = {
        ...placeData,
        updated_by: storeId,
        updatedAt: new Date()
      };

      const result = await FirestoreService.update(
        PlaceService.PLACE_COLLECTION,
        placeId,
        updateData
      );

      if (result.success) {
        // Get the updated place data
        const getResult = await this.getPlaceById(placeId);
        return getResult;
      } else {
        return {
          success: false,
          error: result.error || 'Failed to update place'
        };
      }
    } catch (error) {
      console.error('Error updating place:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update place'
      };
    }
  }

  /**
   * Get place by ID
   * @param placeId Place ID to retrieve
   */
  static async getPlaceById(placeId: string): Promise<{
    success: boolean;
    data?: GeoPlace;
    error?: string;
  }> {
    try {
      const result = await FirestoreService.getById<GeoPlace & { id?: string } & Record<string, unknown>>(
        PlaceService.PLACE_COLLECTION,
        placeId
      );

      if (result.success && result.data) {
        return {
          success: true,
          data: result.data
        };
      } else {
        return {
          success: false,
          error: result.error || 'Place not found'
        };
      }
    } catch (error) {
      console.error('Error getting place by ID:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get place'
      };
    }
  }

  /**
   * Get place by store ID
   * @param storeId Store ID to search for
   */
  static async getPlaceByStoreId(storeId: string): Promise<{
    success: boolean;
    data?: GeoPlace;
    error?: string;
  }> {
    try {
      const result = await FirestoreService.getMany<GeoPlace & { id?: string } & Record<string, unknown>>(
        PlaceService.PLACE_COLLECTION,
        {
          where: [{ field: 'storeId', operator: '==', value: storeId }],
          limit: 1
        }
      );

      if (result.success && result.data && result.data.length > 0) {
        return {
          success: true,
          data: result.data[0]
        };
      } else {
        return {
          success: false,
          error: 'Place not found for store ID'
        };
      }
    } catch (error) {
      console.error('Error getting place by store ID:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get place'
      };
    }
  }

  /**
   * Delete place by ID
   * @param placeId Place ID to delete
   */
  static async deletePlace(placeId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const result = await FirestoreService.delete(
        PlaceService.PLACE_COLLECTION,
        placeId
      );

      return result;
    } catch (error) {
      console.error('Error deleting place:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete place'
      };
    }
  }

  /**
   * Create or update place based on Google Maps data
   * @param googlePlaceData Google Maps place data
   * @param storeId Associated store ID
   */
  static async createOrUpdatePlaceFromGoogle(
    googlePlaceData: {
      placeId: string;
      name: string;
      formattedAddress: string;
      location: { lat: number; lng: number };
      address: any;
      businessName?: string;
      website?: string;
      phoneNumber?: string;
      businessStatus?: string;
      rating?: number;
      userRatingsTotal?: number;
      types?: string[];
      openingHours?: any;
      photos?: Array<{
        photo_reference: string;
        height: number;
        width: number;
        html_attributions: string[];
      }>;
    },
    storeId: string
  ): Promise<{
    success: boolean;
    data?: GeoPlace;
    error?: string;
  }> {
    try {
      // First check if place already exists for this store
      const existingPlace = await this.getPlaceByStoreId(storeId);

      const placeData: Partial<GeoPlace> = {
        GMapPlaceId: googlePlaceData.placeId,
        name: googlePlaceData.name,
        internationalPhoneNumber: googlePlaceData.phoneNumber,
        types: googlePlaceData.types || [],
        formattedAddress: googlePlaceData.formattedAddress,
        displayName: {
          text: googlePlaceData.name,
          languageCode: 'en'
        },
        postalAddress: {
          regionCode: googlePlaceData.address.country,
          postalCode: googlePlaceData.address.postCode,
          administrativeArea: googlePlaceData.address.province,
          locality: googlePlaceData.address.city,
          addressLines: [googlePlaceData.address.addressLine1, googlePlaceData.address.addressLine2].filter(Boolean) as string[]
        },
        location: googlePlaceData.location,
        businessStatus: googlePlaceData.businessStatus as BusinessStatus || BusinessStatus.OPERATIONAL,
        regularOpeningHours: googlePlaceData.openingHours,
        rating: googlePlaceData.rating,
        storeId: storeId,
        // Convert Google photos to GeoPhoto format
        photos: googlePlaceData.photos ? googlePlaceData.photos.map(photo => ({
          name: photo.photo_reference,
          widthPx: photo.width,
          heightPx: photo.height,
          authorAttributions: photo.html_attributions.map(attr => ({
            displayName: attr.replace(/<[^>]*>/g, ''), // Remove HTML tags
            uri: attr.match(/href="([^"]*)"/)?.[1] || undefined
          }))
        })) : undefined
      };

      if (existingPlace.success && existingPlace.data) {
        // Update existing place
        return await this.updatePlace(existingPlace.data.ONPlaceId, placeData, storeId);
      } else {
        // Create new place
        return await this.createPlace(placeData, storeId);
      }
    } catch (error) {
      console.error('Error creating/updating place from Google data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create/update place'
      };
    }
  }
}

export default PlaceService; 