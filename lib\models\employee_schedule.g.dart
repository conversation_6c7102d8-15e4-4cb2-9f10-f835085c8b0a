// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'employee_schedule.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkTime _$WorkTimeFromJson(Map<String, dynamic> json) => WorkTime(
      weekday: json['weekday'] as String,
      open: json['open'] as bool,
      startTime: json['startTime'] as String,
      endTime: json['endTime'] as String,
    );

Map<String, dynamic> _$WorkTimeToJson(WorkTime instance) => <String, dynamic>{
      'weekday': instance.weekday,
      'open': instance.open,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
    };

EmployeeSchedule _$EmployeeScheduleFromJson(Map<String, dynamic> json) =>
    EmployeeSchedule(
      uid: json['uid'] as String,
      storeId: json['storeId'] as String,
      active: json['active'] as bool? ?? true,
      workTimes: (json['workTimes'] as List<dynamic>?)
              ?.map((e) => WorkTime.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      sid: json['sid'] as String?,
      isValid: json['isValid'] == null
          ? true
          : JsonUtil.boolFromJson(json['isValid']),
      isSynced: json['isSynced'] == null
          ? true
          : JsonUtil.boolFromJson(json['isSynced']),
    )
      ..id = (json['id'] as num?)?.toInt()
      ..name = json['name'] as String?;

Map<String, dynamic> _$EmployeeScheduleToJson(EmployeeSchedule instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'uid': instance.uid,
      'storeId': instance.storeId,
      'active': instance.active,
      'workTimes': instance.workTimes,
    };
