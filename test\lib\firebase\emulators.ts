// Firebase 模拟器配置和工具函数

export interface EmulatorConfig {
  auth: {
    host: string;
    port: number;
  };
  firestore: {
    host: string;
    port: number;
  };
  storage: {
    host: string;
    port: number;
  };
  database: {
    host: string;
    port: number;
  };
  functions: {
    host: string;
    port: number;
  };
  ui: {
    host: string;
    port: number;
  };
}

// 默认模拟器配置
export const EMULATOR_CONFIG: EmulatorConfig = {
  auth: {
    host: 'localhost',
    port: 9099,
  },
  firestore: {
    host: 'localhost',
    port: 8080,
  },
  storage: {
    host: 'localhost',
    port: 9199,
  },
  database: {
    host: 'localhost',
    port: 9000,
  },
  functions: {
    host: 'localhost',
    port: 5001,
  },
  ui: {
    host: 'localhost',
    port: 4000,
  },
};

// 检查模拟器是否运行
export const checkEmulatorStatus = async (service: keyof Omit<EmulatorConfig, 'ui'>): Promise<boolean> => {
  if (typeof window === 'undefined') return false;
  
  const config = EMULATOR_CONFIG[service];
  const url = `http://${config.host}:${config.port}`;
  
  try {
    await fetch(url, { 
      method: 'HEAD',
      mode: 'no-cors' 
    });
    return true;
  } catch {
    return false;
  }
};

// 获取所有模拟器状态
export const getAllEmulatorStatus = async (): Promise<Record<string, boolean>> => {
  const services = ['auth', 'firestore', 'storage', 'database', 'functions'] as const;
  const results: Record<string, boolean> = {};
  
  for (const service of services) {
    results[service] = await checkEmulatorStatus(service);
  }
  
  return results;
};

// 获取模拟器连接信息
export const getEmulatorInfo = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isClient = typeof window !== 'undefined';
  
  return {
    isDevelopment,
    isClient,
    shouldUseEmulators: isDevelopment && isClient,
    config: EMULATOR_CONFIG,
    urls: {
      auth: `http://${EMULATOR_CONFIG.auth.host}:${EMULATOR_CONFIG.auth.port}`,
      firestore: `http://${EMULATOR_CONFIG.firestore.host}:${EMULATOR_CONFIG.firestore.port}`,
      storage: `http://${EMULATOR_CONFIG.storage.host}:${EMULATOR_CONFIG.storage.port}`,
      database: `http://${EMULATOR_CONFIG.database.host}:${EMULATOR_CONFIG.database.port}`,
      functions: `http://${EMULATOR_CONFIG.functions.host}:${EMULATOR_CONFIG.functions.port}`,
      ui: `http://${EMULATOR_CONFIG.ui.host}:${EMULATOR_CONFIG.ui.port}`,
    }
  };
};

// 日志模拟器连接状态
export const logEmulatorStatus = async () => {
  if (process.env.NODE_ENV !== 'development') return;
  
  console.log('🔥 Firebase 模拟器状态检查...');
  const info = getEmulatorInfo();
  console.log('📋 模拟器配置:', info);
  
  const status = await getAllEmulatorStatus();
  console.log('📊 模拟器运行状态:', status);
  
  Object.entries(status).forEach(([service, isRunning]) => {
    const emoji = isRunning ? '✅' : '❌';
    const url = info.urls[service as keyof typeof info.urls];
    console.log(`${emoji} ${service.toUpperCase()}: ${url} ${isRunning ? '(运行中)' : '(未运行)'}`);
  });
  
  console.log(`🌐 模拟器 UI: ${info.urls.ui}`);
}; 