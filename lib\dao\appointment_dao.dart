import 'dart:async';
import 'package:onenata_app/common/plugin/firebase/firebase_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';

class AppointmentDao {
  final FirebaseFirestore _db = FirebaseFirestore.instance;

  // Collection references
  final CollectionReference _appointmentCollection =
      FirebaseFirestore.instance.collection(Appointment.collection);

  /// 从Firestore文档创建Appointment对象的辅助方法
  Appointment _appointmentFromDoc(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    // 转换snake_case到camelCase
    final Map<String, dynamic> jsonData = {};
    data.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });
    // 设置文档ID
    jsonData['sid'] = doc.id;
    return Appointment.fromJson(jsonData);
  }

  // ==================== Appointment CRUD ====================

  /// 添加预约
  Future<void> addAppointment(Appointment appointment) async {
    DocumentReference appointmentRef = _appointmentCollection.doc(appointment.sid);

    Map<String, dynamic> appointmentDoc = appointment.toFirestoreData();
    appointmentDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await appointmentRef.set(appointmentDoc);
  }

  /// 更新预约
  Future<void> updateAppointment(Appointment appointment, {bool mergeOption = false}) async {
    DocumentReference appointmentRef = _appointmentCollection.doc(appointment.sid!);
    Map<String, dynamic> appointmentDoc = appointment.toFirestoreData();
    appointmentDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await appointmentRef.set(appointmentDoc, SetOptions(merge: mergeOption));
  }

  /// 删除预约
  Future<void> deleteAppointment(String appointmentId) async {
    DocumentReference appointmentRef = _appointmentCollection.doc(appointmentId);
    await appointmentRef.delete();
  }

  /// 软删除预约
  Future<void> softDeleteAppointment(String appointmentId) async {
    DocumentReference appointmentRef = _appointmentCollection.doc(appointmentId);
    await appointmentRef.update({CommonField.isValid: false});
  }

  /// 根据ID获取预约
  Future<Appointment?> getAppointmentById(String appointmentId) async {
    DocumentSnapshot doc = await _appointmentCollection.doc(appointmentId).get();

    if (doc.exists) {
      return _appointmentFromDoc(doc);
    } else {
      return null;
    }
  }

  /// 根据用户ID获取预约列表
  Future<List<Appointment>> getAppointmentsByUserId(String userId) async {
    QuerySnapshot querySnapshot = await _appointmentCollection
        .where('user_id', isEqualTo: userId)
        .where(CommonField.isValid, isEqualTo: true)
        .orderBy(CommonField.createDate, descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => _appointmentFromDoc(doc))
        .toList();
  }

  /// 根据店铺ID获取预约列表
  Future<List<Appointment>> getAppointmentsByStoreId(String storeId) async {
    QuerySnapshot querySnapshot = await _appointmentCollection
        .where('store_id', isEqualTo: storeId)
        .where(CommonField.isValid, isEqualTo: true)
        .orderBy(CommonField.createDate, descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => _appointmentFromDoc(doc))
        .toList();
  }

  /// 根据状态获取预约列表
  Future<List<Appointment>> getAppointmentsByStatus(
    String storeId,
    String status
  ) async {
    QuerySnapshot querySnapshot = await _appointmentCollection
        .where('store_id', isEqualTo: storeId)
        .where('status', isEqualTo: status)
        .where(CommonField.isValid, isEqualTo: true)
        .orderBy(CommonField.createDate, descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => _appointmentFromDoc(doc))
        .toList();
  }

  /// 根据日期范围获取预约列表
  Future<List<Appointment>> getAppointmentsByDateRange(
    String storeId,
    DateTime startDate,
    DateTime endDate
  ) async {
    QuerySnapshot querySnapshot = await _appointmentCollection
        .where('store_id', isEqualTo: storeId)
        .where('appointment_date', isGreaterThanOrEqualTo: startDate.millisecondsSinceEpoch)
        .where('appointment_date', isLessThanOrEqualTo: endDate.millisecondsSinceEpoch)
        .where(CommonField.isValid, isEqualTo: true)
        .orderBy('appointment_date')
        .get();

    return querySnapshot.docs
        .map((doc) => _appointmentFromDoc(doc))
        .toList();
  }

  /// 根据员工ID获取预约列表
  Future<List<Appointment>> getAppointmentsByStaffId(String staffId) async {
    QuerySnapshot querySnapshot = await _appointmentCollection
        .where('staff_id', isEqualTo: staffId)
        .where(CommonField.isValid, isEqualTo: true)
        .orderBy(CommonField.createDate, descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => _appointmentFromDoc(doc))
        .toList();
  }

  /// 检查时间段冲突
  Future<bool> checkTimeSlotConflict(
    String storeId,
    String staffId,
    DateTime appointmentDate,
    String startTime,
    String endTime,
    {String? excludeAppointmentId}
  ) async {
    Query query = _appointmentCollection
        .where('store_id', isEqualTo: storeId)
        .where('staff_id', isEqualTo: staffId)
        .where('appointment_date', isEqualTo: appointmentDate.millisecondsSinceEpoch)
        .where('status', whereIn: ['CONFIRMED', 'IN_PROGRESS'])
        .where(CommonField.isValid, isEqualTo: true);

    QuerySnapshot querySnapshot = await query.get();

    for (var doc in querySnapshot.docs) {
      if (excludeAppointmentId != null && doc.id == excludeAppointmentId) {
        continue;
      }

      final appointment = _appointmentFromDoc(doc);

      // 检查时间冲突逻辑
      if (_isTimeConflict(
        startTime, endTime,
        appointment.timeInfo.startTime, appointment.timeInfo.endTime
      )) {
        return true;
      }
    }

    return false;
  }

  /// 时间冲突检查辅助方法
  bool _isTimeConflict(String start1, String end1, String start2, String end2) {
    int start1Minutes = _timeToMinutes(start1);
    int end1Minutes = _timeToMinutes(end1);
    int start2Minutes = _timeToMinutes(start2);
    int end2Minutes = _timeToMinutes(end2);

    return !(end1Minutes <= start2Minutes || start1Minutes >= end2Minutes);
  }

  /// 将时间字符串转换为分钟数
  int _timeToMinutes(String time) {
    if (time.isEmpty) return 0;
    final parts = time.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }
}