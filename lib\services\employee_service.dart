import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:onenata_app/models/employee_schedule.dart';
import 'package:onenata_app/models/staff_service_offering.dart';

/// 员工服务类
/// 负责获取员工排班和服务信息
class EmployeeService {
  static final EmployeeService _instance = EmployeeService._internal();
  factory EmployeeService() => _instance;
  EmployeeService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 获取店铺的所有员工排班信息
  Future<List<EmployeeSchedule>> getStoreEmployeeSchedules(String storeId) async {
    try {
      print('🔍 查询店铺员工排班，storeId: $storeId');
      
      final QuerySnapshot querySnapshot = await _firestore
          .collection('portal-user-data')
          .get();
      
      List<EmployeeSchedule> schedules = [];
      
      for (final userDoc in querySnapshot.docs) {
        try {
          // 查询该用户的employee-schedule子集合
          final scheduleQuery = await _firestore
              .collection('portal-user-data')
              .doc(userDoc.id)
              .collection('employee-schedule')
              .where('storeId', isEqualTo: storeId)
              //.where('isValid', isEqualTo: true)
              .get();
          
          for (final scheduleDoc in scheduleQuery.docs) {
            try {
              final schedule = EmployeeSchedule.fromFirestoreData(scheduleDoc);
              schedules.add(schedule);
              print('✅ 解析员工排班成功: ${schedule.uid}');
            } catch (e) {
              print('❌ 解析员工排班失败 ${scheduleDoc.id}: $e');
            }
          }
        } catch (e) {
          print('❌ 查询用户排班失败 ${userDoc.id}: $e');
        }
      }
      
      print('🎯 最终返回 ${schedules.length} 个员工排班');
      return schedules;
    } catch (e) {
      print('❌ 获取店铺员工排班失败: $e');
      return [];
    }
  }

  /// 获取店铺的所有员工服务信息
  Future<List<StaffServiceOffering>> getStoreEmployeeServices(String storeId) async {
    try {
      print('🔍 查询店铺员工服务，storeId: $storeId');
      
      final QuerySnapshot querySnapshot = await _firestore
          .collection('portal-user-data')
          .get();
      
      List<StaffServiceOffering> services = [];
      
      for (final userDoc in querySnapshot.docs) {
        try {
          // 查询该用户的employee-service子集合
          final serviceQuery = await _firestore
              .collection('portal-user-data')
              .doc(userDoc.id)
              .collection('employee-service')
              .where('storeId', isEqualTo: storeId)
              .where('serviceStatus', isEqualTo: 'active')
              .get();
          
          for (final serviceDoc in serviceQuery.docs) {
            try {
              final service = StaffServiceOffering.fromFirestoreData(serviceDoc);
              services.add(service);
              print('✅ 解析员工服务成功: ${service.serviceName}');
            } catch (e) {
              print('❌ 解析员工服务失败 ${serviceDoc.id}: $e');
            }
          }
        } catch (e) {
          print('❌ 查询用户服务失败 ${userDoc.id}: $e');
        }
      }
      
      print('🎯 最终返回 ${services.length} 个员工服务');
      return services;
    } catch (e) {
      print('❌ 获取店铺员工服务失败: $e');
      return [];
    }
  }

  /// 获取指定员工的排班信息
  Future<EmployeeSchedule?> getEmployeeSchedule(String storeId, String employeeUid) async {
    try {
      final schedules = await getStoreEmployeeSchedules(storeId);
      return schedules.where((schedule) => schedule.uid == employeeUid).firstOrNull;
    } catch (e) {
      print('❌ 获取员工排班失败: $e');
      return null;
    }
  }

  /// 获取指定员工提供的服务
  Future<List<StaffServiceOffering>> getEmployeeServices(String storeId, String employeeUid) async {
    try {
      final services = await getStoreEmployeeServices(storeId);
      return services.where((service) => service.uid == employeeUid).toList();
    } catch (e) {
      print('❌ 获取员工服务失败: $e');
      return [];
    }
  }

  /// 根据服务类别获取可提供该服务的员工
  Future<List<String>> getEmployeesForService(String storeId, String serviceCategory) async {
    try {
      final services = await getStoreEmployeeServices(storeId);
      final matchingServices = services.where((service) => 
        service.serviceCategory == serviceCategory
      ).toList();
      
      return matchingServices.map((service) => service.uid).toSet().toList();
    } catch (e) {
      print('❌ 获取服务员工失败: $e');
      return [];
    }
  }

  /// 生成指定日期和员工的可用时间段
  Future<List<String>> getAvailableTimeSlots({
    required String storeId,
    required String employeeUid,
    required DateTime date,
    int intervalMinutes = 30,
  }) async {
    try {
      final schedule = await getEmployeeSchedule(storeId, employeeUid);
      if (schedule == null) return [];

      final weekday = _getWeekdayName(date.weekday);
      final workTime = schedule.getWorkTimeForDay(weekday);
      
      if (workTime == null || !workTime.open) return [];

      return _generateTimeSlots(
        workTime.startTime,
        workTime.endTime,
        intervalMinutes,
      );
    } catch (e) {
      print('❌ 获取可用时间段失败: $e');
      return [];
    }
  }

  /// 获取星期名称
  String _getWeekdayName(int weekday) {
    const weekdays = [
      'Monday', 'Tuesday', 'Wednesday', 'Thursday', 
      'Friday', 'Saturday', 'Sunday'
    ];
    return weekdays[weekday - 1];
  }

  /// 生成时间段列表
  List<String> _generateTimeSlots(String startTime, String endTime, int intervalMinutes) {
    List<String> timeSlots = [];
    
    final start = _parseTime(startTime);
    final end = _parseTime(endTime);
    
    DateTime current = start;
    while (current.isBefore(end)) {
      timeSlots.add(_formatTime(current));
      current = current.add(Duration(minutes: intervalMinutes));
    }
    
    return timeSlots;
  }

  /// 解析时间字符串
  DateTime _parseTime(String timeStr) {
    final parts = timeStr.split(':');
    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);
    return DateTime(2000, 1, 1, hour, minute);
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
