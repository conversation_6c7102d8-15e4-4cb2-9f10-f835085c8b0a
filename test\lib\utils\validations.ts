/**
 * 通用表单验证函数库
 * 提供可复用的验证方法，用于各种表单验证场景
 */

import { Timestamp } from '../models/types';

/**
 * 验证电子邮箱格式
 * @param email 需要验证的电子邮箱
 * @returns 验证是否通过
 */
export const validateEmail = (email: string): boolean => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };
  
  /**
   * 验证字符串长度是否在指定范围内
   * @param str 需要验证的字符串
   * @param minLength 最小长度 (默认为0)
   * @param maxLength 最大长度 (默认为无限)
   * @returns 验证是否通过
   */
  export const validateLength = (str: string, minLength = 0, maxLength = Infinity): boolean => {
    const length = str.trim().length;
    return length >= minLength && length <= maxLength;
  };
  
  /**
   * 验证字符串是否为空
   * @param str 需要验证的字符串
   * @returns 验证是否通过 (非空为true)
   */
  export const validateRequired = (str: string): boolean => {
    return str.trim().length > 0;
  };
  
/**
 * 验证北美电话号码格式 (美国和加拿大)
 * 支持格式: (*************, ************, ************, 1234567890, ******-456-7890
 * @param phone 需要验证的电话号码
 * @returns 验证是否通过
 */
export const validatePhoneNumber = (phone: string): boolean => {
  if (!phone) return false;
  
  // 移除所有非数字字符
  const cleanPhone = phone.replace(/\D/g, '');
  
  // 支持10位(本地)或11位(带国家代码1)的北美号码
  if (cleanPhone.length === 10) {
    // 10位号码: 第一位不能是0或1
    return /^[2-9]\d{2}[2-9]\d{6}$/.test(cleanPhone);
  } else if (cleanPhone.length === 11) {
    // 11位号码: 必须以1开头，后面跟10位有效号码
    return /^1[2-9]\d{2}[2-9]\d{6}$/.test(cleanPhone);
  }
  
  return false;
};

/**
 * 格式化电话号码为标准显示格式
 * @param phone 输入的电话号码
 * @returns 格式化后的电话号码
 */
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return '';
  
  const cleanPhone = phone.replace(/\D/g, '');
  
  if (cleanPhone.length === 10) {
    return `(${cleanPhone.slice(0, 3)}) ${cleanPhone.slice(3, 6)}-${cleanPhone.slice(6)}`;
  } else if (cleanPhone.length === 11 && cleanPhone.startsWith('1')) {
    return `+1 (${cleanPhone.slice(1, 4)}) ${cleanPhone.slice(4, 7)}-${cleanPhone.slice(7)}`;
  }
  
  return phone;
};
  
/**
 * 验证网站URL格式
 * @param website 需要验证的网站URL
 * @returns 验证是否通过
 */
export const validateWebsite = (website: string): boolean => {
  if (!website) return true; // 网站是可选的
  
  // 更完善的URL验证正则表达式
  const urlRegex = /^(https?:\/\/)?(www\.)?[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}(\/.*)?$/;
  
  // 简单的域名验证（不带协议）
  const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$/;
  
  return urlRegex.test(website) || domainRegex.test(website);
};

/**
 * 标准化网站URL（添加协议前缀）
 * @param website 输入的网站URL
 * @returns 标准化后的URL
 */
export const normalizeWebsiteUrl = (website: string): string => {
  if (!website) return '';
  
  const trimmed = website.trim();
  if (!trimmed) return '';
  
  // 如果已经有协议，直接返回
  if (/^https?:\/\//i.test(trimmed)) {
    return trimmed;
  }
  
  // 添加https://前缀
  return `https://${trimmed}`;
};
  
  /**
   * 验证密码强度
   * @param password 需要验证的密码
   * @param minLength 最小长度 (默认为8)
   * @returns 验证是否通过
   */
  export const validatePassword = (password: string, minLength = 8): boolean => {
    if (password.length < minLength) return false;
    
    // 至少包含一个数字
    const hasNumber = /\d/.test(password);
    // 至少包含一个小写字母
    const hasLowercase = /[a-z]/.test(password);
    // 至少包含一个大写字母
    const hasUppercase = /[A-Z]/.test(password);
    
    return hasNumber && hasLowercase && hasUppercase;
  };
  
  /**
   * 验证两个字段值是否匹配 (如密码和确认密码)
   * @param value1 第一个值
   * @param value2 第二个值
   * @returns 验证是否通过
   */
  export const validateMatch = (value1: string, value2: string): boolean => {
    return value1 === value2;
  };
  
  /**
   * 验证表单字段
   * @param field 字段名称
   * @param value 字段值
   * @param options 验证选项
   * @returns 错误消息或空字符串 (无错误)
   */
  export const validateField = (
    field: string, 
    value: string,
    options: {
      required?: boolean;
      minLength?: number;
      maxLength?: number;
      isEmail?: boolean;
      isPhone?: boolean;
      isWebsite?: boolean;
      isPassword?: boolean;
      match?: string;
      errorMessages: Record<string, string>;
    }
  ): string => {
    const { required, minLength, maxLength, isEmail, isPhone, isWebsite, isPassword, match, errorMessages } = options;
    
    // 必填验证
    if (required && !validateRequired(value)) {
      return errorMessages.required || `${field} is required`;
    }
    
    // 如果字段为空且非必填，跳过其他验证
    if (!value.trim() && !required) {
      return '';
    }
    
    // 长度验证
    if ((minLength !== undefined || maxLength !== undefined) && 
        !validateLength(value, minLength, maxLength)) {
      if (minLength !== undefined && maxLength !== undefined) {
        return errorMessages.length || `${field} must be between ${minLength} and ${maxLength} characters`;
      } else if (minLength !== undefined) {
        return errorMessages.minLength || `${field} must be at least ${minLength} characters`;
      } else {
        return errorMessages.maxLength || `${field} cannot exceed ${maxLength} characters`;
      }
    }
    
    // 邮箱验证
    if (isEmail && value && !validateEmail(value)) {
      return errorMessages.email || `Please enter a valid email address`;
    }
    
    // 电话验证
    if (isPhone && value && !validatePhoneNumber(value)) {
      return errorMessages.phone || `Please enter a valid phone number`;
    }
    
    // 网站验证
    if (isWebsite && value && !validateWebsite(value)) {
      return errorMessages.website || `Please enter a valid website URL`;
    }
    
    // 密码验证
    if (isPassword && value && !validatePassword(value, minLength)) {
      return errorMessages.password || `Password must meet the requirements`;
    }
    
    // 匹配验证
    if (match !== undefined && !validateMatch(value, match)) {
      return errorMessages.match || `Fields do not match`;
    }
    
    return '';
  };
  
  /**
   * 添加协议前缀到网站URL
   * @param url 输入的URL
   * @returns 添加了协议前缀的URL
   * @deprecated 使用 normalizeWebsiteUrl 替代
   */
  export const addProtocolToUrl = (url: string): string => {
    return normalizeWebsiteUrl(url);
  };

/**
 * 格式化日期时间
 * @param date 日期对象、时间戳或日期字符串
 * @param format 格式化选项：'date'（仅日期）, 'time'（仅时间）, 'datetime'（日期和时间）
 * @param locale 地区设置，默认为 'zh-CN'
 */
export function formatDateTime(
  date: Date | string | number | null | undefined,
  format: 'date' | 'time' | 'datetime' = 'date',
  locale: string = 'zh-CN'
): string {
  if (!date) return '-';

  try {
    const d = date instanceof Date ? date : new Date(date);
    
    // 检查日期是否有效
    if (isNaN(d.getTime())) {
      console.warn('Invalid date:', date);
      return '-';
    }

    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour12: false
    };

    if (format === 'time') {
      options.year = undefined;
      options.month = undefined;
      options.day = undefined;
      options.hour = '2-digit';
      options.minute = '2-digit';
    } else if (format === 'datetime') {
      options.hour = '2-digit';
      options.minute = '2-digit';
    }

    return d.toLocaleString(locale, options);
  } catch (error) {
    console.error('Error formatting date:', error);
    return '-';
  }
}

/**
 * 格式化时间戳
 * @param timestamp Firebase Timestamp 或 Date 对象或时间戳
 */
export function formatTimestamp(timestamp: Timestamp | null | undefined): string {
  if (!timestamp) return '-';
  
  try {
    const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
    return formatDateTime(date, 'datetime');
  } catch (error) {
    console.error('Error formatting timestamp:', error);
    return '-';
  }
}

/**
 * 解析时间戳
 * @param timestamp 时间戳字符串或数字
 */
export function parseTimestamp(timestamp: string | number | Date): Date {
  if (timestamp instanceof Date) return timestamp;
  return new Date(timestamp);
}
  