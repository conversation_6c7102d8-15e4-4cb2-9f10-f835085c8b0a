/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, set, get, push, remove, onValue, off, query, orderByChild, equalTo } from 'firebase/database';
import { database } from '../config';

// 写入数据
export const writeData = async (path: string, data: any): Promise<void> => {
  try {
    const dbRef = ref(database, path);
    await set(dbRef, data);
    console.log('✅ 数据写入成功:', path);
  } catch (error) {
    console.error('❌ 数据写入失败:', error);
    throw error;
  }
};

// 读取数据
export const readData = async (path: string): Promise<any> => {
  try {
    const dbRef = ref(database, path);
    const snapshot = await get(dbRef);
    if (snapshot.exists()) {
      return snapshot.val();
    } else {
      console.log('📭 数据不存在:', path);
      return null;
    }
  } catch (error) {
    console.error('❌ 数据读取失败:', error);
    throw error;
  }
};

// 添加数据（自动生成 key）
export const pushData = async (path: string, data: any): Promise<string> => {
  try {
    const dbRef = ref(database, path);
    const newRef = push(dbRef, data);
    console.log('✅ 数据添加成功:', newRef.key);
    return newRef.key!;
  } catch (error) {
    console.error('❌ 数据添加失败:', error);
    throw error;
  }
};

// 删除数据
export const deleteData = async (path: string): Promise<void> => {
  try {
    const dbRef = ref(database, path);
    await remove(dbRef);
    console.log('✅ 数据删除成功:', path);
  } catch (error) {
    console.error('❌ 数据删除失败:', error);
    throw error;
  }
};

// 监听数据变化
export const listenToData = (
  path: string, 
  callback: (data: any) => void,
  errorCallback?: (error: Error) => void
): (() => void) => {
  const dbRef = ref(database, path);
  
  const unsubscribe = () => {
    off(dbRef, 'value', callback);
  };
  
  onValue(dbRef, (snapshot) => {
    const data = snapshot.exists() ? snapshot.val() : null;
    callback(data);
  }, errorCallback);
  
  return unsubscribe;
};

// 查询数据
export const queryData = async (
  path: string, 
  orderBy: string, 
  equalToValue: any
): Promise<any> => {
  try {
    const dbRef = ref(database, path);
    const queryRef = query(dbRef, orderByChild(orderBy), equalTo(equalToValue));
    const snapshot = await get(queryRef);
    
    if (snapshot.exists()) {
      return snapshot.val();
    } else {
      console.log('📭 查询无结果:', { path, orderBy, equalToValue });
      return null;
    }
  } catch (error) {
    console.error('❌ 数据查询失败:', error);
    throw error;
  }
}; 