// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'staff_service_offering.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StaffServiceOffering _$StaffServiceOfferingFromJson(
        Map<String, dynamic> json) =>
    StaffServiceOffering(
      uid: json['uid'] as String,
      storeId: json['storeId'] as String,
      serviceName: json['serviceName'] as String,
      serviceCategory: json['serviceCategory'] as String? ?? 'grooming',
      serviceBreed: json['serviceBreed'] as String? ?? 'dog',
      serviceStatus: json['serviceStatus'] as String? ?? 'active',
      serviceDuration: (json['serviceDuration'] as num?)?.toInt() ?? 60,
      serviceAmount: (json['serviceAmount'] as num?)?.toDouble() ?? 0.0,
      serviceAmountCurrency: json['serviceAmountCurrency'] as String? ?? 'CAD',
      serviceCount: (json['serviceCount'] as num?)?.toInt() ?? 0,
      servicePhotos: (json['servicePhotos'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      sid: json['sid'] as String?,
      isValid: json['isValid'] == null
          ? true
          : JsonUtil.boolFromJson(json['isValid']),
      isSynced: json['isSynced'] == null
          ? true
          : JsonUtil.boolFromJson(json['isSynced']),
    )
      ..id = (json['id'] as num?)?.toInt()
      ..name = json['name'] as String?;

Map<String, dynamic> _$StaffServiceOfferingToJson(
        StaffServiceOffering instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'uid': instance.uid,
      'storeId': instance.storeId,
      'serviceName': instance.serviceName,
      'serviceCategory': instance.serviceCategory,
      'serviceBreed': instance.serviceBreed,
      'serviceStatus': instance.serviceStatus,
      'serviceDuration': instance.serviceDuration,
      'serviceAmount': instance.serviceAmount,
      'serviceAmountCurrency': instance.serviceAmountCurrency,
      'serviceCount': instance.serviceCount,
      'servicePhotos': instance.servicePhotos,
    };
