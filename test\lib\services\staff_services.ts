/* eslint-disable @typescript-eslint/no-unused-vars */
import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  // setDoc, 
  updateDoc, 
//   deleteDoc, 
  query, 
  where, 
  // orderBy, 
//   limit,
//   Timestamp,
//   addDoc,
//   serverTimestamp
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { 
  PortalUserData, 
  PortalUserAccount,
  StoreStaffInfo, 
  EmployeeSchedule,
  EmployeeService,
//   EmployeeServiceTransaction,
  EmployeeScheduleImpl,
  // EmployeeServiceImpl
} from '../models/portal-user';
import { 
  StoreRole, 
  WorkTime, 
  UserType,
  ServiceCategory,
  ServiceBreed,
  ServiceStatus,
  Currency,
//   TransactionStatus,
//   PaymentStatus
} from '../models/types';
import { v4 as uuidv4 } from 'uuid';

import { FirestoreService } from '../firebase/services/firestore';
import { 
  checkEmailExists, 
  checkPhoneExists,
  createStaffAccount
} from '../firebase/services/auth';
import { ProgressCallback } from '../types/common';




// 员工详细信息接口（包含用户数据和店铺员工信息）
export interface StaffMember {
  userData: PortalUserData;
  staffInfo: StoreStaffInfo;
  schedule?: EmployeeSchedule;
  services?: EmployeeService[];
  isActive: boolean;
  email?: string;
}

// 创建员工数据接口
export interface CreateStaffData {
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  role: StoreRole;
  note?: string;
  password?: string; // 临时密码
}

// 更新员工信息数据接口
export interface UpdateStaffData {
  role?: StoreRole;
  active?: boolean;
  note?: string;
  startTime?: Date;
  endTime?: Date;
}

// 员工排班数据接口
export interface UpdateScheduleData {
  active: boolean;
  workTimes: WorkTime[];
}

// 员工服务数据接口
export interface CreateStaffServiceData {
  serviceName: string;
  serviceCategory: ServiceCategory;
  serviceBreed: ServiceBreed;
  serviceDuration: number; // 分钟
  serviceAmount: number;
  serviceAmountCurrency: Currency;
  servicePhotos?: string[];
}

// 服务响应接口
export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ServiceListResponse<T> extends ServiceResponse<T[]> {
  total?: number;
}

// 辅助函数

const generateTempPassword = (): string => {
  return Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8);
};

/**
 * 员工服务类
 * 负责处理店铺员工相关的所有操作
 */
export class StaffService {
  private readonly PORTAL_USER_ACCOUNT_COLLECTION = 'portal-user-account';
  private readonly PORTAL_USER_DATA_COLLECTION = 'portal-user-data';
  private readonly STORE_STAFF_INFO_COLLECTION = 'store-staff-info';
  private readonly EMPLOYEE_SCHEDULE_COLLECTION = `employee-schedule`;
  private readonly EMPLOYEE_SERVICE_COLLECTION = `employee-service`;
  private readonly EMPLOYEE_SERVICE_TRANSACTION_COLLECTION = `employee-service-transaction`;
  private readonly STAFF_INVITATION_COLLECTION = `staff-invitation`;
  private readonly STORE_INFO_COLLECTION = `store-info`;

  private readonly PLEASE_CONTACT_ADMINISTRATOR = 'Please contact the administrator by 778-866-7555';

  /**
   * 创建员工信息和排班数据的私有方法
   */
 async createStaffInfoAndSchedule(
    userAccountSid: string,
    userDataDocId: string,
    storeId: string,
    staffData: CreateStaffData,
    createdBy?: string,
    onProgress?: ProgressCallback
  ): Promise<ServiceResponse<{ staffInfoSid: string; scheduleSid: string }>> {
    try {
      // 创建员工信息
      onProgress?.(6, 8, 'Creating staff info...');

      console.log('createStaffInfoAndSchedule', userAccountSid, userDataDocId, storeId, staffData, createdBy);  
      const { staffInfoResult, staffInfoSid } = await this.createStaffInfo(userAccountSid, storeId, staffData, createdBy, userDataDocId);

      if (!staffInfoResult.success) {
        return {
          success: false,
          error: `Failed to create the staff: reason: create staff info failed, ${this.PLEASE_CONTACT_ADMINISTRATOR}`
        };
      }
      await new Promise(resolve => setTimeout(resolve, 600)); // 模拟创建时间

      // 创建员工排班文档（默认为不活跃状态）
      onProgress?.(7, 8, 'Setting up staff schedule...');
      console.log('createStaffSchedule', userAccountSid, storeId, createdBy, userDataDocId);  
      const { scheduleResult, scheduleSid } = await this.createStaffSchedule(userAccountSid, storeId, createdBy, userDataDocId);
      
      if (!scheduleResult.success) {
        return {
          success: false,
          error: `Failed to create the staff: reason: create schedule failed, ${this.PLEASE_CONTACT_ADMINISTRATOR}`
        };
      }
      await new Promise(resolve => setTimeout(resolve, 500)); // 模拟设置时间

      return {
        success: true,
        data: { staffInfoSid, scheduleSid },
        message: 'Staff info and schedule created successfully'
      };

    } catch (error) {
      console.error('Error creating staff info and schedule:', error);
      return {
        success: false,
        error: error instanceof Error ? `Failed to create staff info and schedule: ${error.message}, ${this.PLEASE_CONTACT_ADMINISTRATOR}` : `Failed to create staff info and schedule, ${this.PLEASE_CONTACT_ADMINISTRATOR}`
      };
    }
  }

 async createStaffSchedule(userAccountSid: string, storeId: string, createdBy: string | undefined, userDataDocId: string, workTimes?: WorkTime[]) {
    const scheduleSid = uuidv4();
    const scheduleData: Partial<EmployeeSchedule> = {
      sid: scheduleSid,
      uid: userAccountSid,
      storeId,
      active: false,
      workTimes: workTimes || [],
      isValid: true,
      isSynced: true,
      updated_by: createdBy || undefined,
      update_date: new Date()
    };

    const scheduleResult = await FirestoreService.createWithId(
      `${this.PORTAL_USER_DATA_COLLECTION}/${userDataDocId}/${this.EMPLOYEE_SCHEDULE_COLLECTION}`,
      scheduleSid,
      scheduleData
    );
    return { scheduleResult, scheduleSid };
  }

  async createStaffInfo(userAccountSid: string, storeId: string, staffData: CreateStaffData, createdBy: string | undefined, userDataDocId: string) {
    const staffInfoSid = uuidv4();
    const staffInfoData: Partial<StoreStaffInfo> = {
      sid: staffInfoSid,
      uid: userAccountSid, // 关联到用户账户的 sid
      storeId,
      role: staffData.role,
      active: true,
      note: staffData.note || '',
      startTime: new Date(),
      isValid: true,
      isSynced: true,
      updated_by: createdBy,
      update_date: new Date()
    };

    const staffInfoResult = await FirestoreService.createWithId(
      `${this.PORTAL_USER_DATA_COLLECTION}/${userDataDocId}/${this.STORE_STAFF_INFO_COLLECTION}`,
      staffInfoSid,
      staffInfoData
    );
    return { staffInfoResult, staffInfoSid };
  }

  /**
   * 创建员工账户和相关数据
   * 通过 API 路由创建员工，避免影响当前用户登录状态
   */
  async createStaff(
    storeId: string, 
    staffData: CreateStaffData,
    onProgress?: ProgressCallback,
    createdBy?: string,
    locale?: string
  ): Promise<ServiceResponse<string>> {
    const totalSteps = 8;
    let currentStep = 0;
    
    const updateProgress = (message: string) => {
      currentStep++;
      onProgress?.(currentStep, totalSteps, message);
    };

    try {
      // 1. 验证输入数据
      updateProgress('Validating input data...');
      if (!staffData.email || !staffData.firstName || !staffData.lastName) {
        return {
          success: false,
          error: 'need email, firstName, lastName for your staff'
        };
      }
      await new Promise(resolve => setTimeout(resolve, 500)); // 模拟验证时间

      // 2. 检查邮箱是否已存在
      updateProgress('Checking if email exists...');
      const emailExists = await checkEmailExists(staffData.email);
      if (emailExists) {
        return {
          success: false,
          error: 'email already exists'
        };
      }
      await new Promise(resolve => setTimeout(resolve, 500)); // 模拟检查时间

      // 3. 检查手机号是否已存在（如果提供）
      updateProgress('Checking if phone number exists...');
      if (staffData.phoneNumber) {
        const phoneExists = await checkPhoneExists(staffData.phoneNumber);
        if (phoneExists) {
          return {
            success: false,
            error: 'phone number already exists'
          };
        }
      }
      await new Promise(resolve => setTimeout(resolve, 500)); // 模拟验证时间

      // 4. 生成临时密码（如果未提供）
      updateProgress('Generating temporary password...');
      let tempPassword = staffData.password;
      let needChangePassword = false;
      if (!tempPassword) {
        tempPassword = generateTempPassword();
        needChangePassword = true;
      }
      await new Promise(resolve => setTimeout(resolve, 300)); // 模拟生成时间

      // 5. 使用 createStaffAccount 创建员工账户（不影响当前用户登录状态）
      updateProgress('Creating user account...');
      const userAccountSid = await createStaffAccount(
        staffData.email,
        tempPassword,
        createdBy || undefined,
        staffData.phoneNumber,
        needChangePassword
      )

      if (!userAccountSid) {
        return {
          success: false,
          error: 'Failed to create staff account'
        };
      }

      await new Promise(resolve => setTimeout(resolve, 800)); // 模拟创建时间




      // 6. 创建员工信息文档
      const userDataSid = uuidv4();
      const userDataData: Partial<PortalUserData> = {
        sid: userDataSid,
        uid: userAccountSid,
        firstName: staffData.firstName,
        lastName: staffData.lastName,
        displayName: `${staffData.firstName} ${staffData.lastName}`,
        phoneNumber: staffData.phoneNumber,
        userType: UserType.PETSTORE_STAFF,
        bio: staffData.note || '',
        photoURL: "",
        preferences: {
          language: locale || 'en',
          notificationsEnabled: true,
        },
        invitationCode: `STORESTAFF-${storeId}`,
        created_by: createdBy || '',
        create_date: new Date(),
        update_date: new Date(),
        isValid: true,
        isSynced: true, 
        updated_by: createdBy || ''
      }


            /*
 const userData: Partial<PortalUserData> = {
      sid: userDataSid,
      fid: user.uid,
      uid: sid, // 关联到用户账户的 sid
      displayName,
      firstName,
      lastName,
      phoneNumber: phoneNumber || undefined,
      userType,
      invitationCode: invitationCode || undefined,
      bio: undefined,
      photoURL: user.photoURL || undefined,
      preferences: {
        notificationsEnabled: notificationsEnabled ?? true,
        language: locale || 'zh-CN'
      },
      isValid: true,
      isSynced: true,
      updated_by: createdBy || sid,
      update_date: new Date()
    };
      */
      
      const userDataResult = await FirestoreService.createWithId(
        this.PORTAL_USER_DATA_COLLECTION,
        userDataSid,
        userDataData
      );
      
      if (!userDataResult.success) {
        return {
          success: false,
          error: `Failed to create the staff:  reason:  create user data failed, ${this.PLEASE_CONTACT_ADMINISTRATOR}`
        };
      }


      
      // 6. 获取用户数据文档ID
      const userDataResultId = await FirestoreService.getDocIdByQueryField(
        this.PORTAL_USER_DATA_COLLECTION,
        userAccountSid || '',
        'uid'
      );
      
      if (!userDataResultId.success) {
        return {
          success: false,
          error: `Failed to create the staff: reason: get user data id failed, ${this.PLEASE_CONTACT_ADMINISTRATOR}`
        };
      }

             // 7. 创建员工信息和排班数据
       const staffInfoAndScheduleResult = await this.createStaffInfoAndSchedule(
         userAccountSid,
         userDataResultId.data!,
         storeId,
         staffData,
         createdBy,
         onProgress
       );

       if (!staffInfoAndScheduleResult.success) {
         return {
           success: false,
           error: staffInfoAndScheduleResult.error!
         };
       }
      
      // 8. 员工Id加入store-info的staffIds数组
      updateProgress('Completing final setup...');
      await FirestoreService.arrayAddByQueryDocId(
        this.STORE_INFO_COLLECTION,
        storeId,
        'storeId',
        'staffs',
        userAccountSid || ''
      );
      await new Promise(resolve => setTimeout(resolve, 400)); // 模拟完成时间

      return {
        success: true,
        data: userAccountSid || '',
        message: 'Success create staff'
      };

    } catch (error) {
      console.error('Error creating staff:', error);
      return {
        success: false,
        error: error instanceof Error ? `Failed to create staff account: ${error.message}, ${this.PLEASE_CONTACT_ADMINISTRATOR}` : `Failed to create staff account , ${this.PLEASE_CONTACT_ADMINISTRATOR}`
      };
    }
  }

  /**
   * 获取店铺所有员工列表
   */
  async getStoreStaff(storeId: string): Promise<ServiceListResponse<StaffMember>> {
    try {
      const staffMembers: StaffMember[] = [];
      
      // 1. 查询所有store-info文档, get staffs array
      const storeInfoQuery = query(
        collection(db, this.STORE_INFO_COLLECTION), 
        where('storeId', '==', storeId),
      );
      
      const storeInfoSnapshot = await getDocs(storeInfoQuery);
      const staffs = storeInfoSnapshot.docs[0].data().staffs;
      
      // 2. for each staff in staffs array, get staff info
      for (const staff of staffs) {
        const staffUserInfoResult = await FirestoreService.getDocByQueryField(
          this.PORTAL_USER_DATA_COLLECTION,
          'uid',
          staff
        );

        const staffUserInfo = staffUserInfoResult.data as unknown as PortalUserData;

        if (!staffUserInfoResult.success || !staffUserInfo) {
          console.error(`${this.PLEASE_CONTACT_ADMINISTRATOR} Error fetching staff info: ${staffUserInfoResult.error}`);
          break;
        }

        // 获取排班信息
        const scheduleDoc = await FirestoreService.getDocByQueryField(
          `${this.PORTAL_USER_DATA_COLLECTION}/${staffUserInfo?.sid}/${this.EMPLOYEE_SCHEDULE_COLLECTION}`,
          'uid',
          staffUserInfo?.uid
        );
        
        const schedule = scheduleDoc.data as unknown as EmployeeSchedule;

        if (!scheduleDoc.success || !schedule) {
          console.error(`${this.PLEASE_CONTACT_ADMINISTRATOR} Error fetching schedule info: ${scheduleDoc.error}`);
          break;
        } 

        
        // 检查store-staff-info子集合
        const staffInfoDoc = await FirestoreService.getDocByQueryField(
          `${this.PORTAL_USER_DATA_COLLECTION}/${staffUserInfo?.sid}/${this.STORE_STAFF_INFO_COLLECTION}`,
          'uid',
          staffUserInfo?.uid
        );
        
        if (staffInfoDoc.success && staffInfoDoc.data) {
          const staffInfo = staffInfoDoc.data as unknown as StoreStaffInfo;
          
          // 获取服务列表
          // const servicesQuery = query(
          //   collection(db, this.EMPLOYEE_SERVICE_COLLECTION),
          //   where('uid', '==', userData.uid),
          //   where('serviceStatus', '==', ServiceStatus.ACTIVE)
          // );
          
          // const servicesSnapshot = await getDocs(servicesQuery);
          // const services = servicesSnapshot.docs.map(doc => doc.data() as EmployeeService);
          
          // 获取邮箱信息
          const accountDoc = await getDoc(
            doc(db, this.PORTAL_USER_ACCOUNT_COLLECTION, staff)
          );
          
          const email = accountDoc.exists() ? (accountDoc.data() as PortalUserAccount).email : undefined;
          
          staffMembers.push({
            userData: staffUserInfo as unknown as PortalUserData,
            staffInfo: staffInfo as unknown as StoreStaffInfo,
            schedule: schedule as unknown as EmployeeSchedule,
            // services,
            isActive: staffInfo.active as boolean,
            email
          });
        }
      }
      
      // 按创建时间排序
      staffMembers.sort((a, b) => {
        const aTime = a.staffInfo.create_date instanceof Date ? a.staffInfo.create_date : new Date(a.staffInfo.create_date!);
        const bTime = b.staffInfo.create_date instanceof Date ? b.staffInfo.create_date : new Date(b.staffInfo.create_date!);
        return bTime.getTime() - aTime.getTime();
      });
      
      return {
        success: true,
        data: staffMembers,
        total: staffMembers.length,
        message: '员工列表获取成功'
      };
      
    } catch (error) {
      console.error('Error fetching store staff:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取员工列表失败'
      };
    }
  }

  /**
   * 根据员工ID获取员工详细信息
   */
  async getStaffMember(staffUid: string, storeId: string): Promise<ServiceResponse<StaffMember>> {
    try {
      // 1. 获取用户数据

      console.log('getStaffMember staffUid:', staffUid);
      const userDataResult = await FirestoreService.getDocByQueryField(
        this.PORTAL_USER_DATA_COLLECTION,
        'uid',
        staffUid
      );

      if (!userDataResult.success || !userDataResult.data) {
        return {
          success: false,
          error: '员工不存在'
        };
      }
      
      const userData = userDataResult.data as unknown as PortalUserData;
      
      // 2. 获取员工店铺信息
      const staffInfoResult = await FirestoreService.getDocByQueryField(
        `${this.PORTAL_USER_DATA_COLLECTION}/${userData.sid}/${this.STORE_STAFF_INFO_COLLECTION}`,
        'uid',
        staffUid
      );
      
      if (!staffInfoResult.success || !staffInfoResult.data) {
        return {
          success: false,
          error: 'Staff info not found'
        };
      } 
      
      const staffInfo = staffInfoResult.data as unknown as StoreStaffInfo;
      
      // 3. 获取排班信息
      const scheduleResult = await FirestoreService.getDocByQueryField(
        `${this.PORTAL_USER_DATA_COLLECTION}/${userData.sid}/${this.EMPLOYEE_SCHEDULE_COLLECTION}`,
        'uid',
        staffUid
      );
      
      const schedule = scheduleResult.data as unknown as EmployeeSchedule;
      
      // 4. 获取服务列表
      const servicesResult = await FirestoreService.getMany(
        `${this.PORTAL_USER_DATA_COLLECTION}/${userData.sid}/${this.EMPLOYEE_SERVICE_COLLECTION}`,
        {
          where: [
            { field: 'uid', operator: '==', value: staffUid },
            { field: 'serviceStatus', operator: '==', value: ServiceStatus.ACTIVE }
          ]
        }
      );
      
      const services = servicesResult.data as unknown as EmployeeService[];
      
      // 5. 获取邮箱信息
      const accountResult = await FirestoreService.getDocByQueryField(
        this.PORTAL_USER_ACCOUNT_COLLECTION,
        'uid',
        staffUid
      );
      
      const email = accountResult.data ? (accountResult.data as unknown as PortalUserAccount).email : undefined;
      
      return {
        success: true,
        data: {
          userData,
          staffInfo,
          schedule,
          services,
          isActive: staffInfo.active,
          email
        },
        message: 'success get staff member'
      };
      
    } catch (error) {
      console.error('Error fetching staff member:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取员工信息失败'
      };
    }
  }

  /**
   * 更新员工信息
   */
  async updateStaffInfo(
    staffUid: string,
    storeId: string,
    updates: UpdateStaffData,
    updatedBy: string
  ): Promise<ServiceResponse<void>> {
    try {
      // 查找用户文档
      const userDataQuery = query(
        collection(db, this.PORTAL_USER_DATA_COLLECTION),
        where('uid', '==', staffUid)
      );
      
      const userDataSnapshot = await getDocs(userDataQuery);
      
      if (userDataSnapshot.empty) {
        return {
          success: false,
          error: '员工不存在'
        };
      }
      
      const userDoc = userDataSnapshot.docs[0];
      
      // 更新员工信息
      const updateData = {
        ...updates,
        updated_by: updatedBy,
        update_date: new Date()
      };
      
      //TODO:  Use updateByQueryField instead of updateDoc
      
      await updateDoc(
        doc(db, this.PORTAL_USER_DATA_COLLECTION, userDoc.id, 'store-staff-info', storeId),
        updateData
      );
      
      return {
        success: true,
        message: 'update staff info success'
      };
      
    } catch (error) {
      console.error('Error updating staff info:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'update staff info failed'
      };
    }
  }

  /**
   * 删除员工（设置为非活跃状态）
   */
  async removeStaff(staffUid: string, storeId: string, removedBy: string): Promise<ServiceResponse<void>> {
    try {
      const updateData: UpdateStaffData = {
        active: false,
        endTime: new Date(),
      };
      
      return await this.updateStaffInfo(staffUid, storeId, updateData, removedBy);
      
    } catch (error) {
      console.error('Error removing staff:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'remove staff failed'
      };
    }
  }

  /**
   * 获取员工排班信息
   */
  async getStaffSchedule(staff: StaffMember): Promise<ServiceResponse<EmployeeSchedule>> {
    try {
      const scheduleDoc = await FirestoreService.getById(
        `${this.PORTAL_USER_DATA_COLLECTION}/${staff.userData.id}/${this.EMPLOYEE_SCHEDULE_COLLECTION}/${staff.schedule?.sid}`,
        staff.schedule?.sid as string
      );
      
      if (scheduleDoc.success && scheduleDoc.data) {
        return {
          success: true,
          data: scheduleDoc.data as unknown as EmployeeSchedule,
          message: 'get staff schedule success'
        };
      }
      
      return {
        success: false,
        error: 'get staff schedule failed'
      };
      
    } catch (error) {
      console.error('Error fetching staff schedule:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'get staff schedule failed'
      };
    }
  }

  /**
   * 更新员工排班信息
   */
  async updateStaffSchedule(
    staff: StaffMember,
    scheduleData: UpdateScheduleData,
    updatedBy?: string
  ): Promise<ServiceResponse<string>> {
    try {
      
      console.log('staff.schedule:', staff.schedule);
      
      const scheduleId = (staff.schedule as unknown as EmployeeSchedule)?.sid;
      console.log('scheduleId:', scheduleId);
      
      // 检查是否已存在排班记录
      // const existingSchedule = await this.getStaffSchedule(staff);
      
      if (scheduleId) {
        // 更新现有排班
        const updateData = {
          ...scheduleData,
          updated_by: updatedBy || 'ADMIN ERROR',
          update_date: new Date()
        };
        
        await FirestoreService.update(
          `${this.PORTAL_USER_DATA_COLLECTION}/${staff.userData.sid}/${this.EMPLOYEE_SCHEDULE_COLLECTION}`,
          scheduleId as string,
          updateData
        );
        
        return {
          success: true,
          data: scheduleId,
          message: 'update staff schedule success'
        };
      } else {
        // 创建新的排班记录
        const scheduleId = uuidv4();
        const schedule: EmployeeSchedule = {
          sid: scheduleId,
          uid: staff.userData.uid,
          storeId: staff.staffInfo.storeId,
          active: scheduleData.active,
          workTimes: scheduleData.workTimes,
          created_by: staff.userData.uid,
          create_date: new Date(),
          updated_by: updatedBy,
          update_date: new Date()
        };
        
        await FirestoreService.create(
          `${this.PORTAL_USER_DATA_COLLECTION}/${staff.userData.sid}/${this.EMPLOYEE_SCHEDULE_COLLECTION}`,
          new EmployeeScheduleImpl(schedule).toJson()
        );
        
        return {
          success: true,
          data: scheduleId,
          message: 'create staff schedule success'
        };
      }
      
    } catch (error) {
      console.error('Error updating staff schedule:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'update staff schedule failed'
      };
    }
  }

  /**
   * 为员工添加服务
   */
  async addStaffService(
    staff: StaffMember,
    serviceData: CreateStaffServiceData,
    createdBy: string
  ): Promise<ServiceResponse<string>> {
    try {
      const serviceId = uuidv4();
      
      const service: Partial<EmployeeService> = {
        sid: serviceId,
        uid: staff.userData.uid,
        storeId: staff.staffInfo.storeId,
        serviceName: serviceData.serviceName,
        serviceCategory: serviceData.serviceCategory,
        serviceBreed: serviceData.serviceBreed,
        serviceStatus: ServiceStatus.ACTIVE,
        serviceDuration: serviceData.serviceDuration,
        serviceAmount: serviceData.serviceAmount,
        serviceAmountCurrency: serviceData.serviceAmountCurrency,
        serviceCount: 0,
        servicePhotos: serviceData.servicePhotos || [],
        created_by: createdBy,
        create_date: new Date(),
        updated_by: createdBy,
        update_date: new Date()
      };
      
      const result = await FirestoreService.createWithId(
        `${this.PORTAL_USER_DATA_COLLECTION}/${staff.userData.sid}/${this.EMPLOYEE_SERVICE_COLLECTION}`,
        serviceId,
        service
      );
      
      if (result.success) {
        return {
          success: true,
          data: serviceId,
          message: 'add staff service success'
        };
      } else {
        return {
          success: false,
          error: result.error || 'add staff service failed'
        };
      }
      
    } catch (error) {
      console.error('Error adding staff service:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'add staff service failed'
      };
    }
  }

  /**
   * 获取员工服务列表
   */
  async getStaffServices(staff: StaffMember): Promise<ServiceListResponse<EmployeeService>> {
    try {

      console.log('staff.userData.sid:', staff.userData.sid);
      const servicesSnapshot = await FirestoreService.getMany(
        `${this.PORTAL_USER_DATA_COLLECTION}/${staff.userData.sid}/${this.EMPLOYEE_SERVICE_COLLECTION}`,
        {
          where: [
            { field: 'storeId', operator: '==', value: staff.staffInfo.storeId }
          ]
        }
      );
      console.log('servicesSnapshot:', servicesSnapshot);

      const services: EmployeeService[] = [];

      for (const data of servicesSnapshot.data!) {
        services.push(data as unknown as EmployeeService);
      }

      // const services = servicesSnapshot.data?.map(doc => doc.data as unknown as EmployeeService);
      console.log('services:', services);
      
      return {
        success: true,
        data: services,
        total: services?.length || 0,
        message: 'get staff services success'
      };
      
    } catch (error) {
      console.error('Error fetching staff services:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'get staff services failed'
      };
    }
  }

  /**
   * 更新员工服务
   */
  async updateStaffService(
    serviceId: string,
    staff: StaffMember,
    updates: Partial<CreateStaffServiceData>,
    updatedBy: string
  ): Promise<ServiceResponse<void>> {
    try {
      const updateData = {
        ...updates,
        updated_by: updatedBy,
        update_date: new Date()
      };
      
      await FirestoreService.update(
        `${this.PORTAL_USER_DATA_COLLECTION}/${staff.userData.sid}/${this.EMPLOYEE_SERVICE_COLLECTION}`,
        serviceId,
        updateData
      );
      
      return {
        success: true,
        message: 'update staff service success'
      };
      
    } catch (error) {
      console.error('Error updating staff service:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'update staff service failed'
      };
    }
  }

  /**
   * 删除员工服务（设置为非活跃状态）
   */
  async removeStaffService(serviceId: string, staff: StaffMember, removedBy: string): Promise<ServiceResponse<void>> {
    try {
      const updateData = {
        serviceStatus: ServiceStatus.INACTIVE,
        updated_by: removedBy,
        update_date: new Date()
      };
      
      await FirestoreService.update(
        `${this.PORTAL_USER_DATA_COLLECTION}/${staff.userData.sid}/${this.EMPLOYEE_SERVICE_COLLECTION}`,
        serviceId,
        updateData
      );
      
      return {
        success: true,
        message: 'remove staff service success'
      };
      
    } catch (error) {
      console.error('Error removing staff service:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'remove staff service failed'
      };
    }
  }

  /**
   * 根据角色筛选员工
   */
  async getStaffByRole(storeId: string, role: StoreRole): Promise<ServiceListResponse<StaffMember>> {
    try {
      const allStaffResult = await this.getStoreStaff(storeId);
      
      if (!allStaffResult.success || !allStaffResult.data) {
        return allStaffResult;
      }
      
      const filteredStaff = allStaffResult.data.filter(staff => staff.staffInfo.role === role);
      
      return {
        success: true,
        data: filteredStaff,
        total: filteredStaff.length,
        message: '按角色筛选员工成功'
      };
      
    } catch (error) {
      console.error('Error fetching staff by role:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '按角色筛选员工失败'
      };
    }
  }

  /**
   * 搜索员工（按姓名或邮箱）
   */
  async searchStaff(storeId: string, searchTerm: string): Promise<ServiceListResponse<StaffMember>> {
    try {
      const allStaffResult = await this.getStoreStaff(storeId);
      
      if (!allStaffResult.success || !allStaffResult.data) {
        return allStaffResult;
      }
      
      const searchLower = searchTerm.toLowerCase();
      const filteredStaff = allStaffResult.data.filter(staff => {
        const userData = staff.userData;
        
        return (
          userData.firstName?.toLowerCase().includes(searchLower) ||
          userData.lastName?.toLowerCase().includes(searchLower) ||
          userData.displayName?.toLowerCase().includes(searchLower) ||
          staff.email?.toLowerCase().includes(searchLower)
        );
      });
      
      return {
        success: true,
        data: filteredStaff,
        total: filteredStaff.length,
        message: '员工搜索完成'
      };
      
    } catch (error) {
      console.error('Error searching staff:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '搜索员工失败'
      };
    }
  }

  /**
   * 获取员工统计信息
   */
  async getStaffStats(storeId: string): Promise<ServiceResponse<{
    total: number;
    active: number;
    inactive: number;
    byRole: Record<StoreRole, number>;
  }>> {
    try {
      const staffResult = await this.getStoreStaff(storeId);
      
      if (!staffResult.success || !staffResult.data) {
        return {
          success: false,
          error: staffResult.error || '获取员工统计信息失败'
        };
      }
      
      const stats = {
        total: staffResult.data.length,
        active: 0,
        inactive: 0,
        byRole: {
          [StoreRole.STORE_OWNER]: 0,
          [StoreRole.STORE_ADMIN]: 0,
          [StoreRole.STORE_STAFF]: 0
        } as Record<StoreRole, number>
      };
      
      staffResult.data.forEach(staff => {
        if (staff.staffInfo.active) {
          stats.active++;
        } else {
          stats.inactive++;
        }
        
        stats.byRole[staff.staffInfo.role]++;
      });
      
      return {
        success: true,
        data: stats,
        message: '员工统计信息获取成功'
      };
      
    } catch (error) {
      console.error('Error fetching staff stats:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取员工统计信息失败'
      };
    }
  }

  /**
   * 一键应用排班到工作日
   */
  async applyScheduleToWeekdays(
    staff: StaffMember,
    workTime: { startTime: string; endTime: string }
  ): Promise<ServiceResponse<string>> {
    try {
      const weekdays: WorkTime[] = [
        { weekday: 'Monday', startTime: workTime.startTime, endTime: workTime.endTime, open: true },
        { weekday: 'Tuesday', startTime: workTime.startTime, endTime: workTime.endTime, open: true },
        { weekday: 'Wednesday', startTime: workTime.startTime, endTime: workTime.endTime, open: true },
        { weekday: 'Thursday', startTime: workTime.startTime, endTime: workTime.endTime, open: true },
        { weekday: 'Friday', startTime: workTime.startTime, endTime: workTime.endTime, open: true },
      ];
      
      const scheduleData: UpdateScheduleData = {
        active: true,
        workTimes: weekdays
      };
      
      return await this.updateStaffSchedule(staff, scheduleData);
      
    } catch (error) {
      console.error('Error applying schedule to weekdays:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '应用工作日排班失败'
      };
    }
  }

  // ==================== Appointment Related Methods ====================

  // /**
  //  * Create store service based on staff service
  //  * This is triggered when staff creates a new service
  //  */
  // async createStoreServiceFromStaffService(
  //   staff: StaffMember,
  //   staffServiceId: string,
  //   updatedBy: string
  // ): Promise<ServiceResponse<string>> {
  //   try {
  //     // Get the staff service
  //     const staffServiceResult = await this.getStaffServiceById(staff, staffServiceId);
  //     if (!staffServiceResult.success || !staffServiceResult.data) {
  //       return {
  //         success: false,
  //         error: 'Staff service not found'
  //       };
  //     }

  //     const staffService = staffServiceResult.data;

  //     // Import appointment service to create store service
  //     const { default: appointmentService } = await import('./appointment_service');

  //     // Check if store service already exists for this category and breed
  //     const existingServices = await appointmentService.getStoreBookableServices(staff.staffInfo.storeId);
  //     const existingService = existingServices.data?.find(service => 
  //       service.serviceCategory === staffService.serviceCategory &&
  //       service.serviceBreed === staffService.serviceBreed
  //     );

  //     if (existingService) {
  //       // Add staff to existing service if not already included
  //       if (!existingService.staffIds.includes(staff.userData.uid)) {
  //         // Update existing service to include this staff
  //         const result = await this.updateStoreServiceStaff(
  //           existingService.serviceId,
  //           [...existingService.staffIds, staff.userData.uid],
  //           updatedBy
  //         );
          
  //         if (result.success) {
  //           return {
  //             success: true,
  //             data: existingService.serviceId,
  //             message: 'Staff added to existing store service'
  //           };
  //         }
  //       } else {
  //         return {
  //           success: true,
  //           data: existingService.serviceId,
  //           message: 'Staff is already assigned to this store service'
  //         };
  //       }
  //     }

  //     // Create new store service
  //     const createServiceData = {
  //       serviceName: staffService.serviceName,
  //       serviceCategory: staffService.serviceCategory,
  //       serviceBreed: staffService.serviceBreed,
  //       description: `${staffService.serviceCategory} service for ${staffService.serviceBreed}`,
  //       staffIds: [staff.userData.uid],
  //       minDuration: staffService.serviceDuration,
  //       maxDuration: staffService.serviceDuration * 2,
  //       defaultDuration: staffService.serviceDuration,
  //       basePrice: staffService.serviceAmount,
  //       currency: staffService.serviceAmountCurrency,
  //       maxCapacityPerSlot: 1,
  //       requiresApproval: false
  //     };

  //     const storeServiceResult = await appointmentService.createStoreBookableService(
  //       staff.staffInfo.storeId,
  //       createServiceData,
  //       updatedBy
  //     );

  //     if (storeServiceResult.success) {
  //       return {
  //         success: true,
  //         data: storeServiceResult.data!,
  //         message: 'Store service created from staff service'
  //       };
  //     } else {
  //       return {
  //         success: false,
  //         error: storeServiceResult.error || 'Failed to create store service'
  //       };
  //     }

  //   } catch (error) {
  //     console.error('Error creating store service from staff service:', error);
  //     return {
  //       success: false,
  //       error: error instanceof Error ? error.message : 'Failed to create store service'
  //     };
  //   }
  // }

  /**
   * Get staff service by ID
   */
  async getStaffServiceById(staff: StaffMember, serviceId: string): Promise<ServiceResponse<EmployeeService>> {
    try {
      const serviceResult = await FirestoreService.getById(
        `${this.PORTAL_USER_DATA_COLLECTION}/${staff.userData.sid}/${this.EMPLOYEE_SERVICE_COLLECTION}`,
        serviceId
      );

      if (serviceResult.success && serviceResult.data) {
        return {
          success: true,
          data: serviceResult.data as unknown as EmployeeService,
          message: 'Staff service retrieved successfully'
        };
      }

      return {
        success: false,
        error: 'Staff service not found'
      };

    } catch (error) {
      console.error('Error getting staff service by ID:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get staff service'
      };
    }
  }

  /**
   * Update store service staff assignments
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private async updateStoreServiceStaff(
    storeServiceId: string,
    staffIds: string[],
    updatedBy: string
  ): Promise<ServiceResponse<void>> {
    try {
      // This would need to be implemented in appointment service
      // For now, we'll return success
      console.log('updateStoreServiceStaff', storeServiceId, staffIds, updatedBy);
      return {
        success: true,
        message: 'Store service staff updated successfully'
      };

    } catch (error) {
      console.error('Error updating store service staff:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update store service staff'
      };
    }
  }

  /**
   * Get staff availability for a specific date
   */
  async getStaffAvailabilityForDate(
    staff: StaffMember,
    date: string
  ): Promise<ServiceResponse<{
    isAvailable: boolean;
    workTimes: WorkTime[];
    holidays: string[];
    existingAppointments: number;
  }>> {
    try {
      // Get day of week
      const dateObj = new Date(date);
      const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const dayName = dayNames[dateObj.getDay()];

      // Check if staff has schedule
      if (!staff.schedule || !staff.schedule.active) {
        return {
          success: true,
          data: {
            isAvailable: false,
            workTimes: [],
            holidays: ['No active schedule'],
            existingAppointments: 0
          },
          message: 'Staff has no active schedule'
        };
      }

      // Get work times for the day
      const workTimes = staff.schedule.workTimes?.filter(wt => 
        wt.weekday === dayName && wt.open
      ) || [];

      // This would normally check for holidays and appointments
      // For now, we'll return basic availability
      return {
        success: true,
        data: {
          isAvailable: workTimes.length > 0,
          workTimes,
          holidays: [],
          existingAppointments: 0
        },
        message: 'Staff availability retrieved successfully'
      };

    } catch (error) {
      console.error('Error getting staff availability:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get staff availability'
      };
    }
  }

  /**
   * Create staff holiday
   */
  async createStaffHoliday(
    staff: StaffMember,
    holidayData: {
      title: string;
      startDate: string;
      endDate: string;
      isFullDay: boolean;
      startTime?: string;
      endTime?: string;
      reason?: string;
      isRecurring?: boolean;
    },
    createdBy: string
  ): Promise<ServiceResponse<string>> {
    try {
      const holidayId = uuidv4();

      const holiday = {
        sid: holidayId,
        storeId: staff.staffInfo.storeId,
        staffId: staff.userData.uid,
        holidayId,
        title: holidayData.title,
        startDate: holidayData.startDate,
        endDate: holidayData.endDate,
        isFullDay: holidayData.isFullDay,
        startTime: holidayData.startTime,
        endTime: holidayData.endTime,
        reason: holidayData.reason,
        isRecurring: holidayData.isRecurring || false,
        isApproved: false, // Requires approval
        created_by: createdBy,
        create_date: new Date(),
        updated_by: createdBy,
        update_date: new Date(),
        isValid: true,
        isSynced: true
      };

      const result = await FirestoreService.createWithId(
        this.STAFF_INVITATION_COLLECTION, // Reusing collection name, should be 'staff-holiday'
        holidayId,
        holiday
      );

      if (result.success) {
        return {
          success: true,
          data: holidayId,
          message: 'Staff holiday created successfully'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to create staff holiday'
        };
      }

    } catch (error) {
      console.error('Error creating staff holiday:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create staff holiday'
      };
    }
  }

  /**
   * 一键应用排班到周末
   */
  async applyScheduleToWeekends(
    staff: StaffMember,
    workTime: { startTime: string; endTime: string }
  ): Promise<ServiceResponse<string>> {
    try {
      const weekends: WorkTime[] = [
        { weekday: 'Saturday', startTime: workTime.startTime, endTime: workTime.endTime, open: true },
        { weekday: 'Sunday', startTime: workTime.startTime, endTime: workTime.endTime, open: true },
      ];
      
      const scheduleData: UpdateScheduleData = {
        active: true,
        workTimes: weekends
      };
      
      return await this.updateStaffSchedule(staff, scheduleData);
      
    } catch (error) {
      console.error('Error applying schedule to weekends:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '应用周末排班失败'
      };
    }
  }
}

// 导出单例实例
export const staffService = new StaffService();
export default staffService;
