import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:onenata_app/models/bookable_service.dart';
import 'package:onenata_app/common/enum/enum_i.dart';

/// 可预约服务服务类
/// 负责获取和管理店铺的可预约服务
class BookableServiceService {
  static final BookableServiceService _instance = BookableServiceService._internal();
  factory BookableServiceService() => _instance;
  BookableServiceService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 获取店铺的所有可预约服务
  Future<List<BookableService>> getStoreBookableServices(String storeId) async {
    try {
      print('🔍 查询店铺服务，storeId: $storeId');

      // 方法1：尝试从独立的 store-services 集合查询
      QuerySnapshot querySnapshot = await _firestore
          .collection('store-services')
          .where('storeId', isEqualTo: storeId)
          .where('isValid', isEqualTo: true)
          .get();

      print('📊 独立collection查询结果: ${querySnapshot.docs.length} 个服务');

      // 如果独立collection没有数据，尝试从store-info子collection查询
      if (querySnapshot.docs.isEmpty) {
        print('🔍 尝试从store-info子collection查询...');

        // 先找到store-info文档
        final storeInfoQuery = await _firestore
            .collection('store-info')
            .where('storeId', isEqualTo: storeId)
            .limit(1)
            .get();

        if (storeInfoQuery.docs.isNotEmpty) {
          final storeInfoId = storeInfoQuery.docs.first.id;
          print('📋 找到store-info文档: $storeInfoId');

          querySnapshot = await _firestore
              .collection('store-info')
              .doc(storeInfoId)
              .collection('store-services')
              .where('isValid', isEqualTo: true)
              .get();

          print('📊 子collection查询结果: ${querySnapshot.docs.length} 个服务');
        }
      }

      final List<BookableService> services = [];

      for (final doc in querySnapshot.docs) {
        try {
          print('🔍 开始解析文档 ${doc.id}');
          final data = doc.data() as Map<String, dynamic>;
          print('📋 文档数据: ${data.toString()}');

          final service = BookableService.fromFirestoreData(doc);
          services.add(service);
          print('✅ 解析服务成功: ${service.serviceName}');
        } catch (e, stackTrace) {
          print('❌ 解析服务文档失败 ${doc.id}: $e');
          print('📍 错误堆栈: $stackTrace');
          // 继续处理其他文档
        }
      }

      print('🎯 最终返回 ${services.length} 个服务');
      return services;
    } catch (e) {
      print('❌ 获取店铺服务失败: $e');
      return [];
    }
  }

  /// 获取店铺的活跃可预约服务（仅返回可在线预约的服务）
  Future<List<BookableService>> getActiveBookableServices(String storeId) async {
    try {
      final allServices = await getStoreBookableServices(storeId);
      
      // 过滤出活跃且支持在线预约的服务
      return allServices.where((service) => 
        service.status == StoreServiceStatus.active &&
        service.isOnlineBookingEnabled &&
        service.staffIds.isNotEmpty
      ).toList();
    } catch (e) {
      print('Error getting active bookable services: $e');
      return [];
    }
  }

  /// 根据服务类别获取店铺服务
  Future<List<BookableService>> getServicesByCategory(
    String storeId, 
    ServiceCategory category
  ) async {
    try {
      final allServices = await getActiveBookableServices(storeId);
      
      return allServices.where((service) => 
        service.serviceCategory == category
      ).toList();
    } catch (e) {
      print('Error getting services by category: $e');
      return [];
    }
  }

  /// 根据服务对象获取店铺服务
  Future<List<BookableService>> getServicesByBreed(
    String storeId, 
    ServiceBreed breed
  ) async {
    try {
      final allServices = await getActiveBookableServices(storeId);
      
      return allServices.where((service) => 
        service.serviceBreed == breed || service.serviceBreed == ServiceBreed.other
      ).toList();
    } catch (e) {
      print('Error getting services by breed: $e');
      return [];
    }
  }

  /// 获取指定员工提供的服务
  Future<List<BookableService>> getServicesByStaff(
    String storeId, 
    String staffId
  ) async {
    try {
      final allServices = await getActiveBookableServices(storeId);
      
      return allServices.where((service) => 
        service.staffIds.contains(staffId)
      ).toList();
    } catch (e) {
      print('Error getting services by staff: $e');
      return [];
    }
  }

  /// 根据服务ID获取单个服务
  Future<BookableService?> getServiceById(String serviceId) async {
    try {
      final DocumentSnapshot doc = await _firestore
          .collection('bookable-service')
          .doc(serviceId)
          .get();

      if (doc.exists) {
        return BookableService.fromFirestoreData(doc);
      }
      
      return null;
    } catch (e) {
      print('Error getting service by ID: $e');
      return null;
    }
  }

  /// 创建测试数据（用于开发测试）
  Future<List<BookableService>> createTestServices(String storeId) async {
    final List<BookableService> testServices = [
      BookableService.create(
        storeId: storeId,
        serviceId: 'service_1',
        serviceName: 'Bath & Blow Dry',
        serviceCategory: ServiceCategory.grooming,
        serviceBreed: ServiceBreed.dog,
        description: 'Gentle wash and blow dry for your pet',
        minDuration: 30,
        maxDuration: 45,
        defaultDuration: 30,
        basePrice: 45.00,
        currency: Currency.cad,
        staffIds: ['staff_1', 'staff_2'],
        isOnlineBookingEnabled: true,
      ),
      BookableService.create(
        storeId: storeId,
        serviceId: 'service_2',
        serviceName: 'Hair Trimming',
        serviceCategory: ServiceCategory.grooming,
        serviceBreed: ServiceBreed.dog,
        description: 'Professional hair trimming and styling',
        minDuration: 45,
        maxDuration: 90,
        defaultDuration: 60,
        basePrice: 65.00,
        currency: Currency.cad,
        staffIds: ['staff_1'],
        isOnlineBookingEnabled: true,
      ),
      BookableService.create(
        storeId: storeId,
        serviceId: 'service_3',
        serviceName: 'Nail Clipping',
        serviceCategory: ServiceCategory.grooming,
        serviceBreed: ServiceBreed.dog,
        description: 'Gentle paw care and nail trimming',
        minDuration: 15,
        maxDuration: 30,
        defaultDuration: 20,
        basePrice: 25.00,
        currency: Currency.cad,
        staffIds: ['staff_2'],
        isOnlineBookingEnabled: true,
      ),
      BookableService.create(
        storeId: storeId,
        serviceId: 'service_4',
        serviceName: 'Teeth Brushing',
        serviceCategory: ServiceCategory.grooming,
        serviceBreed: ServiceBreed.dog,
        description: 'Promote good oral and dental health',
        minDuration: 20,
        maxDuration: 40,
        defaultDuration: 30,
        basePrice: 35.00,
        currency: Currency.cad,
        staffIds: ['staff_1', 'staff_2'],
        isOnlineBookingEnabled: true,
      ),
      BookableService.create(
        storeId: storeId,
        serviceId: 'service_5',
        serviceName: 'Full Grooming Package',
        serviceCategory: ServiceCategory.grooming,
        serviceBreed: ServiceBreed.dog,
        description: 'Complete grooming service including bath, trim, and nail care',
        minDuration: 90,
        maxDuration: 120,
        defaultDuration: 105,
        basePrice: 120.00,
        currency: Currency.cad,
        staffIds: ['staff_1'],
        isOnlineBookingEnabled: true,
      ),
    ];

    return testServices;
  }

  /// 获取服务的格式化信息
  Map<String, String> getServiceDisplayInfo(BookableService service) {
    return {
      'name': service.serviceName,
      'description': service.description ?? '',
      'duration': service.formattedDuration,
      'price': service.formattedPrice,
      'category': service.serviceCategory.name.toUpperCase(),
      'breed': service.serviceBreed.name.toUpperCase(),
      'availability': service.isAvailable ? 'Available' : 'Unavailable',
    };
  }

  /// 检查服务是否在指定时间可用
  Future<bool> isServiceAvailableAtTime(
    String serviceId, 
    DateTime date, 
    String timeSlot
  ) async {
    // 这里可以添加更复杂的可用性检查逻辑
    // 比如检查员工排班、已有预约等
    try {
      final service = await getServiceById(serviceId);
      return service?.isAvailable ?? false;
    } catch (e) {
      print('Error checking service availability: $e');
      return false;
    }
  }

  /// 获取服务的可用员工列表
  Future<List<String>> getAvailableStaffForService(String serviceId) async {
    try {
      final service = await getServiceById(serviceId);
      return service?.staffIds ?? [];
    } catch (e) {
      print('Error getting available staff: $e');
      return [];
    }
  }
}
