import { 
  doc, 
  getDoc, 
  getDocs, 
  setDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter, 
  endBefore,
  onSnapshot,
  writeBatch,
  increment,
  arrayUnion,
  arrayRemove,
  serverTimestamp,
  DocumentSnapshot,
  QueryDocumentSnapshot,
  QuerySnapshot,
  WhereFilterOp,
  OrderByDirection,
  Unsubscribe,
  DocumentData,
  UpdateData,
  // DocumentRef erence
} from 'firebase/firestore';
import { db } from '../config';

// 基础文档接口
export interface FirestoreDocument {
  id?: string;
  createdAt?: Date | null;
  updatedAt?: Date | null;
  [key: string]: unknown;
}

// 查询选项接口
export interface QueryOptions {
  where?: {
    field: string;
    operator: WhereFilterOp;
    value: unknown;
  }[];
  orderBy?: {
    field: string;
    direction?: OrderByDirection;
  }[];
  limit?: number;
  startAfter?: QueryDocumentSnapshot<DocumentData>;
  endBefore?: QueryDocumentSnapshot<DocumentData>;
}

// 响应接口
export interface FirestoreResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface FirestoreListResponse<T> {
  success: boolean;
  data?: T[];
  total?: number;
  hasMore?: boolean;
  lastDoc?: QueryDocumentSnapshot<DocumentData>;
  firstDoc?: QueryDocumentSnapshot<DocumentData>;
  error?: string;
  message?: string;
}

/**
 * Firestore 操作服务类
 */
export class FirestoreService {
  
  /**
   * 创建文档（自动生成ID）
   */
  static async create<T extends Record<string, unknown>>(
    collectionName: string, 
    data: T
  ): Promise<FirestoreResponse<string>> {
    try {
      const docData = {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      console.log('create docs', collectionName, docData);
      
      const docRef = await addDoc(collection(db, collectionName), docData);
      
      return {
        success: true,
        data: docRef.id,
        message: 'success'
      };
    } catch (error: unknown) {
      console.error('create document failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'create document failed'
      };
    }
  }

  /**
   * 创建文档（指定ID）
   */
  static async createWithId<T extends Record<string, unknown>>(
    collectionName: string, 
    docId: string, 
    data: T
  ): Promise<FirestoreResponse<string>> {
    try {
      const docData = {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };
      
      console.log('createWithId', collectionName, docId, docData);


      await setDoc(doc(db, collectionName, docId), docData);
      
      return {
        success: true,
        data: docId,
        message: 'success'
      };
    } catch (error: unknown) {
      console.error('create document failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'create document failed'
      };
    }
  }

  /**
   * 创建文档（with query doc id）
   */
  static async createWithQueryDocId<T extends Record<string, unknown>>(
    collectionName: string, 
    queryValue: string,
    queryField: string,
    data: T
  ): Promise<FirestoreResponse<string>> {
    try {
      const docData = {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };
      
      const queryRef = query(collection(db, collectionName), where(queryField, '==', queryValue));
      const querySnapshot = await getDocs(queryRef);
      const docRef = querySnapshot.docs[0];
      if (!docRef) {
        return {
          success: false,
          error: `document not found by query: ${queryValue} in ${queryField}`
        };
      }
      await setDoc(docRef.ref, docData);
      
      return {
        success: true,
        data: docRef.id,
        message: 'success'
      };

    } catch (error: unknown) {
      console.error('create document failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'create document failed'
      };
    }
  }


  /**
   * 获取单个文档
   */
  static async getById<T extends FirestoreDocument>(
    collectionName: string, 
    docId: string
  ): Promise<FirestoreResponse<T>> {
    try {
      const docRef = doc(db, collectionName, docId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const data = { id: docSnap.id, ...docSnap.data() } as T;
        return {
          success: true,
          data,
          message: 'success'
        };
      } else {
        return {
          success: false,
          error: 'document not found'
        };
      }
    } catch (error: unknown) {
        console.error('get document failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'get document failed'
      };
    }
  }

  /**
   * 获取单个文档ID（with query doc id）
   */
  static async getDocIdByQueryField(
    collectionName: string, 
    queryValue: string,
    queryField: string
  ): Promise<FirestoreResponse<string>> {
    try {
      const queryRef = query(collection(db, collectionName), where(queryField, '==', queryValue));
      const querySnapshot = await getDocs(queryRef);
      const docRef = querySnapshot.docs[0];
      if (!docRef) {
        return {
          success: false,
          error: `document not found by query: ${queryValue} in ${queryField}`
        };
      }

      return {
        success: true,
        data: docRef.id,
        message: 'success'
      };
    } catch (error: unknown) {
      console.error('get document id by query field failed:', error);
      return {  
        success: false,
        error: error instanceof Error ? error.message : 'get document id by query field failed'
      };
    }
  }

  static async getDocByQueryField<T extends FirestoreDocument>(
    collectionName: string, 
    field: string, 
    value: unknown
  ): Promise<FirestoreResponse<T>> {
    try {
      console.log('getDocByQueryField', collectionName, field, value);
      const queryRef = query(collection(db, collectionName), where(field, '==', value));
      const querySnapshot = await getDocs(queryRef);
      const documents: T[] = [];
      querySnapshot.forEach((doc) => {
        documents.push({ id: doc.id, ...doc.data() } as T);
      });
      return {
        success: true,
        data: documents[0],
        message: 'success'
      };
    } catch (error: unknown) {
      console.error('get document by field failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'get document by field failed'
      };
    }
  }


  

  /**
   * 获取多个文档
   */
  static async getMany<T extends FirestoreDocument>(
    collectionName: string, 
    options: QueryOptions = {}
  ): Promise<FirestoreListResponse<T>> {
    try {
      const collectionRef = collection(db, collectionName);
      let queryRef = query(collectionRef);

      // 添加 where 条件
      if (options.where) {
        for (const condition of options.where) {
          queryRef = query(queryRef, where(condition.field, condition.operator, condition.value));
        }
      }

      // 添加 orderBy 条件
      if (options.orderBy) {
        for (const order of options.orderBy) {
          queryRef = query(queryRef, orderBy(order.field, order.direction || 'asc'));
        }
      }

      // 添加分页条件
      if (options.startAfter) {
        queryRef = query(queryRef, startAfter(options.startAfter));
      }

      if (options.endBefore) {
        queryRef = query(queryRef, endBefore(options.endBefore));
      }

      // 添加限制条件
      if (options.limit) {
        queryRef = query(queryRef, limit(options.limit));
      }

      const querySnapshot = await getDocs(queryRef);
      const documents: T[] = [];
      
      querySnapshot.forEach((doc) => {
        documents.push({ id: doc.id, ...doc.data() } as T);
      });

      return {
        success: true,
        data: documents,
        total: documents.length,
        hasMore: documents.length === (options.limit || 0),
        lastDoc: querySnapshot.docs[querySnapshot.docs.length - 1],
        firstDoc: querySnapshot.docs[0],
        message: 'success'
      };
    } catch (error: unknown) {
      console.error('get document list failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'get document list failed'
      };
    }
  }

  /**
   * 更新文档
   */
  static async update<T extends Record<string, unknown>>(
    collectionName: string, 
    docId: string, 
    data: Partial<T>
  ): Promise<FirestoreResponse<void>> {
    try {
      const docRef = doc(db, collectionName, docId);
      const updateData = {
        ...data,
        updatedAt: serverTimestamp()
      };
      
      await updateDoc(docRef, updateData);
      
      return {
        success: true,
        message: 'success'
      };
    } catch (error: unknown) {
      console.error('update document failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'update document failed'
      };
    }
  }

  /**
   * 更新文档（with query field）
   */
  static async updateByQueryField<T extends Record<string, unknown>>(
    collectionName: string, 
    field: string, 
    value: unknown,
    data: Partial<T>
  ): Promise<FirestoreResponse<void>> {
    try {
      const queryRef = query(collection(db, collectionName), where(field, '==', value));
      const querySnapshot = await getDocs(queryRef);
      const docRef = querySnapshot.docs[0];
      if (!docRef) {
        return {
          success: false,
          error: `document not found by query: ${value} in ${field}`
        };
      }
      await updateDoc(docRef.ref, data as UpdateData<T>);
      return {
        success: true,
        message: 'success'
      };
    } catch (error: unknown) {
      console.error('update document by query field failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'update document by query field failed'
      };
    }
  }

  /**
   * 删除文档
   */
  static async delete(
    collectionName: string, 
    docId: string
  ): Promise<FirestoreResponse<void>> {
    try {
      const docRef = doc(db, collectionName, docId);
      await deleteDoc(docRef);
      
      return {
        success: true,
        message: 'success'
      };
    } catch (error: unknown) {
        console.error('delete document failed:', error);
      return {
        success: false
      };
    }
  }

  /**
   * 批量操作
   */
  static async batchWrite(operations: {
    type: 'create' | 'update' | 'delete';
    collectionName: string;
    docId?: string;
    data?: Record<string, unknown>;
  }[]): Promise<FirestoreResponse<void>> {
    try {
      const batch = writeBatch(db);

      for (const operation of operations) {
        const docRef = operation.docId 
          ? doc(db, operation.collectionName, operation.docId)
          : doc(collection(db, operation.collectionName));

        switch (operation.type) {
          case 'create':
            batch.set(docRef, {
              ...operation.data,
              createdAt: serverTimestamp(),
              updatedAt: serverTimestamp()
            });
            break;
          case 'update':
            batch.update(docRef, {
              ...operation.data,
              updatedAt: serverTimestamp()
            });
            break;
          case 'delete':
            batch.delete(docRef);
            break;
        }
      }

      await batch.commit();
      
      return {
        success: true,
        message: 'success'
      };
    } catch (error: unknown) {
      console.error('batch operation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'batch operation failed'
      };
    }
  }

  /**
   * 实时监听文档变化
   */
  static subscribeToDocument<T extends FirestoreDocument>(
    collectionName: string,
    docId: string,
    callback: (data: T | null, error?: string) => void
  ): Unsubscribe {
    const docRef = doc(db, collectionName, docId);
    
    return onSnapshot(docRef, 
      (doc) => {
        if (doc.exists()) {
          callback({ id: doc.id, ...doc.data() } as T);
        } else {
          callback(null);
        }
      },
      (error) => {
        console.error('listen document change failed:', error);
        callback(null, error.message);
      }
    );
  }

  /**
   * 实时监听集合变化
   */
  static subscribeToCollection<T extends FirestoreDocument>(
    collectionName: string,
    options: QueryOptions = {},
    callback: (data: T[], error?: string) => void
  ): Unsubscribe {
    const collectionRef = collection(db, collectionName);
    let queryRef = query(collectionRef);

    // 构建查询
    if (options.where) {
      for (const condition of options.where) {
        queryRef = query(queryRef, where(condition.field, condition.operator, condition.value));
      }
    }

    if (options.orderBy) {
      for (const order of options.orderBy) {
        queryRef = query(queryRef, orderBy(order.field, order.direction || 'asc'));
      }
    }

    if (options.limit) {
      queryRef = query(queryRef, limit(options.limit));
    }

    return onSnapshot(queryRef,
      (querySnapshot: QuerySnapshot<DocumentData>) => {
        const documents: T[] = [];
        querySnapshot.forEach((doc) => {
          documents.push({ id: doc.id, ...doc.data() } as T);
        });
        callback(documents);
      },
      (error) => {
        console.error('listen collection change failed:', error);
        callback([], error.message);
      }
    );
  }

  /**
   * 数组字段操作 - 添加元素
   */
  static async arrayAdd(
    collectionName: string,
    docId: string,
    field: string,
    value: unknown
  ): Promise<FirestoreResponse<void>> {
    try {
      const docRef = doc(db, collectionName, docId);
      await updateDoc(docRef, {
        [field]: arrayUnion(value),
        updatedAt: serverTimestamp()
      });
      
      return {
        success: true,
        message: 'success'
      };
    } catch (error: unknown) {
      console.error('array element add failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'array element add failed'
      };
    }
  }
  
  /**
   * 数组字段操作 - 添加元素
   */
  static async arrayAddByQueryDocId(
    collectionName: string,
    queryValue: string,
    queryField: string,
    field: string,
    value: unknown
  ): Promise<FirestoreResponse<void>> {
    try {
      const queryRef = query(collection(db, collectionName), where(queryField, '==', queryValue));
      const querySnapshot = await getDocs(queryRef);
      const docRef = querySnapshot.docs[0];
      if (!docRef) {
        return {
          success: false,
          error: `document not found by query: ${queryValue} in ${queryField}`
        };
      }

      await updateDoc(docRef.ref, {
        [field]: arrayUnion(value),
        updatedAt: serverTimestamp()
      });
      
      return {
        success: true,
        message: 'success'
      };
    } catch (error: unknown) {
      console.error('array element add failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'array element add failed'
      };
    }
  }


  /**
   * 数组字段操作 - 删除元素
   */
  static async arrayRemoveValue(
    collectionName: string,
    docId: string,
    field: string,
    value: unknown
  ): Promise<FirestoreResponse<void>> {
    try {
      const docRef = doc(db, collectionName, docId);
      await updateDoc(docRef, {
        [field]: arrayRemove(value),
        updatedAt: serverTimestamp()
      });
      
      return {
        success: true,
        message: 'success'
      };
    } catch (error: unknown) {
      console.error('array element delete failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'array element delete failed'
      };
    }
  }

  /**
   * 数值字段操作 - 增加/减少
   */
  static async incrementField(
    collectionName: string,
    docId: string,
    field: string,
    value: number
  ): Promise<FirestoreResponse<void>> {
    try {
      const docRef = doc(db, collectionName, docId);
      await updateDoc(docRef, {
        [field]: increment(value),
        updatedAt: serverTimestamp()
      });
      
      return {
        success: true,
        message: 'success'
      };
    } catch (error: unknown) {
      console.error('number field update failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'number field update failed'
      };
    }
  }

  /**
   * 检查文档是否存在
   */
  static async exists(collectionName: string, docId: string): Promise<boolean> {
    try {
      const docRef = doc(db, collectionName, docId);
      const docSnap = await getDoc(docRef);
      return docSnap.exists();
    } catch (error) {
      console.error('check document existence failed:', error);
      return false;
    }
  }

  /**
   * 获取集合中文档数量
   */
  static async getCount(
    collectionName: string,
    options: QueryOptions = {}
  ): Promise<number> {
    try {
      const result = await this.getMany(collectionName, options);
      return result.data?.length || 0;
    } catch (error) {
      console.error('get document count failed:', error);
      return 0;
    }
  }
}

// 导出常用的 Firestore 函数和类型
export {
  serverTimestamp,
  increment,
  arrayUnion,
  arrayRemove,
  type DocumentSnapshot,
  type QueryDocumentSnapshot,
  type QuerySnapshot,
  type Unsubscribe
};

// 默认导出
export default FirestoreService;
