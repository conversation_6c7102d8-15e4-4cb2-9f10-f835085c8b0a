import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'base_model.dart';

part 'portal_user_account.g.dart';

/// Portal User Account - 商家系统用户账户
/// 存储在 Firestore collection "portal-user-account"
/// 对应Web端的 portal-user-account 集合
@JsonSerializable()
class PortalUserAccount extends BaseModel {
  String fid;                     // Firebase Auth ID
  String email;                   // 邮箱地址
  String phoneNumber;             // 电话号码
  bool isEmailVerified;           // 邮箱是否已验证
  bool needChangePassword;        // 是否需要修改密码

  PortalUserAccount({
    required this.fid,
    required this.email,
    required this.phoneNumber,
    this.isEmailVerified = false,
    this.needChangePassword = true,
    super.sid,
    super.isValid = true,
    super.isSynced = true,
  });

  /// 从 Firestore 文档创建 PortalUserAccount 对象
  factory PortalUserAccount.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    
    return PortalUserAccount(
      sid: doc.id,
      fid: data['fid'] ?? '',
      email: data['email'] ?? '',
      phoneNumber: data['phoneNumber'] ?? '',
      isEmailVerified: data['isEmailVerified'] ?? false,
      needChangePassword: data['needChangePassword'] ?? true,
      isValid: data['is_valid'] ?? true,
      isSynced: data['is_synced'] ?? true,
    );
  }

  /// 从 JSON 创建对象
  factory PortalUserAccount.fromJson(Map<String, dynamic> json) =>
      _$PortalUserAccountFromJson(json);

  /// 转换为 JSON
  @override
  Map<String, dynamic> toJson() => _$PortalUserAccountToJson(this);

  /// 转换为 Firestore 数据
  Map<String, dynamic> toFirestoreData() {
    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });
    return dbData;
  }

  /// 集合名称
  static String get collection => 'portal-user-account';

  /// 验证邮箱格式
  bool isValidEmail() {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// 验证电话号码格式
  bool isValidPhoneNumber() {
    return RegExp(r'^\+1 \(\d{3}\)-\d{3}-\d{4}$').hasMatch(phoneNumber);
  }

  /// 是否可以登录
  bool canLogin() {
    return isEmailVerified && isValidEmail();
  }

  /// 获取显示用的电话号码（去掉格式）
  String getCleanPhoneNumber() {
    return phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
  }
}
