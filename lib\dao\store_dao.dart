import 'dart:async';
import 'package:onenata_app/common/plugin/firebase/firebase_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/models/models_i.dart';

class StoreDao {
  // Collection references
  final CollectionReference _storeAccountCollection =
      FirebaseFirestore.instance.collection(StoreAccount.collection);
  final CollectionReference _storeInfoCollection =
      FirebaseFirestore.instance.collection(StoreInfo.collection);

  // ==================== Store Account CRUD ====================

  /// 添加店铺账户
  Future<void> addStoreAccount(StoreAccount storeAccount) async {
    DocumentReference storeAccountRef = _storeAccountCollection.doc(storeAccount.sid);

    Map<String, dynamic> storeAccountDoc = storeAccount.toFirestoreData();
    storeAccountDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await storeAccountRef.set(storeAccountDoc);
  }

  /// 更新店铺账户
  Future<void> updateStoreAccount(StoreAccount storeAccount, {bool mergeOption = false}) async {
    DocumentReference storeAccountRef = _storeAccountCollection.doc(storeAccount.sid!);
    Map<String, dynamic> storeAccountDoc = storeAccount.toFirestoreData();
    storeAccountDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await storeAccountRef.set(storeAccountDoc, SetOptions(merge: mergeOption));
  }

  /// 删除店铺账户
  Future<void> deleteStoreAccount(String storeAccountId) async {
    DocumentReference storeAccountRef = _storeAccountCollection.doc(storeAccountId);
    await storeAccountRef.delete();
  }

  /// 软删除店铺账户
  Future<void> softDeleteStoreAccount(String storeAccountId) async {
    DocumentReference storeAccountRef = _storeAccountCollection.doc(storeAccountId);
    await storeAccountRef.update({CommonField.isValid: false});
  }

  /// 根据ID获取店铺账户
  Future<StoreAccount?> getStoreAccountById(String storeAccountId) async {
    DocumentSnapshot doc = await _storeAccountCollection.doc(storeAccountId).get();

    if (doc.exists) {
      return StoreAccount.fromFirestoreData(doc);
    } else {
      return null;
    }
  }

  /// 根据店主ID获取店铺账户
  Future<List<StoreAccount>> getStoreAccountsByOwnerId(String ownerId) async {
    QuerySnapshot querySnapshot = await _storeAccountCollection
        .where('owner_id', isEqualTo: ownerId)
        .where(CommonField.isValid, isEqualTo: true)
        .get();

    return querySnapshot.docs
        .map((doc) => StoreAccount.fromFirestoreData(doc))
        .toList();
  }

  /// 根据Google Place ID获取店铺账户
  Future<StoreAccount?> getStoreAccountByGooglePlaceId(String googlePlaceId) async {
    QuerySnapshot querySnapshot = await _storeAccountCollection
        .where('google_place_id', isEqualTo: googlePlaceId)
        .where(CommonField.isValid, isEqualTo: true)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return StoreAccount.fromFirestoreData(querySnapshot.docs.first);
    }
    return null;
  }

  /// 获取活跃的店铺账户
  Future<List<StoreAccount>> getActiveStoreAccounts() async {
    QuerySnapshot querySnapshot = await _storeAccountCollection
        .where('store_status', isEqualTo: 'ACTIVE')
        .where(CommonField.isValid, isEqualTo: true)
        .get();

    return querySnapshot.docs
        .map((doc) => StoreAccount.fromFirestoreData(doc))
        .toList();
  }

  // ==================== Store Info CRUD ====================

  /// 添加店铺信息
  Future<void> addStoreInfo(StoreInfo storeInfo) async {
    DocumentReference storeInfoRef = _storeInfoCollection.doc(storeInfo.sid);

    Map<String, dynamic> storeInfoDoc = storeInfo.toFirestoreData();
    storeInfoDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await storeInfoRef.set(storeInfoDoc);
  }

  /// 更新店铺信息
  Future<void> updateStoreInfo(StoreInfo storeInfo, {bool mergeOption = false}) async {
    DocumentReference storeInfoRef = _storeInfoCollection.doc(storeInfo.sid!);
    Map<String, dynamic> storeInfoDoc = storeInfo.toFirestoreData();
    storeInfoDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await storeInfoRef.set(storeInfoDoc, SetOptions(merge: mergeOption));
  }

  /// 删除店铺信息
  Future<void> deleteStoreInfo(String storeInfoId) async {
    DocumentReference storeInfoRef = _storeInfoCollection.doc(storeInfoId);
    await storeInfoRef.delete();
  }

  /// 软删除店铺信息
  Future<void> softDeleteStoreInfo(String storeInfoId) async {
    DocumentReference storeInfoRef = _storeInfoCollection.doc(storeInfoId);
    await storeInfoRef.update({CommonField.isValid: false});
  }

  /// 根据ID获取店铺信息
  Future<StoreInfo?> getStoreInfoById(String storeInfoId) async {
    DocumentSnapshot doc = await _storeInfoCollection.doc(storeInfoId).get();

    if (doc.exists) {
      return StoreInfo.fromFirestoreData(doc);
    } else {
      return null;
    }
  }

  /// 根据店铺ID获取店铺信息
  Future<StoreInfo?> getStoreInfoByStoreId(String storeId) async {
    QuerySnapshot querySnapshot = await _storeInfoCollection
        .where('storeId', isEqualTo: storeId)
        .where(CommonField.isValid, isEqualTo: true)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return StoreInfo.fromFirestoreData(querySnapshot.docs.first);
    }
    return null;
  }

  /// 获取开放预约的店铺信息
  Future<List<StoreInfo>> getAppointmentEnabledStores() async {
    QuerySnapshot querySnapshot = await _storeInfoCollection
        .where('appointmentOpen', isEqualTo: true)
        .where(CommonField.isValid, isEqualTo: true)
        .get();

    return querySnapshot.docs
        .map((doc) => StoreInfo.fromFirestoreData(doc))
        .toList();
  }

  /// 根据业务类型获取店铺信息
  Future<List<StoreInfo>> getStoreInfosByBusinessType(BusinessType businessType) async {
    QuerySnapshot querySnapshot = await _storeInfoCollection
        .where('businessType', isEqualTo: businessType.value)
        .where(CommonField.isValid, isEqualTo: true)
        .get();

    return querySnapshot.docs
        .map((doc) => StoreInfo.fromFirestoreData(doc))
        .toList();
  }

  /// 根据服务类型获取店铺信息
  Future<List<StoreInfo>> getStoreInfosByServiceType(ServiceCategory serviceCategory) async {
    String serviceField = 'services.retail'; // 默认值
    switch (serviceCategory) {
      case ServiceCategory.grooming:
        serviceField = 'services.grooming';
        break;
      case ServiceCategory.boarding:
        serviceField = 'services.boarding';
        break;
      case ServiceCategory.veterinary:
        serviceField = 'services.veterinary';
        break;
      case ServiceCategory.training:
        serviceField = 'services.training';
        break;
      case ServiceCategory.daycare:
        serviceField = 'services.boarding'; // 使用boarding作为daycare的映射
        break;
      case ServiceCategory.other:
        serviceField = 'services.retail';
        break;
    }

    QuerySnapshot querySnapshot = await _storeInfoCollection
        .where(serviceField, isEqualTo: true)
        .where(CommonField.isValid, isEqualTo: true)
        .get();

    return querySnapshot.docs
        .map((doc) => StoreInfo.fromFirestoreData(doc))
        .toList();
  }

  /// 根据城市获取店铺信息
  Future<List<StoreInfo>> getStoreInfosByCity(String city) async {
    QuerySnapshot querySnapshot = await _storeInfoCollection
        .where('current_address.city', isEqualTo: city)
        .where(CommonField.isValid, isEqualTo: true)
        .get();

    return querySnapshot.docs
        .map((doc) => StoreInfo.fromFirestoreData(doc))
        .toList();
  }

  /// 搜索店铺（按名称）
  Future<List<StoreInfo>> searchStoresByName(String searchTerm) async {
    // 注意：Firestore不支持全文搜索，这里使用简单的前缀匹配
    // 在生产环境中，建议使用Algolia或Elasticsearch等专门的搜索服务
    QuerySnapshot querySnapshot = await _storeInfoCollection
        .where(CommonField.isValid, isEqualTo: true)
        .orderBy('name')
        .startAt([searchTerm])
        .endAt(['$searchTerm\uf8ff'])
        .get();

    return querySnapshot.docs
        .map((doc) => StoreInfo.fromFirestoreData(doc))
        .toList();
  }

  // ==================== 组合查询 ====================

  /// 获取店铺的完整信息（账户+信息）
  Future<Map<String, dynamic>?> getCompleteStoreData(String storeId) async {
    final storeAccount = await getStoreAccountById(storeId);
    final storeInfo = await getStoreInfoByStoreId(storeId);

    if (storeAccount == null) return null;

    return {
      'account': storeAccount,
      'info': storeInfo,
    };
  }

  /// 批量获取店铺信息
  Future<List<StoreInfo>> getStoreInfosByIds(List<String> storeIds) async {
    if (storeIds.isEmpty) return [];

    // Firestore的in查询限制为10个元素，需要分批查询
    final List<StoreInfo> allStores = [];
    const batchSize = 10;

    for (int i = 0; i < storeIds.length; i += batchSize) {
      final batch = storeIds.skip(i).take(batchSize).toList();
      
      QuerySnapshot querySnapshot = await _storeInfoCollection
          .where('store_id', whereIn: batch)
          .where(CommonField.isValid, isEqualTo: true)
          .get();

      final batchStores = querySnapshot.docs
          .map((doc) => StoreInfo.fromFirestoreData(doc))
          .toList();
      
      allStores.addAll(batchStores);
    }

    return allStores;
  }
}
