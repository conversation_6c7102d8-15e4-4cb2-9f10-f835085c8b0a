import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'base_model.dart';

part 'employee_schedule.g.dart';

/// 工作时间模型
/// 匹配Web端的workTimes结构
@JsonSerializable()
class WorkTime {
  String weekday;     // Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday
  bool open;          // 是否开放
  String startTime;   // 格式: "09:00"
  String endTime;     // 格式: "21:00"

  WorkTime({
    required this.weekday,
    required this.open,
    required this.startTime,
    required this.endTime,
  });

  factory WorkTime.fromJson(Map<String, dynamic> json) =>
      _$WorkTimeFromJson(json);

  Map<String, dynamic> toJson() => _$WorkTimeToJson(this);

  /// 获取工作时长（分钟）
  int getDurationInMinutes() {
    if (!open) return 0;

    final start = _parseTime(startTime);
    final end = _parseTime(endTime);
    return end.difference(start).inMinutes;
  }

  /// 检查指定时间是否在工作时间内
  bool isTimeInWorkingHours(String time) {
    if (!open) return false;

    final checkTime = _parseTime(time);
    final start = _parseTime(startTime);
    final end = _parseTime(endTime);

    return checkTime.isAfter(start) && checkTime.isBefore(end);
  }

  WorkTime copyWith({
    String? weekday,
    bool? open,
    String? startTime,
    String? endTime,
  }) {
    return WorkTime(
      weekday: weekday ?? this.weekday,
      open: open ?? this.open,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
    );
  }

  DateTime _parseTime(String time) {
    final parts = time.split(':');
    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);
    return DateTime(2000, 1, 1, hour, minute);
  }
}

/// 员工排班模型
/// 存储在 portal-user-data/{userId}/employee-schedule 子集合中
/// 对应 Web端 portal-user.ts 中的 EmployeeSchedule
@JsonSerializable()
class EmployeeSchedule extends BaseModel {
  String uid;                     // Portal user account sid
  String storeId;                 // Store sid
  bool active;                    // 是否可用
  List<WorkTime> workTimes;       // 工作时间安排

  EmployeeSchedule({
    required this.uid,
    required this.storeId,
    this.active = true,
    this.workTimes = const [],
    super.sid,
    super.isValid = true,
    super.isSynced = true,
  });

  /// 从 Firestore 文档创建 EmployeeSchedule 对象
  factory EmployeeSchedule.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

    List<WorkTime> workTimes = [];
    if (data['workTimes'] != null) {
      workTimes = (data['workTimes'] as List)
          .map((item) => item is WorkTime ? item : WorkTime.fromJson(Map<String, dynamic>.from(item)))
          .toList();
    }

    return EmployeeSchedule(
      sid: doc.id,
      uid: data['uid'] ?? '',
      storeId: data['storeId'] ?? '',
      active: data['active'] ?? true,
      workTimes: workTimes,
      isValid: data['is_valid'] ?? true,
      isSynced: data['is_synced'] ?? true,
    );
  }

  /// 从 JSON 创建对象
  factory EmployeeSchedule.fromJson(Map<String, dynamic> json) =>
      _$EmployeeScheduleFromJson(json);

  /// 转换为 JSON
  @override
  Map<String, dynamic> toJson() {
    final json = _$EmployeeScheduleToJson(this);
    // Manually serialize workTimes to ensure proper JSON conversion
    json['workTimes'] = workTimes.map((workTime) => workTime.toJson()).toList();
    return json;
  }

  /// 转换为 Firestore 数据
  Map<String, dynamic> toFirestoreData() {
    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });
    return dbData;
  }

  /// 集合名称（子集合）
  static String get collection => 'employee-schedule';

  /// 获取指定日期的工作时间
  WorkTime? getWorkTimeForDay(String weekday) {
    try {
      return workTimes.firstWhere((wt) => wt.weekday == weekday);
    } catch (e) {
      return null;
    }
  }

  /// 检查指定日期是否工作
  bool isWorkingDay(String weekday) {
    final workTime = getWorkTimeForDay(weekday);
    return workTime?.open ?? false;
  }

  /// 获取一周的工作天数
  int getWorkingDaysCount() {
    return workTimes.where((wt) => wt.open).length;
  }

  /// 获取一周的总工作时长（分钟）
  int getTotalWorkingMinutesPerWeek() {
    return workTimes
        .where((wt) => wt.open)
        .map((wt) => wt.getDurationInMinutes())
        .fold(0, (total, duration) => total + duration);
  }

  /// 复制对象
  EmployeeSchedule copyWith({
    String? uid,
    String? storeId,
    bool? active,
    List<WorkTime>? workTimes,
  }) {
    return EmployeeSchedule(
      uid: uid ?? this.uid,
      storeId: storeId ?? this.storeId,
      active: active ?? this.active,
      workTimes: workTimes ?? this.workTimes,
      sid: sid,
      isValid: isValid,
      isSynced: isSynced,
    );
  }
}