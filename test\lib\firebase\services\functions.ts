import { httpsCallable, getFunctions } from 'firebase/functions';
import { getApps } from 'firebase/app';
// import { PortalUserAccount } from '../../models/portal-user';

// 初始化 Firebase Functions
const app = getApps()[0];
const functions = getFunctions(app);

// 如果在开发环境，连接到模拟器
if (process.env.NODE_ENV === 'development') {
  // 连接到 Functions 模拟器（默认端口 5001）
  // connectFunctionsEmulator(functions, 'localhost', 5001);  
}

// 创建员工账户请求接口
interface CreateAccountRequest {
  email: string;
  password: string;
  phoneNumber?: string;
  createdBy?: string;
  needChangePassword?: boolean;
}

// 创建员工账户响应接口
interface CreateAccountResponse {
  success: boolean;
  data?: {
    userAccountSid: string;
    firebaseUid: string;
  }
  error?: string;
  message?: string;
}

/**
 * 调用 Cloud Function 创建员工账户
 * @param data 创建员工账户的数据
 * @returns Promise<CreateStaffAccountResponse>
 */
export const createStaffAccountFunction = async (
  data: CreateAccountRequest
): Promise<CreateAccountResponse> => {
  try {
    console.log('createStaffAccountFunction', data);

    // 获取云函数引用
    const createAccount = httpsCallable<CreateAccountRequest, CreateAccountResponse>(
      functions, 
      'createStaffAccount'
    );

    // 调用云函数
    const result = await createAccount(data);
    
    return result.data;
    
  } catch (error) {
    console.error('Error calling createAccount function:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create account'
    };
  }
};


export const createCustomerFunction = async (
  data: CreateAccountRequest
): Promise<CreateAccountResponse> => {
  try {
    console.log('createStaffAccountFunction', data);

    // 获取云函数引用
    const createAccount = httpsCallable<CreateAccountRequest, CreateAccountResponse>(
      functions, 
      'createCustomer'
    );

    // 调用云函数
    const result = await createAccount(data);
    
    return result.data;
    
  } catch (error) {
    console.error('Error calling createCustomer function:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create customer account'
    };
  }
}

export default {
  createStaffAccountFunction,
  createCustomerFunction
}; 