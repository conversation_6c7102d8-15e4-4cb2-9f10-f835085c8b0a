import { BaseModel, BaseModelImpl, JsonUtil } from './base_model';
import { 
  PaymentStatus,
  Currency,
  Timestamp,
  PaymentMethodDetails
} from './types';

/**
 * Payment - 支付记录
 * 存储在 Firestore collection "payments"
 */
export interface Payment extends BaseModel {
  orderId: string; // 订单 ID
  customerId: string; // 客户 ID (OneNata App 用户)
  amount: number; // 支付金额
  currency: Currency;
  paymentMethod: PaymentMethodDetails; // 支付方式详情
  status: PaymentStatus; // 支付状态
  transactionFee?: number; // 交易手续费
  refundedAmount?: number; // 退款金额
  createdAt: Timestamp; // 创建时间
  updatedAt: Timestamp; // 更新时间
  notes?: string; // 备注
  storeId?: string; // 店铺 ID
  processedAt?: Timestamp; // 处理时间
  failureReason?: string; // 失败原因
  receiptUrl?: string; // 收据 URL
  refundReason?: string; // 退款原因
  refundedAt?: Timestamp; // 退款时间
  externalTransactionId?: string; // 外部交易 ID
}

export class PaymentImpl extends BaseModelImpl implements Payment {
  orderId: string;
  customerId: string;
  amount: number;
  currency: Currency;
  paymentMethod: PaymentMethodDetails;
  status: PaymentStatus;
  transactionFee?: number;
  refundedAmount?: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  notes?: string;
  storeId?: string;
  processedAt?: Timestamp;
  failureReason?: string;
  receiptUrl?: string;
  refundReason?: string;
  refundedAt?: Timestamp;
  externalTransactionId?: string;

  constructor(data: Partial<Payment>) {
    super(data);
    this.orderId = data.orderId || '';
    this.customerId = data.customerId || '';
    this.amount = data.amount || 0;
    this.currency = data.currency || Currency.CAD;
    this.paymentMethod = data.paymentMethod || {
      type: 'stripe',
      provider: 'Stripe'
    };
    this.status = data.status || PaymentStatus.PENDING;
    this.transactionFee = data.transactionFee;
    this.refundedAmount = data.refundedAmount;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
    this.notes = data.notes;
    this.storeId = data.storeId;
    this.processedAt = data.processedAt;
    this.failureReason = data.failureReason;
    this.receiptUrl = data.receiptUrl;
    this.refundReason = data.refundReason;
    this.refundedAt = data.refundedAt;
    this.externalTransactionId = data.externalTransactionId;
  }

  static fromJson(json: Record<string, unknown>): PaymentImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new PaymentImpl({
      ...baseModel,
      orderId: JsonUtil.stringFromJson(json.orderId) || '',
      customerId: JsonUtil.stringFromJson(json.customerId) || '',
      amount: JsonUtil.numberFromJson(json.amount) || 0,
      currency: (json.currency as Currency) || Currency.CAD,
      paymentMethod: (json.paymentMethod as PaymentMethodDetails) || {
        type: 'stripe',
        provider: 'Stripe'
      },
      status: (json.status as PaymentStatus) || PaymentStatus.PENDING,
      transactionFee: JsonUtil.numberFromJson(json.transactionFee),
      refundedAmount: JsonUtil.numberFromJson(json.refundedAmount),
      createdAt: json.createdAt ? new Date(json.createdAt as string) : new Date(),
      updatedAt: json.updatedAt ? new Date(json.updatedAt as string) : new Date(),
      notes: JsonUtil.stringFromJson(json.notes),
      storeId: JsonUtil.stringFromJson(json.storeId),
      processedAt: json.processedAt ? new Date(json.processedAt as string) : undefined,
      failureReason: JsonUtil.stringFromJson(json.failureReason),
      receiptUrl: JsonUtil.stringFromJson(json.receiptUrl),
      refundReason: JsonUtil.stringFromJson(json.refundReason),
      refundedAt: json.refundedAt ? new Date(json.refundedAt as string) : undefined,
      externalTransactionId: JsonUtil.stringFromJson(json.externalTransactionId),
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      orderId: this.orderId,
      customerId: this.customerId,
      amount: this.amount,
      currency: this.currency,
      paymentMethod: this.paymentMethod,
      status: this.status,
      transactionFee: this.transactionFee,
      refundedAmount: this.refundedAmount,
      createdAt: new Date(this.createdAt).toISOString(),
      updatedAt: new Date(this.updatedAt).toISOString(),
      notes: JsonUtil.stringToJson(this.notes),
      storeId: JsonUtil.stringToJson(this.storeId),
      processedAt: this.processedAt ? new Date(this.processedAt).toISOString() : undefined,
      failureReason: JsonUtil.stringToJson(this.failureReason),
      receiptUrl: JsonUtil.stringToJson(this.receiptUrl),
      refundReason: JsonUtil.stringToJson(this.refundReason),
      refundedAt: this.refundedAt ? new Date(this.refundedAt).toISOString() : undefined,
      externalTransactionId: JsonUtil.stringToJson(this.externalTransactionId),
    };
  }

  getFormattedAmount(): string {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: this.currency
    }).format(this.amount);
  }

  getFormattedTransactionFee(): string {
    if (!this.transactionFee) return '$0.00';
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: this.currency
    }).format(this.transactionFee);
  }

  getFormattedRefundedAmount(): string {
    if (!this.refundedAmount) return '$0.00';
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: this.currency
    }).format(this.refundedAmount);
  }

  getNetAmount(): number {
    return this.amount - (this.transactionFee || 0);
  }

  getFormattedNetAmount(): string {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: this.currency
    }).format(this.getNetAmount());
  }

  isSuccessful(): boolean {
    return this.status === PaymentStatus.PAID;
  }

  isFailed(): boolean {
    return this.status === PaymentStatus.FAILED;
  }

  isPending(): boolean {
    return this.status === PaymentStatus.PENDING;
  }

  isRefunded(): boolean {
    return this.status === PaymentStatus.REFUNDED;
  }

  hasPartialRefund(): boolean {
    return this.refundedAmount !== undefined && 
           this.refundedAmount > 0 && 
           this.refundedAmount < this.amount;
  }

  hasFullRefund(): boolean {
    return this.refundedAmount !== undefined && 
           this.refundedAmount >= this.amount;
  }

  getStatusText(): string {
    switch (this.status) {
      case PaymentStatus.PENDING: return '待处理';
      case PaymentStatus.PAID: return '已支付';
      case PaymentStatus.FAILED: return '支付失败';
      case PaymentStatus.REFUNDED: return '已退款';
      case PaymentStatus.UNPAID: return '未支付';
      default: return '未知状态';
    }
  }

  getPaymentMethodText(): string {
    switch (this.paymentMethod.type) {
      case 'stripe': return 'Stripe';
      case 'paypal': return 'PayPal';
      case 'cash': return '现金';
      case 'pos': return 'POS机';
      case 'bank_transfer': return '银行转账';
      default: return '其他';
    }
  }

  getCardInfo(): string {
    if (this.paymentMethod.card) {
      return `${this.paymentMethod.card.brand} ****${this.paymentMethod.card.last4}`;
    }
    return '';
  }

  getProcessingTime(): string {
    if (!this.processedAt) return '未处理';
    const processingTime = new Date(this.processedAt).getTime() - new Date(this.createdAt).getTime();
    const seconds = Math.floor(processingTime / 1000);
    if (seconds < 60) return `${seconds}秒`;
    const minutes = Math.floor(seconds / 60);
    return `${minutes}分钟`;
  }

  getFormattedCreatedAt(): string {
    return new Date(this.createdAt).toLocaleString('zh-CN');
  }

  getFormattedUpdatedAt(): string {
    return new Date(this.updatedAt).toLocaleString('zh-CN');
  }

  getFormattedProcessedAt(): string {
    if (!this.processedAt) return '未处理';
    return new Date(this.processedAt).toLocaleString('zh-CN');
  }

  getFormattedRefundedAt(): string {
    if (!this.refundedAt) return '未退款';
    return new Date(this.refundedAt).toLocaleString('zh-CN');
  }

  canRefund(): boolean {
    return this.status === PaymentStatus.PAID && !this.hasFullRefund();
  }

  canRetry(): boolean {
    return this.status === PaymentStatus.FAILED || this.status === PaymentStatus.UNPAID;
  }

  getRemainingRefundableAmount(): number {
    if (!this.isSuccessful()) return 0;
    return this.amount - (this.refundedAmount || 0);
  }

  getFormattedRemainingRefundableAmount(): string {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: this.currency
    }).format(this.getRemainingRefundableAmount());
  }
}

/**
 * 支付服务类
 */
export class PaymentService {
  /**
   * 从 API 响应创建支付记录列表
   */
  static createPaymentsFromApiResponse(response: unknown[]): PaymentImpl[] {
    return response
      .filter((item): item is Record<string, unknown> => 
        typeof item === 'object' && item !== null
      )
      .map(item => PaymentImpl.fromJson(item));
  }

  /**
   * 搜索支付记录
   */
  static searchPayments(payments: Payment[], query: string): Payment[] {
    const lowerQuery = query.toLowerCase();
    return payments.filter(payment => 
      payment.sid?.toLowerCase().includes(lowerQuery) ||
      payment.orderId.toLowerCase().includes(lowerQuery) ||
      payment.customerId.toLowerCase().includes(lowerQuery) ||
      payment.externalTransactionId?.toLowerCase().includes(lowerQuery) ||
      payment.paymentMethod.provider.toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * 按状态过滤支付记录
   */
  static getPaymentsByStatus(payments: Payment[], status: PaymentStatus): Payment[] {
    return payments.filter(payment => payment.status === status);
  }

  /**
   * 按支付方式过滤支付记录
   */
  static getPaymentsByMethod(payments: Payment[], methodType: string): Payment[] {
    return payments.filter(payment => payment.paymentMethod.type === methodType);
  }

  /**
   * 获取客户的支付记录
   */
  static getPaymentsByCustomer(payments: Payment[], customerId: string): Payment[] {
    return payments.filter(payment => payment.customerId === customerId);
  }

  /**
   * 获取订单的支付记录
   */
  static getPaymentsByOrder(payments: Payment[], orderId: string): Payment[] {
    return payments.filter(payment => payment.orderId === orderId);
  }

  /**
   * 获取店铺的支付记录
   */
  static getPaymentsByStore(payments: Payment[], storeId: string): Payment[] {
    return payments.filter(payment => payment.storeId === storeId);
  }

  /**
   * 按日期范围过滤支付记录
   */
  static getPaymentsByDateRange(payments: Payment[], startDate: Date, endDate: Date): Payment[] {
    return payments.filter(payment => {
      const paymentDate = new Date(payment.createdAt);
      return paymentDate >= startDate && paymentDate <= endDate;
    });
  }

  /**
   * 获取成功的支付记录
   */
  static getSuccessfulPayments(payments: Payment[]): Payment[] {
    return payments.filter(payment => payment.status === PaymentStatus.PAID);
  }

  /**
   * 获取失败的支付记录
   */
  static getFailedPayments(payments: Payment[]): Payment[] {
    return payments.filter(payment => payment.status === PaymentStatus.FAILED);
  }

  /**
   * 获取待处理的支付记录
   */
  static getPendingPayments(payments: Payment[]): Payment[] {
    return payments.filter(payment => payment.status === PaymentStatus.PENDING);
  }

  /**
   * 获取已退款的支付记录
   */
  static getRefundedPayments(payments: Payment[]): Payment[] {
    return payments.filter(payment => payment.status === PaymentStatus.REFUNDED);
  }

  /**
   * 计算支付总额
   */
  static calculatePaymentsTotal(payments: Payment[]): number {
    return payments.reduce((sum, payment) => sum + payment.amount, 0);
  }

  /**
   * 计算净收入（扣除手续费）
   */
  static calculateNetRevenue(payments: Payment[]): number {
    return payments
      .filter(payment => payment.status === PaymentStatus.PAID)
      .reduce((sum, payment) => sum + payment.amount - (payment.transactionFee || 0), 0);
  }

  /**
   * 计算总手续费
   */
  static calculateTotalFees(payments: Payment[]): number {
    return payments.reduce((sum, payment) => sum + (payment.transactionFee || 0), 0);
  }

  /**
   * 计算总退款金额
   */
  static calculateTotalRefunds(payments: Payment[]): number {
    return payments.reduce((sum, payment) => sum + (payment.refundedAmount || 0), 0);
  }

  /**
   * 获取支付统计信息
   */
  static getPaymentStatistics(payments: Payment[]): {
    total: number;
    successful: number;
    failed: number;
    pending: number;
    refunded: number;
    totalAmount: number;
    netRevenue: number;
    totalFees: number;
    totalRefunds: number;
    averageAmount: number;
    successRate: number;
  } {
    const totalAmount = this.calculatePaymentsTotal(payments);
    const netRevenue = this.calculateNetRevenue(payments);
    const totalFees = this.calculateTotalFees(payments);
    const totalRefunds = this.calculateTotalRefunds(payments);
    const successful = payments.filter(p => p.status === PaymentStatus.PAID).length;
    const failed = payments.filter(p => p.status === PaymentStatus.FAILED).length;
    const pending = payments.filter(p => p.status === PaymentStatus.PENDING).length;
    const refunded = payments.filter(p => p.status === PaymentStatus.REFUNDED).length;
    
    return {
      total: payments.length,
      successful,
      failed,
      pending,
      refunded,
      totalAmount,
      netRevenue,
      totalFees,
      totalRefunds,
      averageAmount: payments.length > 0 ? totalAmount / payments.length : 0,
      successRate: payments.length > 0 ? (successful / payments.length) * 100 : 0,
    };
  }

  /**
   * 验证支付数据
   */
  static validatePayment(payment: Payment): string[] {
    const errors: string[] = [];
    
    if (!payment.orderId) {
      errors.push('订单 ID 是必需的');
    }
    
    if (!payment.customerId) {
      errors.push('客户 ID 是必需的');
    }
    
    if (payment.amount <= 0) {
      errors.push('支付金额必须大于 0');
    }
    
    if (payment.transactionFee && payment.transactionFee < 0) {
      errors.push('交易手续费不能为负数');
    }
    
    if (payment.refundedAmount && payment.refundedAmount < 0) {
      errors.push('退款金额不能为负数');
    }
    
    if (payment.refundedAmount && payment.refundedAmount > payment.amount) {
      errors.push('退款金额不能超过支付金额');
    }
    
    if (!payment.paymentMethod.type) {
      errors.push('支付方式类型是必需的');
    }
    
    if (!payment.paymentMethod.provider) {
      errors.push('支付提供商是必需的');
    }
    
    return errors;
  }

  /**
   * 生成支付摘要
   */
  static generatePaymentSummary(payment: PaymentImpl): string {
    const statusText = payment.getStatusText();
    const methodText = payment.getPaymentMethodText();
    const cardInfo = payment.getCardInfo();
    
    return `支付 ${payment.sid} - ${statusText} - ${methodText}${cardInfo ? ' (' + cardInfo + ')' : ''} - ${payment.getFormattedAmount()}`;
  }

  /**
   * 按月份分组支付记录
   */
  static groupPaymentsByMonth(payments: Payment[]): Record<string, Payment[]> {
    const groups: Record<string, Payment[]> = {};
    
    payments.forEach(payment => {
      const date = new Date(payment.createdAt);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      
      if (!groups[monthKey]) {
        groups[monthKey] = [];
      }
      groups[monthKey].push(payment);
    });
    
    return groups;
  }

  /**
   * 按支付方式分组支付记录
   */
  static groupPaymentsByMethod(payments: Payment[]): Record<string, Payment[]> {
    const groups: Record<string, Payment[]> = {};
    
    payments.forEach(payment => {
      const methodType = payment.paymentMethod.type;
      
      if (!groups[methodType]) {
        groups[methodType] = [];
      }
      groups[methodType].push(payment);
    });
    
    return groups;
  }

  /**
   * 获取支付趋势数据
   */
  static getPaymentTrends(payments: Payment[], days: number = 30): {
    date: string;
    amount: number;
    count: number;
  }[] {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);
    
    const trends: { date: string; amount: number; count: number }[] = [];
    
    for (let i = 0; i < days; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      const dateString = currentDate.toISOString().split('T')[0];
      
      const dayPayments = payments.filter(payment => {
        const paymentDate = new Date(payment.createdAt).toISOString().split('T')[0];
        return paymentDate === dateString && payment.status === PaymentStatus.PAID;
      });
      
      trends.push({
        date: dateString,
        amount: dayPayments.reduce((sum, payment) => sum + payment.amount, 0),
        count: dayPayments.length
      });
    }
    
    return trends;
  }
} 