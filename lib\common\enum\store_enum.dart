import 'package:json_annotation/json_annotation.dart';

/// 店铺验证状态枚举
/// 对应 web端 types.ts 中的 StoreVerifiedStatus
enum StoreVerifiedStatus {
  /// 待审核
  pending('PENDING'),
  /// 已批准
  approved('APPROVED'),
  /// 已拒绝
  rejected('REJECTED'),
  /// 需要更多信息
  needMoreInfo('NEED_MORE_INFO');

  const StoreVerifiedStatus(this.value);
  final String value;

  static StoreVerifiedStatus fromString(String value) {
    return StoreVerifiedStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => StoreVerifiedStatus.pending,
    );
  }

  String get displayName {
    switch (this) {
      case StoreVerifiedStatus.pending:
        return '待审核';
      case StoreVerifiedStatus.approved:
        return '已批准';
      case StoreVerifiedStatus.rejected:
        return '已拒绝';
      case StoreVerifiedStatus.needMoreInfo:
        return '需要更多信息';
    }
  }
}

/// 店铺状态枚举
/// 对应 web端 types.ts 中的 StoreStatus
enum StoreStatus {
  /// 活跃
  active('ACTIVE'),
  /// 非活跃
  inactive('INACTIVE'),
  /// 暂停
  suspended('SUSPENDED'),
  /// 关闭
  closed('CLOSED');

  const StoreStatus(this.value);
  final String value;

  static StoreStatus fromString(String value) {
    return StoreStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => StoreStatus.active,
    );
  }

  String get displayName {
    switch (this) {
      case StoreStatus.active:
        return '活跃';
      case StoreStatus.inactive:
        return '非活跃';
      case StoreStatus.suspended:
        return '暂停';
      case StoreStatus.closed:
        return '关闭';
    }
  }
}

/// 商业类型枚举
/// 对应 web端 types.ts 中的 BusinessType
enum BusinessType {
  /// 在线商店
  @JsonValue('online_store')
  onlineStore('online_store'),
  /// 家庭式企业
  @JsonValue('family_based_business')
  familyBasedBusiness('family_based_business'),
  /// 零售企业
  @JsonValue('retail_business')
  retailBusiness('retail_business'),
  /// 商业企业
  @JsonValue('commercial_business')
  commercialBusiness('commercial_business'),
  /// 宠物店
  @JsonValue('pet_store')
  petStore('pet_store'),
  /// 兽医诊所
  @JsonValue('veterinary_clinic')
  veterinaryClinic('veterinary_clinic'),
  /// 宠物美容
  @JsonValue('pet_grooming')
  petGrooming('pet_grooming'),
  /// 宠物酒店
  @JsonValue('pet_hotel')
  petHotel('pet_hotel'),
  /// 宠物训练
  @JsonValue('pet_training')
  petTraining('pet_training'),
  /// 综合服务
  @JsonValue('comprehensive_service')
  comprehensiveService('comprehensive_service'),
  /// 连锁店
  @JsonValue('franchise')
  franchise('franchise'),
  /// 移动服务
  @JsonValue('mobile_service')
  mobileService('mobile_service'),
  /// 其他
  @JsonValue('other')
  other('other');

  const BusinessType(this.value);
  final String value;

  static BusinessType fromString(String value) {
    return BusinessType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => BusinessType.petStore,
    );
  }

  String get displayName {
    switch (this) {
      case BusinessType.onlineStore:
        return '在线商店';
      case BusinessType.familyBasedBusiness:
        return '家庭式企业';
      case BusinessType.retailBusiness:
        return '零售企业';
      case BusinessType.commercialBusiness:
        return '商业企业';
      case BusinessType.petStore:
        return '宠物店';
      case BusinessType.veterinaryClinic:
        return '兽医诊所';
      case BusinessType.petGrooming:
        return '宠物美容';
      case BusinessType.petHotel:
        return '宠物酒店';
      case BusinessType.petTraining:
        return '宠物训练';
      case BusinessType.comprehensiveService:
        return '综合服务';
      case BusinessType.franchise:
        return '连锁店';
      case BusinessType.mobileService:
        return '移动服务';
      case BusinessType.other:
        return '其他';
    }
  }
}

/// 客户状态枚举
/// 对应 web端 types.ts 中的 CustomerStatus
enum CustomerStatus {
  /// 活跃
  active('ACTIVE'),
  /// 非活跃
  inactive('INACTIVE'),
  /// 黑名单
  blacklisted('BLACKLISTED'),
  /// VIP
  vip('VIP');

  const CustomerStatus(this.value);
  final String value;

  static CustomerStatus fromString(String value) {
    return CustomerStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => CustomerStatus.active,
    );
  }

  String get displayName {
    switch (this) {
      case CustomerStatus.active:
        return '活跃';
      case CustomerStatus.inactive:
        return '非活跃';
      case CustomerStatus.blacklisted:
        return '黑名单';
      case CustomerStatus.vip:
        return 'VIP';
    }
  }
}

/// 货币枚举
/// 对应 web端 types.ts 中的 Currency
enum Currency {
  /// 加拿大元
  cad('CAD'),
  /// 美元
  usd('USD'),
  /// 人民币
  cny('CNY');

  const Currency(this.value);
  final String value;

  static Currency fromString(String value) {
    return Currency.values.firstWhere(
      (currency) => currency.value == value,
      orElse: () => Currency.cad,
    );
  }

  String get displayName {
    switch (this) {
      case Currency.cad:
        return 'CAD';
      case Currency.usd:
        return 'USD';
      case Currency.cny:
        return 'CNY';
    }
  }

  String get symbol {
    switch (this) {
      case Currency.cad:
        return '\$';
      case Currency.usd:
        return '\$';
      case Currency.cny:
        return '¥';
    }
  }
}