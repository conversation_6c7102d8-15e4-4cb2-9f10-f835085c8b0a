/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { 
  AppointmentStatus,
  AppointmentSource,
  StoreServiceStatus,
} from '../models/types';
import { v4 as uuidv4 } from 'uuid';
import { FirestoreDocument, FirestoreService } from '../firebase/services/firestore';
import { ProgressCallback } from '../types/common';

// ==================== Types and Interfaces ====================

export interface CreateAppointmentData {
  customerId: string;
  staffId: string;
  serviceId: string;
  date: string;                       // YYYY-MM-DD
  startTime: string;                  // HH:MM
  duration: number;                   // minutes (must be multiple of 15)
  notes?: string;
  customerNotes?: string;
  source?: AppointmentSource;
}

export interface TimeSlotQuery {
  storeId: string;
  staffId: string;
  serviceId: string;
  date: string;                       // YYYY-MM-DD
  duration: number;                   // minutes
}

export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ServiceListResponse<T> extends ServiceResponse<T[]> {
  total?: number;
}

// ==================== Core Service Class ====================

/**
 * Appointment Service - 预约服务核心类
 * 负责处理所有预约相关的业务逻辑
 */
export class AppointmentService {
  private readonly APPOINTMENT_COLLECTION = 'appointment';
  private readonly CUSTOMER_COLLECTION = 'user-data';
  private readonly PORTAL_USER_DATA_COLLECTION = 'portal-user-data';
  private readonly EMPLOYEE_SCHEDULE_COLLECTION = 'employee-schedule';

  private readonly TIME_SLOT_DURATION = 15; // 15分钟一个时间块
  private readonly MAX_BOOKING_DAYS_AHEAD = 60; // 最多提前60天预约

  // ==================== Core Appointment Methods ====================

  /**
   * Create new appointment - 使用 Store Services 架构
   */
  async createAppointment(
    storeId: string,
    appointmentData: CreateAppointmentData,
    createdBy: string,
    onProgress?: ProgressCallback
  ): Promise<ServiceResponse<string>> {

    console.log('createAppointment appointmentData:', appointmentData);
    console.log('createAppointment createdBy:', createdBy);
    console.log('createAppointment storeId:', storeId);

    const totalSteps = 6;
    let currentStep = 0;
    
    const updateProgress = (message: string) => {
      currentStep++;
      onProgress?.(currentStep, totalSteps, message);
    };

    try {
      updateProgress('Validating appointment data...');
      
      // Validate duration
      if (appointmentData.duration % 15 !== 0) {
        return {
          success: false,
          error: 'Duration must be a multiple of 15 minutes'
        };
      }

      updateProgress('Checking service availability...');
      
      // Import store service to check service availability
      const { default: storeService } = await import('./store_services');
      
      // Get store service by ID
      const serviceResult = await storeService.getStoreServiceById(storeId, appointmentData.serviceId);

      if (!serviceResult.success || !serviceResult.data) {
        return {
          success: false,
          error: 'Service not found'
        };
      }

      const service = serviceResult.data;
      
      // Check if service is active and allows online booking
      if (service.status !== StoreServiceStatus.ACTIVE) {
        return {
          success: false,
          error: 'Service is not currently active'
        };
      }

      if (!service.isOnlineBookingEnabled) {
        return {
          success: false,
          error: 'Service does not allow online booking'
        };
      }

      // Check if staff can provide this service
      if (!service.staffIds.includes(appointmentData.staffId)) {
        return {
          success: false,
          error: 'Selected staff member is not assigned to provide this service'
        };
      }

      updateProgress('Verifying staff availability...');
      
      // Import staff service to validate staff
      const { default: staffService } = await import('./staff_services');
      
      // Get staff member details
      const staffResult = await staffService.getStaffMember(appointmentData.staffId, storeId);
      if (!staffResult.success || !staffResult.data) {
        return {
          success: false,
          error: 'Staff member not found'
        };
      }

      const staff = staffResult.data;
      
      // Check if staff is active
      if (!staff.isActive) {
        return {
          success: false,
          error: 'Selected staff member is not currently active'
        };
      }

      updateProgress('Getting customer information...');
      
      // Get customer info
      
      const customerResult = await FirestoreService.getDocByQueryField(
        this.CUSTOMER_COLLECTION,
        'uid',
        appointmentData.customerId
      );

      if (!customerResult.success || !customerResult.data) {
        return {
          success: false,
          error: 'Customer not found'
        };
      }

      const customer = customerResult.data;

      updateProgress('Creating appointment...');
      
      // Create appointment
      const appointmentId = uuidv4();
      const endTime = this.minutesToTime(
        this.timeToMinutes(appointmentData.startTime) + appointmentData.duration
      );

      const appointment = {
        sid: appointmentId,
        appointmentId,
        storeId,
        customerId: appointmentData.customerId,
        staffId: appointmentData.staffId,
        serviceId: appointmentData.serviceId,
        status: service.requiresApproval ? AppointmentStatus.DRAFT : AppointmentStatus.CONFIRMED,
        source: appointmentData.source || AppointmentSource.PORTAL,
        timeInfo: {
          date: appointmentData.date,
          startTime: appointmentData.startTime,
          endTime,
          duration: appointmentData.duration,
        },
        customerInfo: {
          name: customer.displayName || `${customer.firstName || ''} ${customer.lastName || ''}`.trim(),
          email: customer.email,
          phoneNumber: customer.phoneNumber || ''
        },
        serviceInfo: {
          serviceName: `${service.serviceCategory} - ${service.serviceBreed}`,
          serviceCategory: service.serviceCategory || '',
          serviceBreed: service.serviceBreed || '',
          duration: appointmentData.duration,
        },
        staffInfo: {
          staffName: staff.userData.displayName || `${staff.userData.firstName || ''} ${staff.userData.lastName || ''}`.trim(),
          staffEmail: staff.email || ''
        },
        notes: appointmentData.notes || '',
        customerNotes: appointmentData.customerNotes || '',
        remindersSent: [],
        createdBy,
        created_by: createdBy,
        create_date: new Date(),
        updated_by: createdBy,
        update_date: new Date(),
        isValid: true,
        isSynced: true
      };

      const result = await FirestoreService.createWithId(
        this.APPOINTMENT_COLLECTION,
        appointmentId,
        appointment
      );

      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Failed to create appointment'
        };
      }

      updateProgress('Updating statistics...');
      
      // Update store service booking statistics
      // await storeService.updateStoreServiceBookingStats(
      //   storeId,
      //   appointmentData.serviceId,
      //   true, // increment bookings
      //   false, // don't increment completed yet
      //   createdBy
      // );

      return {
        success: true,
        data: appointmentId,
        message: service.requiresApproval 
          ? 'Appointment created and pending approval'
          : 'Appointment created and confirmed'
      };

    } catch (error) {
      console.error('Error creating appointment:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create appointment'
      };
    }
  }

  /**
   * Delete appointment
   */
  async deleteAppointment(
    appointmentId: string,
    deletedBy: string
  ): Promise<ServiceResponse<void>> {
    try {
      const appointmentResult = await FirestoreService.getById<FirestoreDocument>(
        this.APPOINTMENT_COLLECTION,
        appointmentId
      );

      if (!appointmentResult.success || !appointmentResult.data) {
        return {
          success: false,
          error: 'Appointment not found'
        };
      }

      // 软删除：标记为已删除而不是真正删除
      const result = await FirestoreService.update(
        this.APPOINTMENT_COLLECTION,
        appointmentId,
        {
          isValid: false,
          deletedAt: new Date(),
          deletedBy,
          updated_by: deletedBy,
          update_date: new Date()
        }
      );

      if (result.success) {
        return {
          success: true,
          message: 'Appointment deleted successfully'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to delete appointment'
        };
      }

    } catch (error) {
      console.error('Error deleting appointment:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete appointment'
      };
    }
  }

  /**
   * Update appointment details
   */
  async updateAppointment(
    appointmentId: string,
    updateData: {
      customerId?: string;
      staffId?: string;
      serviceId?: string;
      date?: string;
      startTime?: string;
      duration?: number;
      notes?: string;
      customerNotes?: string;
    },
    updatedBy: string
  ): Promise<ServiceResponse<void>> {
    try {
      const appointmentResult = await FirestoreService.getById<FirestoreDocument>(
        this.APPOINTMENT_COLLECTION,
        appointmentId
      );

      if (!appointmentResult.success || !appointmentResult.data) {
        return {
          success: false,
          error: 'Appointment not found'
        };
      }

      const appointment = appointmentResult.data;

      // 构建更新数据
      const updateFields: Record<string, unknown> = {
        updated_by: updatedBy,
        update_date: new Date()
      };

      // 如果更新了服务或员工，需要重新获取相关信息
      if (updateData.serviceId && updateData.serviceId !== appointment.serviceId) {
        const { default: storeService } = await import('./store_services');
        const serviceResult = await storeService.getStoreServiceById(appointment.storeId as string, updateData.serviceId as string);
        
        if (serviceResult.success && serviceResult.data) {
          updateFields.serviceId = updateData.serviceId;
          updateFields['serviceInfo.serviceName'] = `${serviceResult.data.serviceCategory} - ${serviceResult.data.serviceBreed}`;
          updateFields['serviceInfo.serviceCategory'] = serviceResult.data.serviceCategory;
          updateFields['serviceInfo.serviceBreed'] = serviceResult.data.serviceBreed;
        }
      }

      if (updateData.staffId && updateData.staffId !== appointment.staffId) {
        const { default: staffService } = await import('./staff_services');
        const allStaffResult = await staffService.getStoreStaff(appointment.storeId as string);
        
        if (allStaffResult.success && allStaffResult.data) {
          const staff = allStaffResult.data.find((s: any) => s.userData.uid === updateData.staffId);
          if (staff) {
            updateFields.staffId = updateData.staffId;
            updateFields['staffInfo.staffName'] = staff.userData.displayName || `${staff.userData.firstName || ''} ${staff.userData.lastName || ''}`.trim();
            updateFields['staffInfo.staffEmail'] = staff.email || '';
          }
        }
      }

      if (updateData.customerId && updateData.customerId !== appointment.customerId) {
        const customerResult = await FirestoreService.getDocByQueryField(
          this.CUSTOMER_COLLECTION,
          'uid',
          updateData.customerId
        );

        if (customerResult.success && customerResult.data) {
                  const customer = customerResult.data as any;
        updateFields.customerId = updateData.customerId;
        updateFields['customerInfo.name'] = customer.displayName || `${customer.firstName || ''} ${customer.lastName || ''}`.trim();
        updateFields['customerInfo.email'] = customer.email;
        updateFields['customerInfo.phoneNumber'] = customer.phoneNumber || '';
        }
      }

      // 更新时间信息
      if (updateData.date || updateData.startTime || updateData.duration) {
        const timeInfo = { ...(appointment.timeInfo as any) };
        
        if (updateData.date) timeInfo.date = updateData.date;
        if (updateData.startTime) timeInfo.startTime = updateData.startTime;
        if (updateData.duration) timeInfo.duration = updateData.duration;
        
        // 计算结束时间
        if (updateData.startTime || updateData.duration) {
          const startMinutes = this.timeToMinutes(timeInfo.startTime as string);
          const duration = (timeInfo.duration as number) || 60;
          timeInfo.endTime = this.minutesToTime(startMinutes + duration);
        }
        
        updateFields.timeInfo = timeInfo;
      }

      // 更新备注
      if (updateData.notes !== undefined) {
        updateFields.notes = updateData.notes;
      }
      
      if (updateData.customerNotes !== undefined) {
        updateFields.customerNotes = updateData.customerNotes;
      }

      const result = await FirestoreService.update(
        this.APPOINTMENT_COLLECTION,
        appointmentId,
        updateFields
      );

      if (result.success) {
        return {
          success: true,
          message: 'Appointment updated successfully'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to update appointment'
        };
      }

    } catch (error) {
      console.error('Error updating appointment:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update appointment'
      };
    }
  }

  /**
   * Update appointment status
   */
  async updateAppointmentStatus(
    appointmentId: string,
    status: AppointmentStatus,
    updatedBy: string,
    notes?: string
  ): Promise<ServiceResponse<void>> {
    try {
      const appointmentResult = await FirestoreService.getById<FirestoreDocument>(
        this.APPOINTMENT_COLLECTION,
        appointmentId
      );

      if (!appointmentResult.success || !appointmentResult.data) {
        return {
          success: false,
          error: 'Appointment not found'
        };
      }

      const appointment = appointmentResult.data;
      const updateData: Record<string, unknown> = {
        status,
        updated_by: updatedBy,
        update_date: new Date()
      };

      // Set appropriate timestamp based on status
      switch (status) {
        case AppointmentStatus.CONFIRMED:
          updateData.confirmedAt = new Date();
          break;
        case AppointmentStatus.IN_PROGRESS:
          updateData.startedAt = new Date();
          break;
        case AppointmentStatus.COMPLETED:
          updateData.completedAt = new Date();
          if (notes) {
            updateData.completionNotes = notes;
          }
          // Update service completed statistics
          await this.updateServiceCompletedStats(appointment, updatedBy);
          break;
        case AppointmentStatus.CANCELLED:
          updateData.cancelledAt = new Date();
          if (notes) {
            updateData.cancellationReason = notes;
          }
          break;
      }

      const result = await FirestoreService.update(
        this.APPOINTMENT_COLLECTION,
        appointmentId,
        updateData
      );

      if (result.success) {
        return {
          success: true,
          message: 'Appointment status updated successfully'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to update appointment status'
        };
      }

    } catch (error) {
      console.error('Error updating appointment status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update appointment status'
      };
    }
  }

  /**
   * Get staff available dates - 基于员工排班和时间段可用性
   */
  async getStaffAvailableDates(staffId: string, storeId: string): Promise<ServiceResponse<string[]>> {
    try {
      const { default: staffService } = await import('./staff_services');
      
      // 获取员工信息
      const allStaffResult = await staffService.getStoreStaff(storeId);
      if (!allStaffResult.success || !allStaffResult.data) {
        return {
          success: false,
          error: 'Cannot get store staff list'
        };
      }

      const staff = allStaffResult.data.find((s: any) => s.userData.uid === staffId);
      if (!staff) {
        return {
          success: false,
          error: 'Staff member not found in store'
        };
      }

      const availableDates: string[] = [];
      const today = new Date();
      
      // 检查未来60天的可用性
      for (let i = 1; i <= 60; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        const dateString = date.toISOString().split('T')[0];
        
        // 检查员工在该日期的可用性
        const availability = await staffService.getStaffAvailabilityForDate(staff, dateString);
        
        if (availability.success && availability.data) {
          const { isAvailable, workTimes, holidays } = availability.data;
          
          // 如果员工可用且不是假期
          if (isAvailable && !holidays.includes(dateString)) {
            // 检查是否为工作日
            const dayOfWeek = this.getDayOfWeek(dateString);
            const workTime = workTimes.find(wt => wt.weekday === dayOfWeek);
            
            // 如果该天有工作时间安排
            if (workTime && workTime.open) {
              // 进一步检查是否有可用时间段（简化检查 - 只要有工作时间就认为可能有可用时间段）
              availableDates.push(dateString);
            }
          }
        }
      }
      
      return {
        success: true,
        data: availableDates,
        message: 'Available dates retrieved successfully'
      };
      
    } catch (error) {
      console.error('Error getting staff available dates:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get available dates'
      };
    }
  }

  /**
   * Get available time slots - 基于员工排班和已有预约
   */
  async getAvailableTimeSlots(query: TimeSlotQuery): Promise<ServiceResponse<any>> {
    try {
      const { staffId, serviceId, date, duration } = query;
      
      // 1. 获取员工信息和排班 (需要通过 storeId 来获取)
      const { default: staffService } = await import('./staff_services');
      
      // 首先获取 store 的所有员工，然后找到对应的员工
      const allStaffResult = await staffService.getStoreStaff(query.storeId);
      if (!allStaffResult.success || !allStaffResult.data) {
        return {
          success: false,
          error: 'Cannot get store staff list'
        };
      }

      const staff = allStaffResult.data.find((s: any) => s.userData.uid === staffId);
      if (!staff) {
        return {
          success: false,
          error: 'Staff member not found in store'
        };
      }
      
      // 2. 获取员工在指定日期的排班信息
      const availability = await staffService.getStaffAvailabilityForDate(staff, date);
      
      if (!availability.success || !availability.data) {
        return {
          success: false,
          error: 'Unable to get staff availability'
        };
      }

      const { isAvailable, workTimes, holidays } = availability.data;
      
      // 3. 如果员工当天不可用（假期等），返回空时间段
      if (!isAvailable || holidays.includes(date)) {
        return {
          success: true,
          data: {
            date,
            timeSlots: [],
            staffId,
            serviceId,
            reason: 'Staff not available on this date'
          },
          message: 'No available time slots (staff not working)'
        };
      }

      // 4. 获取当天的工作时间
      const dayOfWeek = this.getDayOfWeek(date);
      const workTime = workTimes.find(wt => wt.weekday === dayOfWeek);
      
      if (!workTime || !workTime.open) {
        return {
          success: true,
          data: {
            date,
            timeSlots: [],
            staffId,
            serviceId,
            reason: 'Staff not scheduled to work on this day'
          },
          message: 'No available time slots (not a working day)'
        };
      }

      // 5. 获取当天已有的预约
      const existingAppointments = await this.getStaffAppointmentsForDate(staffId, date);
      
      // 6. 生成基础时间段（每15分钟一个）
      const allTimeSlots = this.generateTimeSlots(workTime.startTime, workTime.endTime);
      
      // 7. 检查每个时间段的可用性
      const timeSlots = allTimeSlots.map(slot => {
        const slotStatus = this.checkTimeSlotAvailability(
          slot, 
          duration, 
          existingAppointments,
          workTime
        );
        
        return {
          startTime: slot.startTime,
          endTime: slot.endTime,
          status: slotStatus,
          available: slotStatus === 'available'
        };
      });

      return {
        success: true,
        data: {
          date,
          timeSlots,
          staffId,
          serviceId,
          workTime: {
            startTime: workTime.startTime,
            endTime: workTime.endTime
          }
        },
        message: 'Available time slots retrieved successfully'
      };

    } catch (error) {
      console.error('Error getting available time slots:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get available time slots'
      };
    }
  }

  /**
   * 获取员工指定日期的预约
   */
  private async getStaffAppointmentsForDate(staffId: string, date: string): Promise<any[]> {
    try {
      const appointmentsResult = await FirestoreService.getMany<FirestoreDocument>(
        this.APPOINTMENT_COLLECTION,
        {
          where: [
            { field: 'staffId', operator: '==', value: staffId },
            { field: 'timeInfo.date', operator: '==', value: date },
            { field: 'status', operator: 'in', value: ['confirmed', 'in_progress'] }
          ],
          orderBy: [{ field: 'timeInfo.startTime', direction: 'asc' }]
        }
      );

      return appointmentsResult.success ? appointmentsResult.data || [] : [];
    } catch (error) {
      console.error('Error getting staff appointments:', error);
      return [];
    }
  }

  /**
   * 生成基础时间段（15分钟间隔）
   */
  private generateTimeSlots(startTime: string, endTime: string): Array<{ startTime: string; endTime: string }> {
    const slots = [];
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = this.timeToMinutes(endTime);

    for (let minutes = startMinutes; minutes < endMinutes; minutes += 15) {
      const slotStart = this.minutesToTime(minutes);
      const slotEnd = this.minutesToTime(minutes + 15);
      
      slots.push({
        startTime: slotStart,
        endTime: slotEnd
      });
    }

    return slots;
  }

  /**
   * 检查时间段可用性
   */
  private checkTimeSlotAvailability(
    slot: { startTime: string; endTime: string },
    serviceDuration: number,
    existingAppointments: any[],
    workTime: { startTime: string; endTime: string }
  ): string {
    const slotStartMinutes = this.timeToMinutes(slot.startTime);
    const slotEndMinutes = this.timeToMinutes(slot.endTime);
    const workEndMinutes = this.timeToMinutes(workTime.endTime);
    
    // 1. 检查这个15分钟时间段是否被现有预约占用
    for (const appointment of existingAppointments) {
      const apptStartMinutes = this.timeToMinutes(appointment.timeInfo.startTime);
      const apptEndMinutes = this.timeToMinutes(appointment.timeInfo.endTime || 
        this.minutesToTime(apptStartMinutes + (appointment.timeInfo.duration || 60)));
      
      // 检查当前15分钟时间段是否与现有预约重叠
      if (this.hasTimeOverlap(slotStartMinutes, slotEndMinutes, apptStartMinutes, apptEndMinutes)) {
        return 'booked'; // 这个15分钟时间段被占用
      }
    }
    
    // 2. 检查从这个时间段开始是否有足够的连续时间完成服务
    const requiredEndMinutes = slotStartMinutes + serviceDuration;
    if (requiredEndMinutes > workEndMinutes) {
      return 'blocked'; // 超出工作时间
    }

    // 3. 检查所需的整个服务时间段是否与现有预约冲突
    for (const appointment of existingAppointments) {
      const apptStartMinutes = this.timeToMinutes(appointment.timeInfo.startTime);
      const apptEndMinutes = this.timeToMinutes(appointment.timeInfo.endTime || 
        this.minutesToTime(apptStartMinutes + (appointment.timeInfo.duration || 60)));
      
      // 检查新服务的完整时间段是否与现有预约重叠
      if (this.hasTimeOverlap(slotStartMinutes, requiredEndMinutes, apptStartMinutes, apptEndMinutes)) {
        return 'blocked'; // 无法完成完整服务
      }
    }

    return 'available';
  }

  /**
   * 检查两个时间段是否重叠
   */
  private hasTimeOverlap(
    start1: number, 
    end1: number, 
    start2: number, 
    end2: number
  ): boolean {
    return start1 < end2 && end1 > start2;
  }

  /**
   * 获取日期对应的星期几
   */
  private getDayOfWeek(dateString: string): string {
    const date = new Date(dateString);
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[date.getDay()];
  }

  // ==================== Helper Methods ====================

  /**
   * Update service completed statistics
   */
  private async updateServiceCompletedStats(appointment: any, updatedBy: string): Promise<void> {
    try {
      const { default: storeService } = await import('./store_services');
      await storeService.updateStoreServiceBookingStats(
        appointment.storeId,
        appointment.serviceId,
        false, // don't increment total bookings
        true,  // increment completed bookings
        updatedBy
      );
    } catch (error) {
      console.error('Error updating service completed stats:', error);
    }
  }

  /**
   * Convert time string to minutes
   */
  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Convert minutes to time string
   */
  private minutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  /**
   * Get appointment by ID
   */
  async getAppointmentById(appointmentId: string): Promise<ServiceResponse<any>> {
    try {
      const appointmentResult = await FirestoreService.getById<FirestoreDocument>(
        this.APPOINTMENT_COLLECTION,
        appointmentId
      );

      if (appointmentResult.success && appointmentResult.data) {
        return {
          success: true,
          data: appointmentResult.data,
          message: 'Appointment retrieved successfully'
        };
      }

      return {
        success: false,
        error: appointmentResult.error || 'Appointment not found'
      };

    } catch (error) {
      console.error('Error getting appointment:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get appointment'
      };
    }
  }

  /**
   * Get customer appointments
   */
  async getCustomerAppointments(
    customerId: string,
    options?: {
      status?: AppointmentStatus;
      limit?: number;
    }
  ): Promise<ServiceListResponse<any>> {
    try {
      const whereConditions: Array<{ field: string; operator: '==' | '!=' | '<' | '<=' | '>' | '>=' | 'in' | 'not-in' | 'array-contains' | 'array-contains-any'; value: unknown }> = [
        { field: 'customerId', operator: '==', value: customerId },
        { field: 'isValid', operator: '==', value: true }
      ];

      if (options?.status) {
        whereConditions.push({ field: 'status', operator: '==', value: options.status });
      }

      const queryOptions: {
        where: typeof whereConditions;
        orderBy: Array<{ field: string; direction: 'asc' | 'desc' }>;
        limit?: number;
      } = {
        where: whereConditions,
        orderBy: [{ field: 'create_date', direction: 'desc' }]
      };

      if (options?.limit) {
        queryOptions.limit = options.limit;
      }

      const appointmentsResult = await FirestoreService.getMany<FirestoreDocument>(
        this.APPOINTMENT_COLLECTION,
        queryOptions
      );

      if (appointmentsResult.success && appointmentsResult.data) {
        return {
          success: true,
          data: appointmentsResult.data,
          total: appointmentsResult.data.length,
          message: 'Customer appointments retrieved successfully'
        };
      }

      return {
        success: false,
        error: appointmentsResult.error || 'Failed to get customer appointments'
      };

    } catch (error) {
      console.error('Error getting customer appointments:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get customer appointments'
      };
    }
  }

  /**
   * Get store appointments
   */
  async getStoreAppointments(
    storeId: string,
    options?: {
      status?: AppointmentStatus;
      staffId?: string;
      date?: string;
      limit?: number;
    }
  ): Promise<ServiceListResponse<any>> {
    try {
      const whereConditions: Array<{ field: string; operator: '==' | '!=' | '<' | '<=' | '>' | '>=' | 'in' | 'not-in' | 'array-contains' | 'array-contains-any'; value: unknown }> = [
        { field: 'storeId', operator: '==', value: storeId },
        { field: 'isValid', operator: '==', value: true }
      ];

      if (options?.status) {
        whereConditions.push({ field: 'status', operator: '==', value: options.status });
      }

      if (options?.staffId) {
        whereConditions.push({ field: 'staffId', operator: '==', value: options.staffId });
      }

      if (options?.date) {
        whereConditions.push({ field: 'timeInfo.date', operator: '==', value: options.date });
      }

      const queryOptions: {
        where: typeof whereConditions;
        orderBy: Array<{ field: string; direction: 'asc' | 'desc' }>;
        limit?: number;
      } = {
        where: whereConditions,
        orderBy: [{ field: 'create_date', direction: 'desc' }]
      };

      if (options?.limit) {
        queryOptions.limit = options.limit;
      }

      const appointmentsResult = await FirestoreService.getMany<FirestoreDocument>(
        this.APPOINTMENT_COLLECTION,
        queryOptions
      );

      if (appointmentsResult.success && appointmentsResult.data) {
        return {
          success: true,
          data: appointmentsResult.data,
          total: appointmentsResult.data.length,
          message: 'Appointments retrieved successfully'
        };
      }

      return {
        success: false,
        error: appointmentsResult.error || 'Failed to get appointments'
      };

    } catch (error) {
      console.error('Error getting store appointments:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get appointments'
      };
    }
  }

  /**
   * Get store dashboard statistics
   */
  async getStoreDashboardStats(storeId: string): Promise<ServiceResponse<any>> {
    try {
      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];
      const currentMonth = today.getMonth();
      const currentYear = today.getFullYear();
      
      // Get today's appointments
      const todayAppointments = await this.getStoreAppointments(storeId, {
        date: todayStr
      });

      // Get this month's appointments
      const monthlyAppointments = await this.getMonthlyAppointments(storeId, currentYear, currentMonth);

      // Get appointment trends for the past 7 days
      const appointmentTrends = await this.getAppointmentTrends(storeId, 7);

      // Get appointment status statistics
      const appointmentStats = await this.getAppointmentStatusStats(storeId);

      // Calculate revenue based on completed appointments
      const revenueData = await this.calculateRevenueFromAppointments(storeId, todayStr, currentYear, currentMonth);

      return {
        success: true,
        data: {
          todayAppointments: todayAppointments.data?.length || 0,
          todayRevenue: revenueData.todayRevenue,
          monthlyRevenue: revenueData.monthlyRevenue,
          appointmentTrends: appointmentTrends.data || [],
          appointmentStats: appointmentStats.data || {},
          todayAppointmentsList: todayAppointments.data || []
        },
        message: 'Dashboard statistics retrieved successfully'
      };

    } catch (error) {
      console.error('Error getting dashboard stats:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get dashboard statistics'
      };
    }
  }

  /**
   * Get monthly appointments
   */
  private async getMonthlyAppointments(storeId: string, year: number, month: number): Promise<ServiceListResponse<any>> {
    try {
      const startDate = new Date(year, month, 1);
      const endDate = new Date(year, month + 1, 0);
      
      const appointments = [];
      
      // Get appointments for each day of the month
      for (let day = 1; day <= endDate.getDate(); day++) {
        const date = new Date(year, month, day);
        const dateStr = date.toISOString().split('T')[0];
        
        const dayAppointments = await this.getStoreAppointments(storeId, {
          date: dateStr
        });
        
        if (dayAppointments.success && dayAppointments.data) {
          appointments.push(...dayAppointments.data);
        }
      }

      return {
        success: true,
        data: appointments,
        total: appointments.length
      };

    } catch (error) {
      console.error('Error getting monthly appointments:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get monthly appointments'
      };
    }
  }

  /**
   * Get appointment trends for the past N days
   */
  private async getAppointmentTrends(storeId: string, days: number): Promise<ServiceResponse<any[]>> {
    try {
      const trends = [];
      const today = new Date();
      
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        
        const dayAppointments = await this.getStoreAppointments(storeId, {
          date: dateStr
        });
        
        const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
        
        trends.push({
          day: dayName,
          date: dateStr,
          appointments: dayAppointments.data?.length || 0,
          completed: dayAppointments.data?.filter(apt => apt.status === AppointmentStatus.COMPLETED).length || 0
        });
      }

      return {
        success: true,
        data: trends
      };

    } catch (error) {
      console.error('Error getting appointment trends:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get appointment trends'
      };
    }
  }

  /**
   * Get appointment status statistics
   */
  private async getAppointmentStatusStats(storeId: string): Promise<ServiceResponse<any>> {
    try {
      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];
      
      // Get recent appointments (last 30 days)
      const stats = {
        draft: 0,
        confirmed: 0,
        inProgress: 0,
        completed: 0,
        cancelled: 0
      };

      for (let i = 0; i < 30; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        
        const dayAppointments = await this.getStoreAppointments(storeId, {
          date: dateStr
        });
        
        if (dayAppointments.success && dayAppointments.data) {
          dayAppointments.data.forEach(apt => {
            switch (apt.status) {
              case AppointmentStatus.DRAFT:
                stats.draft++;
                break;
              case AppointmentStatus.CONFIRMED:
                stats.confirmed++;
                break;
              case AppointmentStatus.IN_PROGRESS:
                stats.inProgress++;
                break;
              case AppointmentStatus.COMPLETED:
                stats.completed++;
                break;
              case AppointmentStatus.CANCELLED:
                stats.cancelled++;
                break;
            }
          });
        }
      }

      return {
        success: true,
        data: stats
      };

    } catch (error) {
      console.error('Error getting appointment status stats:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get appointment status statistics'
      };
    }
  }

  /**
   * Calculate revenue from appointments
   */
  private async calculateRevenueFromAppointments(storeId: string, todayStr: string, currentYear: number, currentMonth: number): Promise<any> {
    try {
      const { default: storeService } = await import('./store_services');
      
      // Get store services to get pricing
      const servicesResult = await storeService.getStoreServices(storeId);
      const services = servicesResult.data || [];
      
      // Calculate today's revenue (using default price since StoreServiceData doesn't have price field)
      const todayAppointments = await this.getStoreAppointments(storeId, {
        date: todayStr,
        status: AppointmentStatus.COMPLETED
      });
      
      let todayRevenue = 0;
      if (todayAppointments.success && todayAppointments.data) {
        // Using default service price of $50 per appointment for now
        todayRevenue = todayAppointments.data.length * 50;
      }

      // Calculate monthly revenue
      const monthlyAppointmentsResult = await this.getMonthlyAppointments(storeId, currentYear, currentMonth);
      let monthlyRevenue = 0;
      
      if (monthlyAppointmentsResult.success && monthlyAppointmentsResult.data) {
        const completedAppointments = monthlyAppointmentsResult.data.filter(apt => apt.status === AppointmentStatus.COMPLETED);
        // Using default service price of $50 per appointment for now
        monthlyRevenue = completedAppointments.length * 50;
      }

      return {
        todayRevenue,
        monthlyRevenue
      };

    } catch (error) {
      console.error('Error calculating revenue:', error);
      return {
        todayRevenue: 0,
        monthlyRevenue: 0
      };
    }
  }

  /**
   * Get store bookable services - 使用 StoreService
   * @deprecated 使用 storeService.getStoreServices() 替代
   */
  async getStoreBookableServices(storeId: string): Promise<ServiceListResponse<any>> {
    try {
      const { default: storeService } = await import('./store_services');
      const result = await storeService.getStoreServices(storeId);
      
      if (result.success && result.data) {
        // 只返回活跃且支持在线预约的服务
        const activeServices = result.data.filter(service => 
          service.status === StoreServiceStatus.ACTIVE && 
          service.isOnlineBookingEnabled
        );
        
        return {
          success: true,
          data: activeServices,
          total: activeServices.length,
          message: 'Store bookable services retrieved successfully'
        };
      }

      return {
        success: false,
        error: result.error || 'Failed to get store services'
      };

    } catch (error) {
      console.error('Error getting store bookable services:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get store services'
      };
    }
  }
}

// Export singleton instance
export const appointmentService = new AppointmentService();
export default appointmentService; 