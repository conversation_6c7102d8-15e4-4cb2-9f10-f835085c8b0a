import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'base_model.dart';

part 'staff_service_offering.g.dart';

/// 员工服务提供模型
/// 存储在 portal-user-data/{userId}/employee-service 子集合中
/// 对应 Web端 portal-user.ts 中的 EmployeeService
@JsonSerializable()
class StaffServiceOffering extends BaseModel {
  String uid;                     // Portal user account sid
  String storeId;                 // Store sid
  String serviceName;             // 服务名称
  String serviceCategory;         // 服务类别 (grooming, boarding, etc.)
  String serviceBreed;            // 服务对象 (dog, cat, etc.)
  String serviceStatus;           // 服务状态 (active, inactive)
  int serviceDuration;            // 服务时长（分钟）
  double serviceAmount;           // 服务费用
  String serviceAmountCurrency;   // 货币类型 (CAD, USD)
  int serviceCount;               // 完成的服务次数
  List<String> servicePhotos;     // 服务照片 URL 数组

  StaffServiceOffering({
    required this.uid,
    required this.storeId,
    required this.serviceName,
    this.serviceCategory = 'grooming',
    this.serviceBreed = 'dog',
    this.serviceStatus = 'active',
    this.serviceDuration = 60,
    this.serviceAmount = 0.0,
    this.serviceAmountCurrency = 'CAD',
    this.serviceCount = 0,
    this.servicePhotos = const [],
    super.sid,
    super.isValid = true,
    super.isSynced = true,
  });

  /// 从 Firestore 文档创建 StaffServiceOffering 对象
  factory StaffServiceOffering.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

    return StaffServiceOffering(
      sid: doc.id,
      uid: data['uid'] ?? '',
      storeId: data['storeId'] ?? '',
      serviceName: data['serviceName'] ?? '',
      serviceCategory: data['serviceCategory'] ?? 'grooming',
      serviceBreed: data['serviceBreed'] ?? 'dog',
      serviceStatus: data['serviceStatus'] ?? 'active',
      serviceDuration: data['serviceDuration'] ?? 60,
      serviceAmount: (data['serviceAmount'] ?? 0.0).toDouble(),
      serviceAmountCurrency: data['serviceAmountCurrency'] ?? 'CAD',
      serviceCount: data['serviceCount'] ?? 0,
      servicePhotos: List<String>.from(data['servicePhotos'] ?? []),
      isValid: data['is_valid'] ?? true,
      isSynced: data['is_synced'] ?? true,
    );
  }

  /// 从 JSON 创建对象
  factory StaffServiceOffering.fromJson(Map<String, dynamic> json) =>
      _$StaffServiceOfferingFromJson(json);

  /// 转换为 JSON
  @override
  Map<String, dynamic> toJson() => _$StaffServiceOfferingToJson(this);

  /// 转换为 Firestore 数据
  Map<String, dynamic> toFirestoreData() {
    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });
    return dbData;
  }

  /// 集合名称（子集合）
  static String get collection => 'employee-service';

  /// 复制对象
  StaffServiceOffering copyWith({
    String? uid,
    String? storeId,
    String? serviceName,
    String? serviceCategory,
    String? serviceBreed,
    String? serviceStatus,
    int? serviceDuration,
    double? serviceAmount,
    String? serviceAmountCurrency,
    int? serviceCount,
    List<String>? servicePhotos,
  }) {
    return StaffServiceOffering(
      uid: uid ?? this.uid,
      storeId: storeId ?? this.storeId,
      serviceName: serviceName ?? this.serviceName,
      serviceCategory: serviceCategory ?? this.serviceCategory,
      serviceBreed: serviceBreed ?? this.serviceBreed,
      serviceStatus: serviceStatus ?? this.serviceStatus,
      serviceDuration: serviceDuration ?? this.serviceDuration,
      serviceAmount: serviceAmount ?? this.serviceAmount,
      serviceAmountCurrency: serviceAmountCurrency ?? this.serviceAmountCurrency,
      serviceCount: serviceCount ?? this.serviceCount,
      servicePhotos: servicePhotos ?? this.servicePhotos,
      sid: sid,
      isValid: isValid,
      isSynced: isSynced,
    );
  }

  /// 是否为活跃服务
  bool isActive() {
    return serviceStatus == 'active';
  }

  /// 获取服务价格显示文本
  String getPriceDisplay() {
    return '\$${serviceAmount.toStringAsFixed(2)} $serviceAmountCurrency';
  }

  /// 获取服务时长显示文本
  String getDurationDisplay() {
    final hours = serviceDuration ~/ 60;
    final minutes = serviceDuration % 60;

    if (hours > 0 && minutes > 0) {
      return '${hours}h ${minutes}m';
    } else if (hours > 0) {
      return '${hours}h';
    } else {
      return '${minutes}m';
    }
  }
}