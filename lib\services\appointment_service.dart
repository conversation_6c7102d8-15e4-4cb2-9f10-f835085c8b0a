import 'dart:async';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/models/models_i.dart';

/// 预约业务逻辑服务
class AppointmentService {
  final AppointmentDao _appointmentDao = AppointmentDao();
  final PortalUserDao _portalUserDao = PortalUserDao();
  // Note: StaffDao, StoreServiceDao and CustomerDao were removed as they are no longer needed
  // Services are now managed through PortalUserDao sub-collections

  // ==================== 预约创建和管理 ====================

  /// 创建新预约
  Future<String> createAppointment({
    required String storeId,
    required String customerId,
    required String staffId,
    required String serviceId,
    required DateTime appointmentDate,
    required String startTime,
    required String endTime,
    String? notes,
    String? customerNotes,
    required String createdBy,
  }) async {
    // 1. 验证输入数据
    await _validateAppointmentData(
      storeId: storeId,
      customerId: customerId,
      staffId: staffId,
      serviceId: serviceId,
      appointmentDate: appointmentDate,
      startTime: startTime,
      endTime: endTime,
    );

    // 2. 检查时间冲突
    bool hasConflict = await _appointmentDao.checkTimeSlotConflict(
      storeId,
      staffId,
      appointmentDate,
      startTime,
      endTime,
    );

    if (hasConflict) {
      throw Exception('所选时间段已被预约，请选择其他时间');
    }

    // 3. 获取相关信息用于快照
    // Note: Customer validation removed as we now use different architecture
    final staff = await _portalUserDao.getPortalUserDataByUid(staffId);

    if (staff == null) {
      throw Exception('员工不存在');
    }

    // 4. 创建预约对象
    final appointmentId = uuid.v4();
    final appointment = Appointment(
      appointmentId: appointmentId,
      storeId: storeId,
      customerId: customerId,
      staffId: staffId,
      serviceId: serviceId,
      status: 'pending',
      source: 'portal',
      timeInfo: TimeInfo(
        date: _formatDate(appointmentDate),
        startTime: startTime,
        endTime: endTime,
        duration: _calculateDuration(startTime, endTime),
        timezone: 'America/Toronto',
      ),
      customerInfo: CustomerInfo(
        name: 'Customer Name', // TODO: Get from customer data
        email: '<EMAIL>',
        phoneNumber: '+****************',
      ),
      serviceInfo: ServiceInfo(
        serviceName: 'Service Name', // TODO: Get from employee service
        serviceCategory: ServiceCategory.grooming,
        serviceBreed: ServiceBreed.dog,
        duration: _calculateDuration(startTime, endTime),
        price: 0.0, // TODO: Get from employee service
        currency: Currency.cad,
      ),
      staffInfo: StaffInfo(
        staffName: staff.getDisplayName(),
        staffEmail: staff.phoneNumber,
      ),
      notes: notes,
      customerNotes: customerNotes,
      remindersSent: [],
      createdBy: createdBy,
      sid: appointmentId,
    );

    // 5. 保存预约
    await _appointmentDao.addAppointment(appointment);

    return appointmentId;
  }

  /// 确认预约
  Future<void> confirmAppointment(String appointmentId, String confirmedBy) async {
    final appointment = await _appointmentDao.getAppointmentById(appointmentId);
    if (appointment == null) {
      throw Exception('预约不存在');
    }

    if (!appointment.canBeCancelled()) {
      throw Exception('当前状态的预约无法确认');
    }

    // 创建更新后的预约对象
    final updatedAppointment = Appointment(
      appointmentId: appointment.appointmentId,
      storeId: appointment.storeId,
      customerId: appointment.customerId,
      staffId: appointment.staffId,
      serviceId: appointment.serviceId,
      status: 'confirmed',
      source: appointment.source,
      timeInfo: appointment.timeInfo,
      customerInfo: appointment.customerInfo,
      serviceInfo: appointment.serviceInfo,
      staffInfo: appointment.staffInfo,
      notes: appointment.notes,
      customerNotes: appointment.customerNotes,
      remindersSent: appointment.remindersSent,
      createdBy: appointment.createdBy,
      sid: appointment.sid,
      updatedBy: confirmedBy,
      updateDate: DateTimeUtil.currentMilliseconds(),
      isValid: appointment.isValid,
      isSynced: appointment.isSynced,
      createDate: appointment.createDate,
      reviewedBy: appointment.reviewedBy,
      reviewDate: appointment.reviewDate,
      approveStatus: appointment.approveStatus,
      tags: appointment.tags,
    );

    await _appointmentDao.updateAppointment(updatedAppointment);
  }

  /// 开始预约服务
  Future<void> startAppointment(String appointmentId, String startedBy) async {
    final appointment = await _appointmentDao.getAppointmentById(appointmentId);
    if (appointment == null) {
      throw Exception('预约不存在');
    }

    if (!appointment.canBeStarted()) {
      throw Exception('当前状态的预约无法开始');
    }

    // 更新预约状态为进行中
    appointment.status = 'in_progress';
    appointment.updatedBy = startedBy;
    appointment.updateDate = DateTimeUtil.currentMilliseconds();

    await _appointmentDao.updateAppointment(appointment);
  }

  /// 完成预约服务
  Future<void> completeAppointment(
    String appointmentId,
    String completedBy,
    {String? completionNotes}
  ) async {
    final appointment = await _appointmentDao.getAppointmentById(appointmentId);
    if (appointment == null) {
      throw Exception('预约不存在');
    }

    if (!appointment.canBeCompleted()) {
      throw Exception('当前状态的预约无法完成');
    }

    // 更新预约状态为已完成
    appointment.status = 'completed';
    appointment.notes = completionNotes;
    appointment.updatedBy = completedBy;
    appointment.updateDate = DateTimeUtil.currentMilliseconds();

    await _appointmentDao.updateAppointment(appointment);

    // 更新服务统计
    await _updateServiceStats(appointment.serviceId);
  }

  /// 取消预约
  Future<void> cancelAppointment(
    String appointmentId,
    String cancelledBy,
    {String? cancellationReason}
  ) async {
    final appointment = await _appointmentDao.getAppointmentById(appointmentId);
    if (appointment == null) {
      throw Exception('预约不存在');
    }

    if (!appointment.canBeCancelled()) {
      throw Exception('当前状态的预约无法取消');
    }

    // 更新预约状态为已取消
    appointment.status = 'cancelled';
    appointment.notes = cancellationReason;
    appointment.updatedBy = cancelledBy;
    appointment.updateDate = DateTimeUtil.currentMilliseconds();

    await _appointmentDao.updateAppointment(appointment);
  }

  /// 重新安排预约时间
  Future<void> rescheduleAppointment(
    String appointmentId,
    DateTime newDate,
    String newStartTime,
    String newEndTime,
    String updatedBy,
  ) async {
    final appointment = await _appointmentDao.getAppointmentById(appointmentId);
    if (appointment == null) {
      throw Exception('预约不存在');
    }

    // 检查新时间是否有冲突
    bool hasConflict = await _appointmentDao.checkTimeSlotConflict(
      appointment.storeId,
      appointment.staffId,
      newDate,
      newStartTime,
      newEndTime,
      excludeAppointmentId: appointmentId,
    );

    if (hasConflict) {
      throw Exception('新的时间段已被预约，请选择其他时间');
    }

    // 更新时间信息
    appointment.timeInfo.date = _formatDate(newDate);
    appointment.timeInfo.startTime = newStartTime;
    appointment.timeInfo.endTime = newEndTime;
    appointment.timeInfo.duration = _calculateDuration(newStartTime, newEndTime);
    appointment.updatedBy = updatedBy;
    appointment.updateDate = DateTimeUtil.currentMilliseconds();

    await _appointmentDao.updateAppointment(appointment);
  }

  // ==================== 查询方法 ====================

  /// 获取店铺的预约列表
  Future<List<Appointment>> getStoreAppointments(
    String storeId, {
    AppointmentStatus? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (status != null) {
      return await _appointmentDao.getAppointmentsByStatus(storeId, status.code);
    } else if (startDate != null && endDate != null) {
      return await _appointmentDao.getAppointmentsByDateRange(storeId, startDate, endDate);
    } else {
      return await _appointmentDao.getAppointmentsByStoreId(storeId);
    }
  }

  /// 获取员工的预约列表
  Future<List<Appointment>> getStaffAppointments(String staffId) async {
    return await _appointmentDao.getAppointmentsByStaffId(staffId);
  }

  /// 获取客户的预约列表
  Future<List<Appointment>> getCustomerAppointments(String customerId) async {
    return await _appointmentDao.getAppointmentsByUserId(customerId);
  }

  /// 获取今日预约
  Future<List<Appointment>> getTodayAppointments(String storeId) async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

    return await _appointmentDao.getAppointmentsByDateRange(storeId, startOfDay, endOfDay);
  }

  /// 获取即将到来的预约
  Future<List<Appointment>> getUpcomingAppointments(String storeId, {int days = 7}) async {
    final now = DateTime.now();
    final endDate = now.add(Duration(days: days));

    final appointments = await _appointmentDao.getAppointmentsByDateRange(storeId, now, endDate);
    return appointments.where((apt) => apt.isUpcoming()).toList();
  }

  // ==================== 辅助方法 ====================

  /// 验证预约数据
  Future<void> _validateAppointmentData({
    required String storeId,
    required String customerId,
    required String staffId,
    required String serviceId,
    required DateTime appointmentDate,
    required String startTime,
    required String endTime,
  }) async {
    // 验证日期不能是过去
    if (appointmentDate.isBefore(DateTime.now().subtract(Duration(days: 1)))) {
      throw Exception('预约日期不能是过去的日期');
    }

    // 验证时间格式和逻辑
    if (!_isValidTimeFormat(startTime) || !_isValidTimeFormat(endTime)) {
      throw Exception('时间格式无效，请使用 HH:MM 格式');
    }

    if (_timeToMinutes(startTime) >= _timeToMinutes(endTime)) {
      throw Exception('结束时间必须晚于开始时间');
    }

    // 验证相关实体是否存在
    // Note: Customer validation removed as we now use different architecture
    print('🔍 [验证阶段] 查找员工信息: staffId=$staffId');
    final staff = await _portalUserDao.getPortalUserDataByUid(staffId);
    print('📋 [验证阶段] 员工查找结果: ${staff != null ? "找到员工" : "未找到员工"}');
    if (staff != null) {
      print('📋 [验证阶段] 员工详细信息: uid=${staff.uid}, displayName=${staff.displayName}, firstName=${staff.firstName}, lastName=${staff.lastName}');
    }
    // Note: Service validation will be done through PortalUserDao

    if (staff == null) {
      throw Exception('员工不存在');
    }

    // TODO: Add service validation through PortalUserDao
    // TODO: Add store staff validation through PortalUserDao
    // For now, we'll skip the store staff validation as the old model is removed
  }

  /// 计算时长（分钟）
  int _calculateDuration(String startTime, String endTime) {
    return _timeToMinutes(endTime) - _timeToMinutes(startTime);
  }

  /// 将时间字符串转换为分钟数
  int _timeToMinutes(String time) {
    final parts = time.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  /// 验证时间格式
  bool _isValidTimeFormat(String time) {
    final regex = RegExp(r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$');
    return regex.hasMatch(time);
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 更新服务统计
  Future<void> _updateServiceStats(String serviceId) async {
    // 这里可以添加更新服务预约统计的逻辑
    // 例如增加总预约数、完成预约数等
  }


}