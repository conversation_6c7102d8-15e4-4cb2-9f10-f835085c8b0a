import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onenata_app/models/pet.dart';
import 'package:onenata_app/models/bookable_service.dart';
import 'package:onenata_app/services/bookable_service_service.dart';
import 'package:onenata_app/views/theme/colors/onenata_classic_colors.dart';
import 'package:onenata_app/views/component/appointment/datetime_selection_bottom_sheet.dart';

// 服务类型数据模型
class ServiceType {
  final String id;
  final String name;
  final String description;
  final int durationMinutes;
  final double price;
  final bool isAvailable;

  ServiceType({
    required this.id,
    required this.name,
    required this.description,
    required this.durationMinutes,
    required this.price,
    this.isAvailable = true,
  });
}

class ServiceSelectionBottomSheet extends StatefulWidget {
  final String? storeId;
  final Pet selectedPet;
  final VoidCallback? onServiceSelected;

  const ServiceSelectionBottomSheet({
    Key? key,
    this.storeId,
    required this.selectedPet,
    this.onServiceSelected,
  }) : super(key: key);

  @override
  State<ServiceSelectionBottomSheet> createState() => _ServiceSelectionBottomSheetState();
}

class _ServiceSelectionBottomSheetState extends State<ServiceSelectionBottomSheet> {
  BookableService? selectedService;
  List<BookableService> services = [];
  bool isLoading = true;
  String? errorMessage;

  final BookableServiceService _serviceService = BookableServiceService();

  @override
  void initState() {
    super.initState();
    _loadServices();
  }

  Future<void> _loadServices() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      List<BookableService> loadedServices;

      if (widget.storeId != null) {
        // 从数据库加载真实服务
        loadedServices = await _serviceService.getActiveBookableServices(widget.storeId!);
      } else {
        // 使用测试数据
        loadedServices = await _serviceService.createTestServices('test_store_id');
      }

      setState(() {
        services = loadedServices;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load services: $e';
        isLoading = false;
      });
    }
  }

  void _selectService(BookableService service) {
    if (service.isAvailable) {
      setState(() {
        selectedService = service;
      });
    }
  }

  void _continueToNext() {
    if (selectedService != null) {
      // 关闭当前弹窗并打开日期时间选择弹窗
      Get.back();
      DateTimeSelectionBottomSheetHelper.show(
        selectedPet: widget.selectedPet,
        selectedService: selectedService!,
        onAppointmentConfirmed: () {
          widget.onServiceSelected?.call();
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 403.w,
      height: 823.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(26.w)),
      ),
      child: Column(
        children: [
          // 拖拽指示条
          Container(
            margin: EdgeInsets.only(top: 12.w),
            width: 36.w,
            height: 4.w,
            decoration: BoxDecoration(
              color: Color(0xFFE0E0E0),
              borderRadius: BorderRadius.circular(2.w),
            ),
          ),
          
          // 标题
          Padding(
            padding: EdgeInsets.only(top: 20.w, left: 24.w, right: 24.w),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Please select a service type',
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF262626),
                ),
              ),
            ),
          ),
          
          // 服务列表
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.w),
              child: _buildServiceList(),
            ),
          ),

          // 底部按钮
          if (!isLoading && services.isNotEmpty && selectedService != null)
            _buildBottomButton(),
        ],
      ),
    );
  }

  Widget _buildServiceList() {
    if (isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: OneNataClassicColors.veronica,
        ),
      );
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.w,
              color: Colors.red,
            ),
            SizedBox(height: 16.w),
            Text(
              errorMessage!,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.w),
            ElevatedButton(
              onPressed: _loadServices,
              child: Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (services.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.spa_outlined,
              size: 64.w,
              color: Color(0xFF999999),
            ),
            SizedBox(height: 16.w),
            Text(
              'No services available',
              style: TextStyle(
                fontSize: 16.sp,
                color: Color(0xFF999999),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: services.length,
      itemBuilder: (context, index) {
        final service = services[index];
        final isSelected = selectedService?.serviceId == service.serviceId;
                  
                  return GestureDetector(
                    onTap: () => _selectService(service),
                    child: Container(
                      width: 355.w,
                      height: 98.w,
                      margin: EdgeInsets.only(bottom: 12.w),
                      padding: EdgeInsets.all(10.w),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12.w),
                        border: Border.all(
                          color: isSelected 
                              ? OneNataClassicColors.veronica 
                              : Color(0xFFE5E5E5),
                          width: isSelected ? 2.w : 1.w,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 8.w,
                            offset: Offset(0, 2.w),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 服务名称和价格
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                service.serviceName,
                                style: TextStyle(
                                  fontFamily: 'Manrope',
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                  color: service.isAvailable
                                      ? Color(0xFF262626)
                                      : Color(0xFF999999),
                                ),
                              ),
                              Text(
                                'CA \$${service.basePrice.toStringAsFixed(0)}',
                                style: TextStyle(
                                  fontFamily: 'Manrope',
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w600,
                                  color: service.isAvailable
                                      ? Color(0xFF262626)
                                      : Color(0xFF999999),
                                ),
                              ),
                            ],
                          ),
                          
                          SizedBox(height: 4.w),
                          
                          // 服务描述
                          Text(
                            service.description ?? 'No description available',
                            style: TextStyle(
                              fontFamily: 'Manrope',
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w400,
                              color: service.isAvailable
                                  ? Color(0xFF666666)
                                  : Color(0xFF999999),
                            ),
                          ),
                          
                          SizedBox(height: 8.w),
                          
                          // 时长
                          Text(
                            service.formattedDuration,
                            style: TextStyle(
                              fontFamily: 'Manrope',
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w500,
                              color: OneNataClassicColors.veronica,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
  }

  Widget _buildBottomButton() {
    return Container(
            margin: EdgeInsets.only(bottom: 20.w, left: 24.w, right: 24.w),
            width: double.infinity,
            height: 48.w,
            child: ElevatedButton(
              onPressed: selectedService != null ? _continueToNext : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: selectedService != null 
                    ? Color(0xFFF2D3A4) 
                    : Color(0xFFC6C6C6),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.w),
                ),
                elevation: 0,
              ),
              child: Text(
                'Continue',
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          );
  }
}

/// 显示服务选择底部弹窗的静态方法
class ServiceSelectionBottomSheetHelper {
  static void show({
    String? storeId,
    required Pet selectedPet,
    VoidCallback? onServiceSelected,
  }) {
    Get.bottomSheet(
      ServiceSelectionBottomSheet(
        storeId: storeId,
        selectedPet: selectedPet,
        onServiceSelected: onServiceSelected,
      ),
      isDismissible: true,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }
}
