/// 服务类别枚举
/// 对应 web端 types.ts 中的 ServiceCategory
enum ServiceCategory {
  /// 美容
  grooming('GROOMING'),
  /// 寄养
  boarding('BOARDING'),
  /// 兽医
  veterinary('VETERINARY'),
  /// 训练
  training('TRAINING'),
  /// 零售
  retail('RETAIL'),
  /// 其他
  other('OTHER');

  const ServiceCategory(this.value);
  final String value;

  static ServiceCategory fromString(String value) {
    return ServiceCategory.values.firstWhere(
      (category) => category.value == value,
      orElse: () => ServiceCategory.grooming,
    );
  }

  String get displayName {
    switch (this) {
      case ServiceCategory.grooming:
        return '美容';
      case ServiceCategory.boarding:
        return '寄养';
      case ServiceCategory.veterinary:
        return '兽医';
      case ServiceCategory.training:
        return '训练';
      case ServiceCategory.retail:
        return '零售';
      case ServiceCategory.other:
        return '其他';
    }
  }
}

/// 服务品种枚举
/// 对应 web端 types.ts 中的 ServiceBreed
enum ServiceBreed {
  /// 狗
  dog('DOG'),
  /// 猫
  cat('CAT'),
  /// 鸟
  bird('BIRD'),
  /// 兔子
  rabbit('RABBIT'),
  /// 其他
  other('OTHER');

  const ServiceBreed(this.value);
  final String value;

  static ServiceBreed fromString(String value) {
    return ServiceBreed.values.firstWhere(
      (breed) => breed.value == value,
      orElse: () => ServiceBreed.dog,
    );
  }

  String get displayName {
    switch (this) {
      case ServiceBreed.dog:
        return '狗';
      case ServiceBreed.cat:
        return '猫';
      case ServiceBreed.bird:
        return '鸟';
      case ServiceBreed.rabbit:
        return '兔子';
      case ServiceBreed.other:
        return '其他';
    }
  }
}

/// 服务状态枚举
enum ServiceStatus {
  /// 活跃
  active('ACTIVE'),
  /// 非活跃
  inactive('INACTIVE'),
  /// 暂停
  suspended('SUSPENDED'),
  /// 已删除
  deleted('DELETED');

  const ServiceStatus(this.value);
  final String value;

  static ServiceStatus fromString(String value) {
    return ServiceStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => ServiceStatus.active,
    );
  }

  String get displayName {
    switch (this) {
      case ServiceStatus.active:
        return '活跃';
      case ServiceStatus.inactive:
        return '非活跃';
      case ServiceStatus.suspended:
        return '暂停';
      case ServiceStatus.deleted:
        return '已删除';
    }
  }
}

/// 店铺服务状态枚举
/// 对应 web端 types.ts 中的 StoreServiceStatus
enum StoreServiceStatus {
  /// 活跃
  active('ACTIVE'),
  /// 非活跃
  inactive('INACTIVE'),
  /// 草稿
  draft('DRAFT'),
  /// 已删除
  deleted('DELETED');

  const StoreServiceStatus(this.value);
  final String value;

  static StoreServiceStatus fromString(String value) {
    return StoreServiceStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => StoreServiceStatus.inactive,
    );
  }

  String get displayName {
    switch (this) {
      case StoreServiceStatus.active:
        return '活跃';
      case StoreServiceStatus.inactive:
        return '非活跃';
      case StoreServiceStatus.draft:
        return '草稿';
      case StoreServiceStatus.deleted:
        return '已删除';
    }
  }
}