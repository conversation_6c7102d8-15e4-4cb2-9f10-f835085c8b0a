import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'base_full_model.dart';

part 'portal_user_data.g.dart';

@JsonSerializable()
class UserPreferences {
  String language;                // 语言设置 (zh-CN, en-US)
  bool notificationsEnabled;      // 是否启用通知

  UserPreferences({
    this.language = 'en-US',
    this.notificationsEnabled = true,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) =>
      _$UserPreferencesFromJson(json);

  Map<String, dynamic> toJson() => _$UserPreferencesToJson(this);
}

/// Portal User Data - 商家系统用户详细信息
/// 存储在 Firestore collection "portal-user-data"
/// 对应Web端的 portal-user-data 集合
@JsonSerializable()
class PortalUserData extends BaseFullModel {
  String uid;                     // 关联 portal-user-account.sid
  String firstName;               // 名
  String lastName;                // 姓
  String displayName;             // 显示名称
  String? bio;                    // 个人简介
  String phoneNumber;             // 电话号码
  String? photoURL;               // 头像URL
  String? invitationCode;         // 邀请码
  String userType;                // 用户类型 (202 = staff)
  UserPreferences preferences;    // 用户偏好设置

  PortalUserData({
    required this.uid,
    required this.firstName,
    required this.lastName,
    required this.displayName,
    this.bio,
    required this.phoneNumber,
    this.photoURL,
    this.invitationCode,
    this.userType = '202',
    UserPreferences? preferences,
    super.sid,
    super.isValid = true,
    super.isSynced = true,
  }) : preferences = preferences ?? UserPreferences();

  /// 从 Firestore 文档创建 PortalUserData 对象
  factory PortalUserData.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

    return PortalUserData(
      sid: doc.id,
      uid: data['uid'] ?? '',
      firstName: data['firstName'] ?? data['first_name'] ?? '',
      lastName: data['lastName'] ?? data['last_name'] ?? '',
      displayName: data['displayName'] ?? data['display_name'] ?? '',
      bio: data['bio'],
      phoneNumber: data['phoneNumber'] ?? data['phone_number'] ?? '',
      photoURL: data['photoURL'] ?? data['photo_url'],
      invitationCode: data['invitationCode'] ?? data['invitation_code'],
      userType: data['userType'] ?? data['user_type'] ?? '202',
      preferences: data['preferences'] != null
          ? (data['preferences'] is UserPreferences
              ? data['preferences']
              : UserPreferences.fromJson(Map<String, dynamic>.from(data['preferences'])))
          : UserPreferences(),
      isValid: data['is_valid'] ?? true,
      isSynced: data['is_synced'] ?? true,
    );
  }

  /// 从 JSON 创建对象
  factory PortalUserData.fromJson(Map<String, dynamic> json) =>
      _$PortalUserDataFromJson(json);

  /// 转换为 JSON
  @override
  Map<String, dynamic> toJson() {
    final json = _$PortalUserDataToJson(this);
    // Manually serialize preferences to ensure proper JSON conversion
    json['preferences'] = preferences.toJson();
    return json;
  }

  /// 转换为 Firestore 数据
  Map<String, dynamic> toFirestoreData() {
    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });
    return dbData;
  }

  /// 集合名称
  static String get collection => 'portal-user-data';

  /// 获取全名
  String getFullName() {
    return '$firstName $lastName'.trim();
  }

  /// 是否为员工
  bool isStaff() {
    return userType == '202';
  }

  /// 是否有头像
  bool hasAvatar() {
    return photoURL != null && photoURL!.isNotEmpty;
  }

  /// 获取显示名称（优先使用displayName，否则使用全名）
  String getDisplayName() {
    return displayName.isNotEmpty ? displayName : getFullName();
  }

  /// 是否完成了基本信息
  bool isProfileComplete() {
    return firstName.isNotEmpty && 
           lastName.isNotEmpty && 
           phoneNumber.isNotEmpty;
  }
}
