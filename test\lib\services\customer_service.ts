// Firebase imports not needed as we use FirestoreService
import { 
  // StoreCustomer,
  // StoreCustomerImpl
} from '../models/appointment';
import { 
  UserData,
  UserAccount,
  Pet,
  Customer
} from '../models/customer';
import { 
  // CustomerSource,
  CustomerStatus,
  // PetType,
  // StoreRole,
  UserType
} from '../models/types';
import { v4 as uuidv4 } from 'uuid';
import { FirestoreDocument, FirestoreService } from '../firebase/services/firestore';
import { ProgressCallback } from '../types/common';
import { checkEmailExists, checkPhoneExists } from '../firebase/services/auth';
import { createCustomerAccount } from '../firebase/services/auth';
import { StoreInfo } from '../models/store';


// ==================== Types and Interfaces ====================

export interface CreateCustomerFromOneNataData {
  userData: UserData;
  userAccount: UserAccount;
  pet: Pet;
  storeId: string;
  notes?: string;
}

export interface CustomerSearchOptions {
  searchTerm?: string;
  // source?: CustomerSource;
  status?: CustomerStatus;
  limit?: number;
  offset?: number;
}

export interface CustomerWithPets extends Customer {
  pets?: Pet[];
  oneNataUserData?: UserData;
  oneNataUserAccount?: UserAccount;
}

export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ServiceListResponse<T> extends ServiceResponse<T[]> {
  total?: number;
}

export interface CreateCustomerAccountRequest {
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  note?: string | null;
  password?: string; // 临时密码
}


// ==================== Customer Service Class ====================

/**
 * Customer Service - 客户管理服务类
 * 负责处理店铺客户管理，包括OneNata用户集成
 */
export class CustomerService {
  private readonly STORE_CUSTOMER_COLLECTION = 'store-customer';
  private readonly ONENATA_USER_DATA_COLLECTION = 'user-data';
  private readonly ONENATA_USER_ACCOUNT_COLLECTION = 'user-account';
  private readonly ONENATA_PET_COLLECTION = 'pet';
  // private readonly PORTAL_USER_DATA_COLLECTION = 'portal-user-data';
  private readonly STORE_INFO_COLLECTION = 'store-info';

  // ==================== Customer Management ====================

  /**
   * Create customer
   */
  async createCustomer(
    data: CreateCustomerAccountRequest,
    storeId: string,
    createdBy: string,
    onProgress?: ProgressCallback
  ): Promise<ServiceResponse<string>> {
    try {
      const totalSteps = 8;
      let currentStep = 0;

      const updateProgress = (message: string) => {
        currentStep++;
        onProgress?.(currentStep, totalSteps, message);
      };

        // 1. 验证输入数据
        updateProgress('Validating input data...');
        if (!data.email || !data.firstName || !data.lastName) {
          return {
            success: false,
            error: 'need email, firstName, lastName for your customer'
          };
        }
        await new Promise(resolve => setTimeout(resolve, 500)); // 模拟验证时间 
        
        const emailExists = await checkEmailExists(data.email);   
        if (emailExists) {
          return {
            success: false,
            error: 'email already exists'
          };
        }

        const phoneExists = await checkPhoneExists(data.phoneNumber!);
        if (phoneExists) {
          return {
            success: false,
            error: 'phone number already exists'
          };
        }

        updateProgress('Checking if phone number exists...');

        await new Promise(resolve => setTimeout(resolve, 500)); // 模拟验证时间

        updateProgress('checking password...');

        if (!data.password) {
          return {
            success: false,
            error: 'need password for your customer'
          };
        }
        
        updateProgress('creating customer...');
        const customerAccountSid = await createCustomerAccount(
          data.email,
          data.password,
          createdBy,
          data.phoneNumber,
          true
        );

        if (!customerAccountSid) {
          return {
            success: false,
            error: 'Failed to create customer account'
          };
        }

        if (!data.note) {
          data.note = '';
        }

        updateProgress('creating customer data...');
        const customerDataSid = uuidv4();
        const customerData: Partial<UserData> = {
          sid: customerDataSid,
          uid: customerAccountSid,
          firstName: data.firstName,
          lastName: data.lastName,
          userType: UserType.PETSTORE_CUSTOMER_FROM_PORTAL,
          email: data.email,
          phoneNumber: data.phoneNumber,
          bio: data.note ? data.note : '',
          photoURL: "",
        };

        // 创建 UserData 文档
        const customerDataResult = await FirestoreService.createWithId(
          this.ONENATA_USER_DATA_COLLECTION,
          customerDataSid,
          customerData
        );

        if (!customerDataResult.success) {
          return {
            success: false,
            error: 'Failed to create customer data'
          };
        }

        // 创建 UserAccount 文档
        // const customerAccountData: Partial<UserAccount> = {
        //   sid: customerAccountSid,
        //   fid: customerAccountSid,
        //   email: data.email,
        //   phoneNumber: data.phoneNumber,
        //   isEmailVerified: false,
        //   create_date: new Date(),
        //   update_date: new Date(),
        //   created_by: createdBy,
        //   updated_by: createdBy
        // };

        // const customerAccountResult = await FirestoreService.createWithId(
        //   this.ONENATA_USER_ACCOUNT_COLLECTION,
        //   customerAccountSid,
        //   customerAccountData
        // );

        // if (!customerAccountResult.success) {
        //   return {
        //     success: false,
        //     error: 'Failed to create customer account'
        //   };
        // }

        updateProgress('adding customer to store...');
        
        const storeCustomerAddResult = await FirestoreService.arrayAddByQueryDocId(
          this.STORE_INFO_COLLECTION,
          storeId,
          'storeId',
          'customerList',
          customerAccountSid
        );

        if (!storeCustomerAddResult.success) {
          return {
            success: false,
            error: 'Failed to add customer to store'
          };
        }

        return {
          success: true,
          data: customerDataSid,
          message: 'Customer created successfully'
        };

      } catch (error) {
      console.error('Error creating customer from OneNata user:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create customer'
      };
    }
  }
  
  /**
   * get customers by store
   */
  async getCustomersByStore(storeId: string): Promise<ServiceListResponse<Customer>> {
    try {

      const storeInfoResult = await FirestoreService.getDocByQueryField<FirestoreDocument>(
        this.STORE_INFO_COLLECTION,
        'storeId',
        storeId
      );

      const customers = (storeInfoResult.data as unknown as StoreInfo).customerList || [];

      if (!storeInfoResult.success || !storeInfoResult.data) {
        return {
          success: false,
          error: 'Failed to get customers by store'   
        };
      }

      const customerResults: Customer[] = [];

      console.log('customers', customers);
      for (const customerUid of customers) {
      
        // UserAccount
        const userAccountResult = await FirestoreService.getDocByQueryField<FirestoreDocument>(
          this.ONENATA_USER_ACCOUNT_COLLECTION,
          'sid',
          customerUid
        );

        if (!userAccountResult.success || !userAccountResult.data) {
          console.error('Failed to get customer User Account:', userAccountResult.error);
          throw new Error('Failed to get customer account');
        }

        const userAccount = userAccountResult.data as unknown as UserAccount;

        // UserData
        const customerResult = await FirestoreService.getDocByQueryField<FirestoreDocument>(
          this.ONENATA_USER_DATA_COLLECTION,
          'uid',
          customerUid
        );

        if (!customerResult.success || !customerResult.data) {
          console.error('Failed to get customer User Data:', customerResult.error);
          throw new Error('Failed to get customer data');
        }
      

        const userData = customerResult.data as unknown as UserData;
        
        // Get customer pets using UserData.uid as owner
        const petsResult = await this.getCustomerPets(customerUid);
        const pets = petsResult.success ? petsResult.data  : [];
        console.log('pets', pets);
        // const customerSource = CustomerSource.PORTAL_CREATED;
        
        customerResults.push({
          customerAccount: userAccount,
          customerData: userData,
          pets: pets // Add pets to customer object
        });
        
      }

      console.log('customerResults', customerResults);
      return {
        success: true,
        data: customerResults, 
        total: customerResults.length,
        message: 'Customers retrieved successfully'
      };
    } catch (error) {
      console.error('Error getting customers by store:', error);
      return {    
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get customers by store'
      };
    }
  }
  
  /**
   * Get customer's pets
   */
  private async getCustomerPets(oneNataUserId: string): Promise<ServiceResponse<Pet[]>> {
    try {

      const petsResult = await FirestoreService.getMany<FirestoreDocument>(
        this.ONENATA_PET_COLLECTION,
        {
          where: [{ field: 'ownerId', operator: '==', value: oneNataUserId }],
          orderBy: [{ field: 'name', direction: 'asc' }]
        }
      );


      if (petsResult.success && petsResult.data) {
        return {
          success: true,
          data: petsResult.data as unknown as Pet[],
          message: 'Customer pets retrieved'
        };
      }

      return {
        success: false,
        error: 'No pets found'
      };

    } catch (error) {
      console.error('Error getting customer pets:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get customer pets'
      };
    }
  }

  /**
   * Link customer to their pets
   */
  private async linkCustomerPets(customerId: string, oneNataUserId: string): Promise<void> {
    try {
      const petsResult = await this.getCustomerPets(oneNataUserId);
      if (petsResult.success && petsResult.data) {
        const petIds = petsResult.data.map(pet => pet.sid || '').filter(id => id);
        
        if (petIds.length > 0) {
          await FirestoreService.update(
            this.STORE_CUSTOMER_COLLECTION,
            customerId,
            {
              petIds,
              update_date: new Date()
            }
          );
        }
      }
    } catch (error) {
      console.error('Error linking customer pets:', error);
      // Don't throw error, this is non-critical
    }
  }
}

// Export singleton instance
export const customerService = new CustomerService();
export default customerService; 