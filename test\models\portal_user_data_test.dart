import 'package:flutter_test/flutter_test.dart';
import 'package:onenata_app/models/models_i.dart';

void main() {
  group('PortalUserData Model Tests', () {
    late PortalUserData testUserData;
    late UserPreferences testPreferences;

    setUp(() {
      testPreferences = UserPreferences(
        language: 'zh-CN',
        notificationsEnabled: true,
      );

      testUserData = PortalUserData(
        uid: 'test_account_id',
        firstName: 'John',
        lastName: 'Doe',
        displayName: '<PERSON>',
        bio: 'Test bio',
        phoneNumber: '+****************',
        photoURL: 'https://example.com/photo.jpg',
        invitationCode: 'STORESTAFF-test-store-id',
        userType: '202',
        preferences: testPreferences,
        sid: 'test_user_data_id',
        isValid: true,
        isSynced: true,
      );
    });

    test('should create PortalUserData with required fields', () {
      expect(testUserData.uid, equals('test_account_id'));
      expect(testUserData.firstName, equals('John'));
      expect(testUserData.lastName, equals('Doe'));
      expect(testUserData.displayName, equals('<PERSON>'));
      expect(testUserData.userType, equals('202'));
    });

    test('should create with default preferences when not provided', () {
      final userData = PortalUserData(
        uid: 'test_uid',
        firstName: 'Jane',
        lastName: 'Smith',
        displayName: 'Jane Smith',
        phoneNumber: '+****************',
      );

      expect(userData.preferences.language, equals('en-US'));
      expect(userData.preferences.notificationsEnabled, isTrue);
    });

    test('should get full name correctly', () {
      expect(testUserData.getFullName(), equals('John Doe'));

      // Test with empty names
      testUserData.firstName = '';
      testUserData.lastName = '';
      expect(testUserData.getFullName(), equals(''));

      // Test with only first name
      testUserData.firstName = 'John';
      testUserData.lastName = '';
      expect(testUserData.getFullName(), equals('John'));

      // Test with only last name
      testUserData.firstName = '';
      testUserData.lastName = 'Doe';
      expect(testUserData.getFullName(), equals('Doe'));
    });

    test('should check if user is staff correctly', () {
      expect(testUserData.isStaff(), isTrue);

      testUserData.userType = '101';
      expect(testUserData.isStaff(), isFalse);

      testUserData.userType = '202';
      expect(testUserData.isStaff(), isTrue);
    });

    test('should check if user has avatar correctly', () {
      expect(testUserData.hasAvatar(), isTrue);

      testUserData.photoURL = null;
      expect(testUserData.hasAvatar(), isFalse);

      testUserData.photoURL = '';
      expect(testUserData.hasAvatar(), isFalse);

      testUserData.photoURL = 'https://example.com/photo.jpg';
      expect(testUserData.hasAvatar(), isTrue);
    });

    test('should get display name correctly', () {
      expect(testUserData.getDisplayName(), equals('John Doe'));

      // When displayName is empty, should return full name
      testUserData.displayName = '';
      expect(testUserData.getDisplayName(), equals('John Doe'));

      // When displayName is provided, should return displayName
      testUserData.displayName = 'Johnny';
      expect(testUserData.getDisplayName(), equals('Johnny'));
    });

    test('should check profile completion correctly', () {
      expect(testUserData.isProfileComplete(), isTrue);

      // Missing first name
      testUserData.firstName = '';
      expect(testUserData.isProfileComplete(), isFalse);

      testUserData.firstName = 'John';
      testUserData.lastName = '';
      expect(testUserData.isProfileComplete(), isFalse);

      testUserData.lastName = 'Doe';
      testUserData.phoneNumber = '';
      expect(testUserData.isProfileComplete(), isFalse);
    });

    test('should convert to/from JSON correctly', () {
      final json = testUserData.toJson();
      final fromJson = PortalUserData.fromJson(json);

      expect(fromJson.uid, equals(testUserData.uid));
      expect(fromJson.firstName, equals(testUserData.firstName));
      expect(fromJson.lastName, equals(testUserData.lastName));
      expect(fromJson.displayName, equals(testUserData.displayName));
      expect(fromJson.userType, equals(testUserData.userType));
      expect(fromJson.preferences.language, equals(testUserData.preferences.language));
      expect(fromJson.preferences.notificationsEnabled, equals(testUserData.preferences.notificationsEnabled));
    });

    test('should convert to Firestore data correctly', () {
      final firestoreData = testUserData.toFirestoreData();

      expect(firestoreData['uid'], equals('test_account_id'));
      expect(firestoreData['first_name'], equals('John'));
      expect(firestoreData['last_name'], equals('Doe'));
      expect(firestoreData['display_name'], equals('John Doe'));
      expect(firestoreData['user_type'], equals('202'));
      expect(firestoreData['phone_number'], equals('+****************'));
      expect(firestoreData['photo_url'], equals('https://example.com/photo.jpg'));
      expect(firestoreData['invitation_code'], equals('STORESTAFF-test-store-id'));
    });

    test('should have correct collection name', () {
      expect(PortalUserData.collection, equals('portal-user-data'));
    });

    test('should handle default values correctly', () {
      final defaultUserData = PortalUserData(
        uid: 'test_uid',
        firstName: 'Jane',
        lastName: 'Smith',
        displayName: 'Jane Smith',
        phoneNumber: '+****************',
      );

      expect(defaultUserData.userType, equals('202'));
      expect(defaultUserData.bio, isNull);
      expect(defaultUserData.photoURL, isNull);
      expect(defaultUserData.invitationCode, isNull);
      expect(defaultUserData.isValid, isTrue);
      expect(defaultUserData.isSynced, isTrue);
    });
  });

  group('UserPreferences Model Tests', () {
    test('should create UserPreferences with default values', () {
      final preferences = UserPreferences();

      expect(preferences.language, equals('en-US'));
      expect(preferences.notificationsEnabled, isTrue);
    });

    test('should create UserPreferences with custom values', () {
      final preferences = UserPreferences(
        language: 'zh-CN',
        notificationsEnabled: false,
      );

      expect(preferences.language, equals('zh-CN'));
      expect(preferences.notificationsEnabled, isFalse);
    });

    test('should convert to/from JSON correctly', () {
      final preferences = UserPreferences(
        language: 'zh-CN',
        notificationsEnabled: false,
      );

      final json = preferences.toJson();
      final fromJson = UserPreferences.fromJson(json);

      expect(fromJson.language, equals(preferences.language));
      expect(fromJson.notificationsEnabled, equals(preferences.notificationsEnabled));
    });
  });
}
