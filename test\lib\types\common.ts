/**
 * 认证相关的类型定义
 */

import { AuthChannel } from '../models/types';

// 注册请求数据
export interface RegisterRequest {
  email?: string;
  phoneNumber?: string;
  password: string;
  firstName: string;
  lastName: string;
  displayName?: string;
  invitationCode?: string;
  authChannel: AuthChannel;
  userType?: import('../models/types').UserType;
  bio?: string;
}

// 登录请求数据
export interface LoginRequest {
  email?: string;
  phoneNumber?: string;
  password: string;
  authChannel: AuthChannel;
}

// 忘记密码请求数据
export interface ForgotPasswordRequest {
  email?: string;
  phoneNumber?: string;
  authChannel: AuthChannel;
}

// 重置密码请求数据
export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

// 验证码请求数据
export interface VerificationRequest {
  email?: string;
  phoneNumber?: string;
  code: string;
  type: 'email' | 'phone' | 'password_reset';
}

// 重新发送验证码请求数据
export interface ResendVerificationRequest {
  email?: string;
  phoneNumber?: string;
  type: 'email' | 'phone';
}

// 更改密码请求数据
export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// 认证响应数据
export interface AuthResponse {
  success: boolean;
  message?: string;
  data?: {
    uid?: string;
    sid?: string;
    fid?: string;
    token?: string;
    user?: Record<string, unknown>;
    requiresVerification?: boolean;
    verificationType?: 'email' | 'phone';
  };
  user?: {
    uid: string;
    email: string | null;
    displayName: string | null;
    emailVerified: boolean;
    phoneNumber: string | null;
    photoURL: string | null;
    isAnonymous: boolean;
    metadata: {
      creationTime?: string;
      lastSignInTime?: string;
    };
  };
  token?: string;
  error?: string;
  errorType?: AuthErrorType;
}

// 验证码类型
export enum VerificationType {
  EMAIL_VERIFICATION = 'email_verification',
  PHONE_VERIFICATION = 'phone_verification',
  PASSWORD_RESET = 'password_reset',
  TWO_FACTOR = 'two_factor'
}

// 验证码状态
export enum VerificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  VERIFIED = 'verified',
  EXPIRED = 'expired',
  FAILED = 'failed'
}

// 验证码记录
export interface VerificationRecord {
  id: string;
  email?: string;
  phoneNumber?: string;
  code: string;
  type: VerificationType;
  status: VerificationStatus;
  expiresAt: Date;
  createdAt: Date;
  attempts: number;
  maxAttempts: number;
}

// 密码强度要求
export interface PasswordRequirements {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
}

// 认证配置
export interface AuthConfig {
  passwordRequirements: PasswordRequirements;
  verificationCodeLength: number;
  verificationCodeExpiry: number; // 分钟
  maxVerificationAttempts: number;
  lockoutDuration: number; // 分钟
  sessionTimeout: number; // 分钟
}

// 用户会话
export interface UserSession {
  uid: string;
  sid: string;
  fid: string;
  email?: string;
  phoneNumber?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  createdAt: Date;
  expiresAt: Date;
  lastActivity: Date;
}

// 认证错误类型
export enum AuthErrorType {
  INVALID_CREDENTIALS = 'invalid_credentials',
  USER_NOT_FOUND = 'user_not_found',
  USER_ALREADY_EXISTS = 'user_already_exists',
  WEAK_PASSWORD = 'weak_password',
  UNAUTHORIZED = 'unauthorized',
  REQUIRES_RECENT_LOGIN = 'requires_recent_login',
  VERIFICATION_REQUIRED = 'verification_required',
  VERIFICATION_FAILED = 'verification_failed',
  VERIFICATION_EXPIRED = 'verification_expired',
  TOO_MANY_ATTEMPTS = 'too_many_attempts',
  ACCOUNT_LOCKED = 'account_locked',
  INVALID_TOKEN = 'invalid_token',
  TOKEN_EXPIRED = 'token_expired',
  INVALID_INVITATION_CODE = 'invalid_invitation_code',
  NETWORK_ERROR = 'network_error',
  UNKNOWN_ERROR = 'unknown_error'
}

// 认证错误类
export class AuthError extends Error {
  constructor(
    public type: AuthErrorType,
    public message: string,
    public details?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'AuthError';
  }
} 

// 进度回调接口
export interface ProgressCallback {
  (step: number, total: number, message: string): void;
}