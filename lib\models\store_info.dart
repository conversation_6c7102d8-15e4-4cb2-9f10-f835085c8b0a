import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'base_full_model.dart';

part 'store_info.g.dart';

/// Store Info - 店铺信息
/// 存储在 Firestore collection "store-info"
@JsonSerializable()
class StoreInfo extends BaseFullModel {
  String storeId;                    // Store account sid
  Address currentAddress;            // 当前地址
  String phone;                      // 电话号码
  String email;                      // 邮箱
  BusinessType businessType;         // 业务类型
  String? description;               // 描述
  Services services;                 // 提供的服务类型
  BusinessHours? businessHours;      // 营业时间
  String? businessTimeZone;          // 业务时区
  DateTime? createdAt;               // 创建时间
  DateTime? updatedAt;               // 更新时间
  List<String>? staffs;              // Portal user sids
  bool? hasSpecialEvent;             // 是否有特殊活动
  List<String>? customerList;        // OneNata user sids
  bool? appointmentOpen;             // 是否开放预约
  String? avatarUrl;                 // 头像URL
  List<String>? storePhotos;         // 店铺照片 Firebase storage paths
  String? storeSubscriptionPlan;     // 店铺订阅计划
  String? website;                   // 店铺网站URL

  StoreInfo({
    required this.storeId,
    required this.currentAddress,
    required this.phone,
    required this.email,
    required this.businessType,
    this.description,
    required this.services,
    this.businessHours,
    this.businessTimeZone,
    this.createdAt,
    this.updatedAt,
    this.staffs,
    this.hasSpecialEvent,
    this.customerList,
    this.appointmentOpen,
    this.avatarUrl,
    this.storePhotos,
    this.storeSubscriptionPlan,
    this.website,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  /// 从 Firestore 文档创建 StoreInfo 对象
  factory StoreInfo.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

    // 直接使用数据，不进行字段名转换（因为数据库已经是camelCase）
    final Map<String, dynamic> jsonData = Map<String, dynamic>.from(data);

    // 处理Timestamp字段转换为DateTime
    if (jsonData['createdAt'] is Timestamp) {
      jsonData['createdAt'] = (jsonData['createdAt'] as Timestamp).toDate().toIso8601String();
    }
    if (jsonData['updatedAt'] is Timestamp) {
      jsonData['updatedAt'] = (jsonData['updatedAt'] as Timestamp).toDate().toIso8601String();
    }

    // 处理其他可能的Timestamp字段
    if (jsonData['createDate'] is Timestamp) {
      jsonData['createDate'] = (jsonData['createDate'] as Timestamp).millisecondsSinceEpoch;
    }
    if (jsonData['updateDate'] is Timestamp) {
      jsonData['updateDate'] = (jsonData['updateDate'] as Timestamp).millisecondsSinceEpoch;
    }
    if (jsonData['reviewDate'] is Timestamp) {
      jsonData['reviewDate'] = (jsonData['reviewDate'] as Timestamp).millisecondsSinceEpoch;
    }

    // 设置文档ID
    jsonData['sid'] = doc.id;

    return StoreInfo.fromJson(jsonData);
  }

  factory StoreInfo.fromJson(Map<String, dynamic> json) =>
      _$StoreInfoFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$StoreInfoToJson(this);

  Map<String, dynamic> toFirestoreData() {
    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });
    return dbData;
  }

  static String get collection => 'store-info';

  /// 检查店铺是否开放预约
  bool get isAppointmentEnabled => appointmentOpen ?? false;

  /// 检查店铺是否提供指定服务
  bool providesService(ServiceCategory category) {
    switch (category) {
      case ServiceCategory.grooming:
        return services.grooming;
      case ServiceCategory.boarding:
        return services.boarding;
      case ServiceCategory.veterinary:
        return services.veterinary;
      case ServiceCategory.training:
        return services.training;
      case ServiceCategory.daycare:
        return services.boarding; // daycare使用boarding服务
      case ServiceCategory.other:
        return true; // 其他服务默认支持
    }
  }

  /// 获取店铺的完整地址字符串
  String get fullAddress {
    final parts = <String>[];
    parts.add(currentAddress.addressLine1);
    if (currentAddress.addressLine2?.isNotEmpty == true) {
      parts.add(currentAddress.addressLine2!);
    }
    parts.add(currentAddress.city);
    parts.add(currentAddress.province);
    parts.add(currentAddress.country);
    parts.add(currentAddress.postCode);
    return parts.join(', ');
  }

  /// 检查今天是否营业
  bool get isOpenToday {
    if (businessHours == null) return false;
    
    final today = DateTime.now();
    final dayName = _getDayName(today.weekday);
    final todayHours = businessHours![dayName];
    
    return todayHours != null && todayHours.isOpen;
  }

  /// 获取今天的营业时间
  BusinessHour? get todayHours {
    if (businessHours == null) return null;
    
    final today = DateTime.now();
    final dayName = _getDayName(today.weekday);
    return businessHours![dayName];
  }

  /// 将星期数字转换为字符串
  String _getDayName(int weekday) {
    switch (weekday) {
      case DateTime.monday:
        return 'monday';
      case DateTime.tuesday:
        return 'tuesday';
      case DateTime.wednesday:
        return 'wednesday';
      case DateTime.thursday:
        return 'thursday';
      case DateTime.friday:
        return 'friday';
      case DateTime.saturday:
        return 'saturday';
      case DateTime.sunday:
        return 'sunday';
      default:
        return 'monday';
    }
  }
}

/// 地址模型
@JsonSerializable()
class Address {
  String addressLine1;
  String? addressLine2;
  String city;
  String province;
  String country;
  String postCode;

  Address({
    required this.addressLine1,
    this.addressLine2,
    required this.city,
    required this.province,
    required this.country,
    required this.postCode,
  });

  factory Address.fromJson(Map<String, dynamic> json) =>
      _$AddressFromJson(json);

  Map<String, dynamic> toJson() => _$AddressToJson(this);
}

/// 服务类型模型
@JsonSerializable()
class Services {
  bool grooming;
  bool boarding;
  bool veterinary;
  bool training;
  bool retail;

  Services({
    required this.grooming,
    required this.boarding,
    required this.veterinary,
    required this.training,
    required this.retail,
  });

  factory Services.fromJson(Map<String, dynamic> json) =>
      _$ServicesFromJson(json);

  Map<String, dynamic> toJson() => _$ServicesToJson(this);
}

/// 营业时间模型
typedef BusinessHours = Map<String, BusinessHour>;

/// 单日营业时间
@JsonSerializable()
class BusinessHour {
  String? open;    // 开门时间 "09:00" (关门时可为null)
  String? close;   // 关门时间 "18:00" (关门时可为null)
  bool? closed;    // 是否关门 (可为null，默认根据open/close判断)

  BusinessHour({
    this.open,
    this.close,
    this.closed,
  });

  factory BusinessHour.fromJson(Map<String, dynamic> json) =>
      _$BusinessHourFromJson(json);

  Map<String, dynamic> toJson() => _$BusinessHourToJson(this);

  /// 检查指定时间是否在营业时间内
  bool isWithinHours(String time) {
    // 如果明确标记为关门，或者没有开门/关门时间，则不在营业时间内
    if (closed == true || open == null || close == null) return false;

    final timeMinutes = _timeToMinutes(time);
    final openMinutes = _timeToMinutes(open!);
    final closeMinutes = _timeToMinutes(close!);

    return timeMinutes >= openMinutes && timeMinutes <= closeMinutes;
  }

  /// 是否营业中（考虑默认逻辑）
  bool get isOpen {
    // 如果明确标记为关门，则不营业
    if (closed == true) return false;
    // 如果有开门时间但没有明确标记关门，则认为营业
    if (open != null && closed != true) return true;
    // 其他情况认为不营业
    return false;
  }

  /// 将时间字符串转换为分钟数
  int _timeToMinutes(String time) {
    final parts = time.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }
}
