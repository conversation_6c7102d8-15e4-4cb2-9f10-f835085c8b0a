// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'store_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StoreInfo _$StoreInfoFromJson(Map<String, dynamic> json) => StoreInfo(
      storeId: json['storeId'] as String,
      currentAddress:
          Address.fromJson(json['currentAddress'] as Map<String, dynamic>),
      phone: json['phone'] as String,
      email: json['email'] as String,
      businessType: $enumDecode(_$BusinessTypeEnumMap, json['businessType']),
      description: json['description'] as String?,
      services: Services.fromJson(json['services'] as Map<String, dynamic>),
      businessHours: (json['businessHours'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, BusinessHour.fromJson(e as Map<String, dynamic>)),
      ),
      businessTimeZone: json['businessTimeZone'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      staffs:
          (json['staffs'] as List<dynamic>?)?.map((e) => e as String).toList(),
      hasSpecialEvent: json['hasSpecialEvent'] as bool?,
      customerList: (json['customerList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      appointmentOpen: json['appointmentOpen'] as bool?,
      avatarUrl: json['avatarUrl'] as String?,
      storePhotos: (json['storePhotos'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      storeSubscriptionPlan: json['storeSubscriptionPlan'] as String?,
      website: json['website'] as String?,
      id: (json['id'] as num?)?.toInt(),
      sid: json['sid'] as String?,
      name: json['name'] as String?,
      isValid: JsonUtil.boolFromJson(json['isValid']),
      isSynced: JsonUtil.boolFromJson(json['isSynced']),
      createdBy: json['createdBy'] as String?,
      createDate: (json['createDate'] as num?)?.toInt(),
      updatedBy: json['updatedBy'] as String?,
      updateDate: (json['updateDate'] as num?)?.toInt(),
      reviewedBy: json['reviewedBy'] as String?,
      reviewDate: (json['reviewDate'] as num?)?.toInt(),
      approveStatus: JsonUtil.boolFromJson(json['approveStatus']),
      notes: json['notes'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$StoreInfoToJson(StoreInfo instance) => <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'createdBy': instance.createdBy,
      'createDate': instance.createDate,
      'updatedBy': instance.updatedBy,
      'updateDate': instance.updateDate,
      'reviewedBy': instance.reviewedBy,
      'reviewDate': instance.reviewDate,
      'approveStatus': JsonUtil.boolToJson(instance.approveStatus),
      'notes': instance.notes,
      'tags': instance.tags,
      'storeId': instance.storeId,
      'currentAddress': instance.currentAddress,
      'phone': instance.phone,
      'email': instance.email,
      'businessType': _$BusinessTypeEnumMap[instance.businessType]!,
      'description': instance.description,
      'services': instance.services,
      'businessHours': instance.businessHours,
      'businessTimeZone': instance.businessTimeZone,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'staffs': instance.staffs,
      'hasSpecialEvent': instance.hasSpecialEvent,
      'customerList': instance.customerList,
      'appointmentOpen': instance.appointmentOpen,
      'avatarUrl': instance.avatarUrl,
      'storePhotos': instance.storePhotos,
      'storeSubscriptionPlan': instance.storeSubscriptionPlan,
      'website': instance.website,
    };

const _$BusinessTypeEnumMap = {
  BusinessType.onlineStore: 'online_store',
  BusinessType.familyBasedBusiness: 'family_based_business',
  BusinessType.retailBusiness: 'retail_business',
  BusinessType.commercialBusiness: 'commercial_business',
  BusinessType.petStore: 'pet_store',
  BusinessType.veterinaryClinic: 'veterinary_clinic',
  BusinessType.petGrooming: 'pet_grooming',
  BusinessType.petHotel: 'pet_hotel',
  BusinessType.petTraining: 'pet_training',
  BusinessType.comprehensiveService: 'comprehensive_service',
  BusinessType.franchise: 'franchise',
  BusinessType.mobileService: 'mobile_service',
  BusinessType.other: 'other',
};

Address _$AddressFromJson(Map<String, dynamic> json) => Address(
      addressLine1: json['addressLine1'] as String,
      addressLine2: json['addressLine2'] as String?,
      city: json['city'] as String,
      province: json['province'] as String,
      country: json['country'] as String,
      postCode: json['postCode'] as String,
    );

Map<String, dynamic> _$AddressToJson(Address instance) => <String, dynamic>{
      'addressLine1': instance.addressLine1,
      'addressLine2': instance.addressLine2,
      'city': instance.city,
      'province': instance.province,
      'country': instance.country,
      'postCode': instance.postCode,
    };

Services _$ServicesFromJson(Map<String, dynamic> json) => Services(
      grooming: json['grooming'] as bool,
      boarding: json['boarding'] as bool,
      veterinary: json['veterinary'] as bool,
      training: json['training'] as bool,
      retail: json['retail'] as bool,
    );

Map<String, dynamic> _$ServicesToJson(Services instance) => <String, dynamic>{
      'grooming': instance.grooming,
      'boarding': instance.boarding,
      'veterinary': instance.veterinary,
      'training': instance.training,
      'retail': instance.retail,
    };

BusinessHour _$BusinessHourFromJson(Map<String, dynamic> json) => BusinessHour(
      open: json['open'] as String?,
      close: json['close'] as String?,
      closed: json['closed'] as bool?,
    );

Map<String, dynamic> _$BusinessHourToJson(BusinessHour instance) =>
    <String, dynamic>{
      'open': instance.open,
      'close': instance.close,
      'closed': instance.closed,
    };
