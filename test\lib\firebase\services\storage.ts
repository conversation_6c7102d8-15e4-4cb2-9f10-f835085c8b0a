import { storage } from '../config';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { v4 as uuidv4 } from 'uuid';

export interface UploadResult {
  url: string;
  path: string;
  fileName: string;
}

export interface UploadProgress {
  progress: number;
  isUploading: boolean;
  error?: string;
}

/**
 * 上传用户头像到Firebase Storage
 */
export const uploadUserAvatar = async (
  file: File,
  userId: string,
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> => {
  try {
    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      throw new Error('只支持图片文件');
    }

    // 验证文件大小 (5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      throw new Error('文件大小不能超过5MB');
    }

    // 生成唯一文件名
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const fileName = `${uuidv4()}.${fileExtension}`;
    const filePath = `avatars/${userId}/${fileName}`;

    // 创建存储引用
    const storageRef = ref(storage, filePath);

    // 更新上传进度
    onProgress?.({ progress: 0, isUploading: true });

    // 上传文件
    const snapshot = await uploadBytes(storageRef, file);

    // 更新上传进度
    onProgress?.({ progress: 50, isUploading: true });

    // 获取下载URL
    const downloadURL = await getDownloadURL(snapshot.ref);

    // 完成上传
    onProgress?.({ progress: 100, isUploading: false });

    return {
      url: downloadURL,
      path: filePath,
      fileName: fileName
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '上传失败';
    onProgress?.({ progress: 0, isUploading: false, error: errorMessage });
    throw new Error(errorMessage);
  }
};

/**
 * 上传临时店铺头像（创建店铺时使用）
 */
export const uploadTempStoreAvatar = async (
  file: File,
  userId: string,
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> => {
  try {
    // 验证文件
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // 生成唯一文件名
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const fileName = `avatar_${Date.now()}.${fileExtension}`;
    const filePath = `store_photos/stores/${userId}/avatar/${fileName}`;

    // 创建存储引用
    const storageRef = ref(storage, filePath);

    // 更新上传进度
    onProgress?.({ progress: 0, isUploading: true });

    // 上传文件
    const snapshot = await uploadBytes(storageRef, file);

    // 更新上传进度
    onProgress?.({ progress: 50, isUploading: true });

    // 获取下载URL
    const downloadURL = await getDownloadURL(snapshot.ref);

    // 完成上传
    onProgress?.({ progress: 100, isUploading: false });

    return {
      url: downloadURL,
      path: filePath,
      fileName: fileName
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '上传失败';
    onProgress?.({ progress: 0, isUploading: false, error: errorMessage });
    throw new Error(errorMessage);
  }
};

/**
 * 上传店铺头像
 */
export const uploadStoreAvatar = async (
  file: File,
  placeId: string,
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> => {
  try {
    // 验证文件
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // 生成唯一文件名
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const fileName = `avatar.${fileExtension}`;
    const filePath = `stores/${placeId}/avatar/${fileName}`;

    // 创建存储引用
    const storageRef = ref(storage, filePath);

    // 更新上传进度
    onProgress?.({ progress: 0, isUploading: true });

    // 上传文件
    const snapshot = await uploadBytes(storageRef, file);

    // 更新上传进度
    onProgress?.({ progress: 50, isUploading: true });

    // 获取下载URL
    const downloadURL = await getDownloadURL(snapshot.ref);

    // 完成上传
    onProgress?.({ progress: 100, isUploading: false });

    return {
      url: downloadURL,
      path: filePath,
      fileName: fileName
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '上传失败';
    onProgress?.({ progress: 0, isUploading: false, error: errorMessage });
    throw new Error(errorMessage);
  }
};

/**
 * 上传临时店铺照片（创建店铺时使用）
 */
export const uploadTempStorePhoto = async (
  file: File,
  userId: string,
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> => {
  try {
    // 验证文件
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // 生成唯一文件名
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const fileName = `photo_${Date.now()}_${uuidv4()}.${fileExtension}`;
    const filePath = `temp/stores/${userId}/photos/${fileName}`;

    // 创建存储引用
    const storageRef = ref(storage, filePath);

    // 更新上传进度
    onProgress?.({ progress: 0, isUploading: true });

    // 上传文件
    const snapshot = await uploadBytes(storageRef, file);

    // 更新上传进度
    onProgress?.({ progress: 50, isUploading: true });

    // 获取下载URL
    const downloadURL = await getDownloadURL(snapshot.ref);

    // 完成上传
    onProgress?.({ progress: 100, isUploading: false });

    return {
      url: downloadURL,
      path: filePath,
      fileName: fileName
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '上传失败';
    onProgress?.({ progress: 0, isUploading: false, error: errorMessage });
    throw new Error(errorMessage);
  }
};

/**
 * 上传店铺照片
 */
export const uploadStorePhoto = async (
  file: File,
  storeId: string,
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> => {
  try {
    // 验证文件
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // 生成唯一文件名
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const fileName = `${uuidv4()}.${fileExtension}`;
    const filePath = `stores/${storeId}/photos/${fileName}`;

    // 创建存储引用
    const storageRef = ref(storage, filePath);

    // 更新上传进度
    onProgress?.({ progress: 0, isUploading: true });

    // 上传文件
    const snapshot = await uploadBytes(storageRef, file);

    // 更新上传进度
    onProgress?.({ progress: 50, isUploading: true });

    // 获取下载URL
    const downloadURL = await getDownloadURL(snapshot.ref);

    // 完成上传
    onProgress?.({ progress: 100, isUploading: false });

    return {
      url: downloadURL,
      path: filePath,
      fileName: fileName
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '上传失败';
    onProgress?.({ progress: 0, isUploading: false, error: errorMessage });
    throw new Error(errorMessage);
  }
};

/**
 * 删除用户头像
 */
export const deleteUserAvatar = async (filePath: string): Promise<void> => {
  try {
    const storageRef = ref(storage, filePath);
    await deleteObject(storageRef);
  } catch (error) {
    console.error('删除头像失败:', error);
    throw new Error('删除头像失败');
  }
};

/**
 * 上传通用文件
 */
export const uploadFile = async (
  file: File,
  path: string,
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> => {
  try {
    // 生成唯一文件名
    const fileExtension = file.name.split('.').pop() || 'file';
    const fileName = `${uuidv4()}.${fileExtension}`;
    const filePath = `${path}/${fileName}`;

    // 创建存储引用
    const storageRef = ref(storage, filePath);

    // 更新上传进度
    onProgress?.({ progress: 0, isUploading: true });

    // 上传文件
    const snapshot = await uploadBytes(storageRef, file);

    // 更新上传进度
    onProgress?.({ progress: 50, isUploading: true });

    // 获取下载URL
    const downloadURL = await getDownloadURL(snapshot.ref);

    // 完成上传
    onProgress?.({ progress: 100, isUploading: false });

    return {
      url: downloadURL,
      path: filePath,
      fileName: fileName
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '上传失败';
    onProgress?.({ progress: 0, isUploading: false, error: errorMessage });
    throw new Error(errorMessage);
  }
};

/**
 * 压缩图片
 */
export const compressImage = (file: File, maxWidth: number = 800, quality: number = 0.8): Promise<File> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 计算新的尺寸
      const { width, height } = img;
      const ratio = Math.min(maxWidth / width, maxWidth / height);
      const newWidth = width * ratio;
      const newHeight = height * ratio;

      // 设置画布尺寸
      canvas.width = newWidth;
      canvas.height = newHeight;

      // 绘制压缩后的图片
      ctx?.drawImage(img, 0, 0, newWidth, newHeight);

      // 转换为Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(compressedFile);
          } else {
            reject(new Error('图片压缩失败'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error('图片加载失败'));
    img.src = URL.createObjectURL(file);
  });
};

/**
 * 移动临时文件到正式位置
 */
export const moveTempFilesToStore = async (
  tempUrls: string[],
  storeId: string,
  type: 'avatar' | 'photos'
): Promise<string[]> => {
  const movedUrls: string[] = [];
  
  for (const tempUrl of tempUrls) {
    try {
      // 从URL中提取文件路径
      const url = new URL(tempUrl);
      const pathParts = url.pathname.split('/');
      const fileName = pathParts[pathParts.length - 1];
      
      // 构建临时文件路径
      const tempPath = decodeURIComponent(url.pathname.substring(1)); // 移除开头的 '/'
      
      // 构建目标路径
      const targetPath = type === 'avatar' 
        ? `stores/${storeId}/avatar/${fileName}`
        : `stores/${storeId}/photos/${fileName}`;
      
      // 创建存储引用
      const tempRef = ref(storage, tempPath);
      const targetRef = ref(storage, targetPath);
      
      // 下载临时文件
      const downloadUrl = await getDownloadURL(tempRef);
      const response = await fetch(downloadUrl);
      const blob = await response.blob();
      
      // 上传到目标位置
      await uploadBytes(targetRef, blob);
      const newUrl = await getDownloadURL(targetRef);
      
      // 删除临时文件
      await deleteObject(tempRef);
      
      movedUrls.push(newUrl);
    } catch (error) {
      console.error('移动文件失败:', error);
      // 如果移动失败，保留原URL
      movedUrls.push(tempUrl);
    }
  }
  
  return movedUrls;
};

/**
 * 清理临时文件
 */
export const cleanupTempFiles = async (userId: string): Promise<void> => {
  try {
    const tempPath = `temp/stores/${userId}`;
    
    // 这里可以添加删除整个临时目录的逻辑
    // 由于Firebase Storage没有直接删除目录的API，需要列出所有文件后逐个删除
    console.log('清理临时文件:', tempPath);
  } catch (error) {
    console.error('清理临时文件失败:', error);
  }
};

/**
 * 验证图片文件
 */
export const validateImageFile = (file: File): { isValid: boolean; error?: string } => {
  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    return { isValid: false, error: '只支持图片文件' };
  }

  // 检查文件大小 (5MB)
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    return { isValid: false, error: '文件大小不能超过5MB' };
  }

  // 检查支持的格式
  const supportedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
  if (!supportedTypes.includes(file.type)) {
    return { isValid: false, error: '只支持 JPEG、PNG、WebP、GIF 格式' };
  }

  return { isValid: true };
}; 

/**
 * 从Google Places照片URL下载并上传到Firebase Storage
 */
export const downloadAndUploadGooglePhoto = async (
  photoUrl: string,
  placeId: string,
  photoReference: string,
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> => {
  try {
    onProgress?.({ progress: 0, isUploading: true });

    // 下载Google照片
    const response = await fetch(photoUrl);
    if (!response.ok) {
      throw new Error(`下载失败: ${response.statusText}`);
    }

    onProgress?.({ progress: 30, isUploading: true });

    const blob = await response.blob();
    
    // 检查是否为图片类型
    if (!blob.type.startsWith('image/')) {
      throw new Error('下载的文件不是图片格式');
    }

    onProgress?.({ progress: 50, isUploading: true });

    // 生成文件名
    const fileExtension = blob.type.split('/')[1] || 'jpg';
    const fileName = `google_${photoReference.substring(0, 10)}_${Date.now()}.${fileExtension}`;
    const filePath = `stores/${placeId}/photos/${fileName}`;

    // 创建存储引用
    const storageRef = ref(storage, filePath);

    onProgress?.({ progress: 70, isUploading: true });

    // 上传到Firebase Storage
    const snapshot = await uploadBytes(storageRef, blob);

    onProgress?.({ progress: 90, isUploading: true });

    // 获取下载URL
    const downloadURL = await getDownloadURL(snapshot.ref);

    onProgress?.({ progress: 100, isUploading: false });

    return {
      url: downloadURL,
      path: filePath,
      fileName: fileName
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '下载和上传失败';
    onProgress?.({ progress: 0, isUploading: false, error: errorMessage });
    throw new Error(errorMessage);
  }
};

/**
 * 批量下载并上传Google Places照片
 */
export const downloadAndUploadGooglePhotos = async (
  photos: Array<{
    photoUrl: string;
    photo_reference: string;
  }>,
  placeId: string,
  onProgress?: (completed: number, total: number, currentProgress: UploadProgress) => void
): Promise<UploadResult[]> => {
  const results: UploadResult[] = [];
  const total = photos.length;

  for (let i = 0; i < photos.length; i++) {
    const photo = photos[i];
    try {
      const result = await downloadAndUploadGooglePhoto(
        photo.photoUrl,
        placeId,
        photo.photo_reference,
        (progress) => {
          onProgress?.(i, total, progress);
        }
      );
      results.push(result);
    } catch (error) {
      console.error(`上传第${i + 1}张照片失败:`, error);
      // 继续处理下一张照片，不中断整个流程
    }
  }

  return results;
}; 