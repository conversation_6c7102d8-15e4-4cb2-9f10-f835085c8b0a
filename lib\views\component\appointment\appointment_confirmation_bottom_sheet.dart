import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:onenata_app/models/pet.dart';
import 'package:onenata_app/models/bookable_service.dart';
import 'package:onenata_app/services/appointment_service.dart';
import 'package:onenata_app/services/auth_service.dart';
import 'package:onenata_app/views/component/appointment/datetime_selection_bottom_sheet.dart';

class AppointmentConfirmationBottomSheet extends StatefulWidget {
  final Pet selectedPet;
  final BookableService selectedService;
  final DateTime selectedDate;
  final TimeSlot selectedTimeSlot;
  final Staff selectedStaff;
  final String notes;
  final VoidCallback? onBackToMain;

  const AppointmentConfirmationBottomSheet({
    Key? key,
    required this.selectedPet,
    required this.selectedService,
    required this.selectedDate,
    required this.selectedTimeSlot,
    required this.selectedStaff,
    required this.notes,
    this.onBackToMain,
  }) : super(key: key);

  @override
  State<AppointmentConfirmationBottomSheet> createState() => _AppointmentConfirmationBottomSheetState();
}

class _AppointmentConfirmationBottomSheetState extends State<AppointmentConfirmationBottomSheet> {
  final AppointmentService _appointmentService = AppointmentService();
  final AuthService _authService = AuthService.instance;
  bool _isLoading = false;
  bool _appointmentSaved = false;

  @override
  void initState() {
    super.initState();
    // 页面显示时自动保存预约
    _saveAppointment();
  }

  /// 保存预约到数据库
  Future<void> _saveAppointment() async {
    if (_appointmentSaved) return; // 防止重复保存

    setState(() {
      _isLoading = true;
    });

    try {
      // 获取当前用户信息
      final currentUser = _authService.currentUser.value;
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      // 获取店铺ID（从服务中获取）
      final storeId = widget.selectedService.storeId;
      if (storeId.isEmpty) {
        throw Exception('无效的店铺信息');
      }

      // 计算结束时间
      final startTime = widget.selectedTimeSlot.time;
      // 从员工服务中获取实际的服务时长
      final duration = await _getServiceDurationFromEmployeeService(
        widget.selectedStaff.id,
        widget.selectedService.serviceCategory.code
      );
      final endTime = _calculateEndTime(startTime, duration);

      // 打印调试信息
      print('🔍 预约数据调试信息:');
      print('  店铺ID: $storeId');
      print('  客户ID: ${currentUser.uid}');
      print('  员工ID: ${widget.selectedStaff.id}');
      print('  员工姓名: ${widget.selectedStaff.name}');
      print('  服务ID: ${widget.selectedService.sid}');
      print('  服务名称: ${widget.selectedService.serviceName}');
      print('  预约日期: ${widget.selectedDate}');
      print('  开始时间: $startTime');
      print('  结束时间: $endTime');
      print('  服务时长: $duration 分钟');
      print('  备注: ${widget.notes}');

      // 创建预约
      final appointmentId = await _appointmentService.createAppointment(
        storeId: storeId,
        customerId: currentUser.uid,
        staffId: widget.selectedStaff.id, // 使用id而不是uid
        serviceId: widget.selectedService.sid!,
        appointmentDate: widget.selectedDate,
        startTime: startTime,
        endTime: endTime,
        notes: widget.notes.isNotEmpty ? widget.notes : null,
        customerNotes: widget.notes.isNotEmpty ? widget.notes : null,
        createdBy: currentUser.uid,
      );

      print('✅ 预约创建成功: $appointmentId');

      setState(() {
        _appointmentSaved = true;
        _isLoading = false;
      });

    } catch (e) {
      print('❌ 预约创建失败: $e');
      setState(() {
        _isLoading = false;
      });

      // 显示错误提示
      Get.snackbar(
        '预约失败',
        e.toString(),
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 从员工服务中获取服务时长
  Future<int> _getServiceDurationFromEmployeeService(String staffId, String serviceCategory) async {
    try {
      print('🔍 查询员工服务时长: staffId=$staffId, serviceCategory=$serviceCategory');

      // 查询portal-user-data集合中的employee-service子集合
      final QuerySnapshot querySnapshot = await FirebaseFirestore.instance
          .collectionGroup('employee-service')
          .where('uid', isEqualTo: staffId)
          .where('serviceCategory', isEqualTo: serviceCategory)
          .where('serviceStatus', isEqualTo: 'active')
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final doc = querySnapshot.docs.first;
        final data = doc.data() as Map<String, dynamic>;
        final duration = data['serviceDuration'] as int? ?? 60;
        print('✅ 找到员工服务时长: $duration 分钟');
        return duration;
      } else {
        print('⚠️ 未找到员工服务，使用默认时长: 60 分钟');
        return 60; // 默认60分钟
      }
    } catch (e) {
      print('❌ 获取员工服务时长失败: $e');
      return 60; // 默认60分钟
    }
  }

  /// 计算结束时间
  String _calculateEndTime(String startTime, int durationMinutes) {
    final parts = startTime.split(':');
    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);

    final startDateTime = DateTime(2000, 1, 1, hour, minute);
    final endDateTime = startDateTime.add(Duration(minutes: durationMinutes));

    return '${endDateTime.hour.toString().padLeft(2, '0')}:${endDateTime.minute.toString().padLeft(2, '0')}';
  }

  void _backToMain() {
    Get.back();
    widget.onBackToMain?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 403.w,
      height: 823.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(26.w)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 拖拽指示条
          Container(
            margin: EdgeInsets.only(top: 12.w),
            width: double.infinity,
            alignment: Alignment.center,
            child: Container(
              width: 36.w,
              height: 4.w,
              decoration: BoxDecoration(
                color: Color(0xFFE0E0E0),
                borderRadius: BorderRadius.circular(2.w),
              ),
            ),
          ),
          
          // 成功标题
          Padding(
            padding: EdgeInsets.only(top: 32.w, left: 24.w, right: 24.w),
            child: Text(
              'Your appointment is booked!',
              style: TextStyle(
                fontFamily: 'Manrope',
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: Color(0xFF262626),
              ),
            ),
          ),
          
          // 副标题
          Padding(
            padding: EdgeInsets.only(top: 8.w, left: 24.w, right: 24.w),
            child: Text(
              'We\'ll send you an email your appointment.',
              style: TextStyle(
                fontFamily: 'Manrope',
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                color: Color(0xFF666666),
              ),
            ),
          ),
          
          // 预约详情
          Expanded(
            child: Container(
              margin: EdgeInsets.all(24.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 24.w),
                  
                  // 宠物名称
                  _buildDetailRow('Pet Name:', widget.selectedPet.name ?? 'Unknown'),

                  SizedBox(height: 16.w),

                  // 时间
                  _buildDetailRow(
                    'Time:',
                    '${DateFormat('MMMM d, yyyy').format(widget.selectedDate)} at ${widget.selectedTimeSlot.time}'
                  ),

                  SizedBox(height: 16.w),

                  // 地址 (写死的示例地址)
                  _buildDetailRow(
                    'Location:',
                    '13071 Vanier Pl, Richmond, BC, V6V2J1'
                  ),

                  SizedBox(height: 16.w),

                  // 服务
                  _buildDetailRow('Service:', widget.selectedService.serviceName),

                  SizedBox(height: 16.w),

                  // 价格
                  _buildDetailRow('Price:', 'CAD \$${widget.selectedService.basePrice.toStringAsFixed(2)}'),

                  SizedBox(height: 16.w),

                  // 预计时间
                  _buildDetailRow('Estimated Time:', '${widget.selectedService.defaultDuration} min'),

                  SizedBox(height: 16.w),

                  // 工作人员
                  _buildDetailRow('Staff:', widget.selectedStaff.name),

                  // 备注 (如果有的话)
                  if (widget.notes.isNotEmpty) ...[
                    SizedBox(height: 16.w),
                    _buildDetailRow('Notes:', widget.notes),
                  ],
                ],
              ),
            ),
          ),
          
          // Back to main 按钮
          Container(
            margin: EdgeInsets.only(bottom: 20.w, left: 24.w, right: 24.w),
            width: double.infinity,
            height: 48.w,
            child: ElevatedButton(
              onPressed: _backToMain,
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFFF2D3A4),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.w),
                ),
                elevation: 0,
              ),
              child: Text(
                'Back to main',
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        SizedBox(height: 4.w),
        Text(
          value,
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: Color(0xFF666666),
          ),
        ),
      ],
    );
  }
}

/// 显示预约确认底部弹窗的静态方法
class AppointmentConfirmationBottomSheetHelper {
  static void show({
    required Pet selectedPet,
    required BookableService selectedService,
    required DateTime selectedDate,
    required TimeSlot selectedTimeSlot,
    required Staff selectedStaff,
    required String notes,
    VoidCallback? onBackToMain,
  }) {
    Get.bottomSheet(
      AppointmentConfirmationBottomSheet(
        selectedPet: selectedPet,
        selectedService: selectedService,
        selectedDate: selectedDate,
        selectedTimeSlot: selectedTimeSlot,
        selectedStaff: selectedStaff,
        notes: notes,
        onBackToMain: onBackToMain,
      ),
      isDismissible: true,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }
}
