import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:onenata_app/models/pet.dart';
import 'package:onenata_app/models/bookable_service.dart';
import 'package:onenata_app/models/employee_schedule.dart';
import 'package:onenata_app/models/staff_service_offering.dart';
import 'package:onenata_app/services/employee_service.dart';
import 'package:onenata_app/views/theme/colors/onenata_classic_colors.dart';
import 'package:onenata_app/views/component/appointment/note_input_bottom_sheet.dart';

// 时间段数据模型
class TimeSlot {
  final String id;
  final String time;
  final bool isAvailable;

  TimeSlot({
    required this.id,
    required this.time,
    this.isAvailable = true,
  });
}

// 人员数据模型
class Staff {
  final String id;
  final String name;
  final bool isAvailable;

  Staff({
    required this.id,
    required this.name,
    this.isAvailable = true,
  });
}

class DateTimeSelectionBottomSheet extends StatefulWidget {
  final Pet selectedPet;
  final BookableService selectedService;
  final VoidCallback? onAppointmentConfirmed;

  const DateTimeSelectionBottomSheet({
    Key? key,
    required this.selectedPet,
    required this.selectedService,
    this.onAppointmentConfirmed,
  }) : super(key: key);

  @override
  State<DateTimeSelectionBottomSheet> createState() => _DateTimeSelectionBottomSheetState();
}

class _DateTimeSelectionBottomSheetState extends State<DateTimeSelectionBottomSheet> {
  DateTime? selectedDate;
  TimeSlot? selectedTimeSlot;
  Staff? selectedStaff;

  // 动态数据
  List<TimeSlot> timeSlots = [];
  List<Staff> staffList = [];
  List<EmployeeSchedule> employeeSchedules = [];
  List<StaffServiceOffering> employeeServices = [];
  Map<String, String> employeeNames = {}; // 存储员工ID到姓名的映射

  bool isLoadingTimeSlots = false;
  bool isLoadingStaff = false;
  bool isTimeSlotExpanded = false; // 时间选择器展开状态

  final EmployeeService _employeeService = EmployeeService();

  @override
  void initState() {
    super.initState();
    // 默认选择当前日期
    selectedDate = DateTime.now();
    _loadEmployeeData();
  }

  /// 加载员工数据
  Future<void> _loadEmployeeData() async {
    try {
      // 从BookableService中获取storeId
      final storeId = widget.selectedService.storeId;

      print('🔍 加载员工数据，storeId: $storeId');

      // 加载员工排班和服务信息
      final schedules = await _employeeService.getStoreEmployeeSchedules(storeId);
      final services = await _employeeService.getStoreEmployeeServices(storeId);

      // 加载员工姓名
      await _loadEmployeeNames(services);

      setState(() {
        employeeSchedules = schedules;
        employeeServices = services;
      });

      // 生成员工列表
      _generateStaffList();

      print('✅ 员工数据加载完成: ${schedules.length} 个排班, ${services.length} 个服务');
    } catch (e) {
      print('❌ 加载员工数据失败: $e');
    }
  }

  /// 加载员工姓名
  Future<void> _loadEmployeeNames(List<StaffServiceOffering> services) async {
    try {
      final uniqueUids = services.map((service) => service.uid).toSet();
      print('🔍 开始加载员工姓名，共 ${uniqueUids.length} 个员工');

      for (final uid in uniqueUids) {
        // 这里应该从portal-user-data获取真实姓名
        // 暂时使用简化的逻辑，你可以根据实际需求修改
        final name = await _getEmployeeNameFromDatabase(uid);
        employeeNames[uid] = name;
        print('✅ 员工姓名加载: $uid -> $name');
      }

      print('🔍 员工姓名映射: $employeeNames');
    } catch (e) {
      print('❌ 加载员工姓名失败: $e');
    }
  }

  /// 从数据库获取员工姓名
  Future<String> _getEmployeeNameFromDatabase(String uid) async {
    try {
      // 从portal-user-data集合获取员工信息
      final userDoc = await FirebaseFirestore.instance
          .collection('portal-user-data')
          .doc(uid)
          .get();

      if (userDoc.exists) {
        final data = userDoc.data() as Map<String, dynamic>;

        // 尝试获取displayName或firstName + lastName
        if (data['displayName'] != null && data['displayName'].toString().isNotEmpty) {
          return data['displayName'].toString();
        }

        final firstName = data['firstName']?.toString() ?? '';
        final lastName = data['lastName']?.toString() ?? '';

        if (firstName.isNotEmpty || lastName.isNotEmpty) {
          return '$firstName $lastName'.trim();
        }
      }

      // 如果没有找到姓名信息，使用备用名称
      return 'Staff ${uid.substring(0, 8)}';
    } catch (e) {
      print('❌ 获取员工姓名失败 $uid: $e');
      return 'Staff ${uid.substring(0, 8)}'; // 使用UID前8位作为备用名称
    }
  }

  /// 生成员工列表
  void _generateStaffList() {
    final serviceCategory = widget.selectedService.serviceCategory.code;

    // 找到提供该服务类别的员工
    final availableEmployees = employeeServices
        .where((service) => service.serviceCategory == serviceCategory && service.isActive())
        .map((service) => service.uid)
        .toSet()
        .toList();

    // 生成员工列表
    staffList = availableEmployees.map((uid) {
      return Staff(
        id: uid,
        name: _getEmployeeName(uid),
        isAvailable: _isEmployeeAvailable(uid),
      );
    }).toList();

    print('👥 生成员工列表: ${staffList.length} 个员工');
  }

  /// 获取员工姓名
  String _getEmployeeName(String uid) {
    final name = employeeNames[uid] ?? 'Staff ${uid.substring(0, 8)}';
    print('🔍 获取员工姓名: $uid -> $name (映射中的值: ${employeeNames[uid]})');
    return name;
  }

  /// 检查员工是否可用
  bool _isEmployeeAvailable(String uid) {
    if (selectedDate == null) return true;

    final schedule = employeeSchedules.firstWhere(
      (schedule) => schedule.uid == uid,
      orElse: () => EmployeeSchedule(
        uid: uid,
        storeId: widget.selectedService.storeId,
        active: false,
        workTimes: [],
      ),
    );

    final weekday = _getWeekdayName(selectedDate!.weekday);
    return schedule.isWorkingDay(weekday);
  }

  /// 获取星期名称
  String _getWeekdayName(int weekday) {
    const weekdays = [
      'Monday', 'Tuesday', 'Wednesday', 'Thursday',
      'Friday', 'Saturday', 'Sunday'
    ];
    return weekdays[weekday - 1];
  }

  /// 加载时间段
  Future<void> _loadTimeSlots() async {
    if (selectedDate == null || selectedStaff == null) return;

    setState(() {
      isLoadingTimeSlots = true;
      timeSlots = [];
    });

    try {
      final storeId = widget.selectedService.storeId;

      // 只为选择的员工加载时间段
      final employeeTimeSlots = await _employeeService.getAvailableTimeSlots(
        storeId: storeId,
        employeeUid: selectedStaff!.id,
        date: selectedDate!,
      );

      // 转换为TimeSlot对象并排序
      final sortedTimes = employeeTimeSlots.toList()..sort();
      timeSlots = sortedTimes.map((time) => TimeSlot(
        id: time,
        time: time,
        isAvailable: true,
      )).toList();

      print('⏰ 为员工 ${selectedStaff!.name} 生成时间段: ${timeSlots.length} 个时间段');
    } catch (e) {
      print('❌ 加载时间段失败: $e');
    } finally {
      setState(() {
        isLoadingTimeSlots = false;
      });
    }
  }

  final List<String> weekLabels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  // 动态生成月份列表
  List<String> get months {
    final now = DateTime.now();
    final currentYear = now.year;
    return [
      '$currentYear',
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
  }

  void _selectDate(DateTime date) {
    setState(() {
      selectedDate = date;
      // 重置后续选择
      selectedTimeSlot = null;
      selectedStaff = null;
      isTimeSlotExpanded = false;
    });

    // 重新生成员工列表（不加载时间段，等选择员工后再加载）
    _generateStaffList();
  }

  void _selectTimeSlot(TimeSlot timeSlot) {
    if (selectedDate != null && timeSlot.isAvailable) {
      setState(() {
        selectedTimeSlot = timeSlot;
        // 自动收起时间选择器
        isTimeSlotExpanded = false;
      });
    }
  }

  void _selectStaff(Staff staff) {
    if (selectedDate != null && staff.isAvailable) {
      setState(() {
        selectedStaff = staff;
        // 重置时间选择
        selectedTimeSlot = null;
        isTimeSlotExpanded = false;
      });
      // 根据选择的员工重新加载时间段
      _loadTimeSlots();
    }
  }

  void _continueToNext() {
    if (selectedDate != null && selectedTimeSlot != null && selectedStaff != null) {
      // 关闭当前弹窗并打开备注输入弹窗
      Get.back();
      NoteInputBottomSheetHelper.show(
        selectedPet: widget.selectedPet,
        selectedService: widget.selectedService,
        selectedDate: selectedDate!,
        selectedTimeSlot: selectedTimeSlot!,
        selectedStaff: selectedStaff!,
        onAppointmentSubmitted: () {
          widget.onAppointmentConfirmed?.call();
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 403.w,
      height: 823.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(26.w)),
      ),
      child: Column(
        children: [
          // 拖拽指示条
          Container(
            margin: EdgeInsets.only(top: 12.w),
            width: 36.w,
            height: 4.w,
            decoration: BoxDecoration(
              color: Color(0xFFE0E0E0),
              borderRadius: BorderRadius.circular(2.w),
            ),
          ),
          
          // 月份选择器
          _buildMonthSelector(),

          // 日历 - 减少空间占用
          SizedBox(
            height: 280.w, // 固定高度，减少空间占用
            child: _buildCalendar(),
          ),

          // 人员选择 - 紧跟在日历下方
          if (selectedDate != null) ...[
            SizedBox(height: 16.w),
            _buildStaffSelection(),
          ],

          // 时间段选择 - 紧跟在员工选择下方
          if (selectedDate != null && selectedStaff != null) ...[
            SizedBox(height: 16.w),
            _buildTimeSlotSelection(),
          ],

          // 弹性空间，将Continue按钮推到底部
          Expanded(child: SizedBox()),
          
          // Continue 按钮
          Container(
            margin: EdgeInsets.only(bottom: 20.w, left: 24.w, right: 24.w),
            width: double.infinity,
            height: 48.w,
            child: ElevatedButton(
              onPressed: (selectedDate != null && selectedTimeSlot != null && selectedStaff != null) 
                  ? _continueToNext 
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: (selectedDate != null && selectedTimeSlot != null && selectedStaff != null)
                    ? Color(0xFFF2D3A4) 
                    : Color(0xFFC6C6C6),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.w),
                ),
                elevation: 0,
              ),
              child: Text(
                'Continue',
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthSelector() {
    final currentMonth = selectedDate?.month ?? DateTime.now().month;

    return Container(
      height: 50.w,
      margin: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.w),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: months.length,
        separatorBuilder: (_, __) => SizedBox(width: 16.w),
        itemBuilder: (context, index) {
          final month = months[index];
          final isYear = index == 0;
          final isSelected = !isYear && currentMonth == index;

          return GestureDetector(
            onTap: () {
              if (!isYear) {
                final now = DateTime.now();
                final newDate = DateTime(now.year, index, 1);
                _selectDate(newDate);
              }
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.w),
              decoration: BoxDecoration(
                color: isSelected ? OneNataClassicColors.veronica.withValues(alpha: 0.1) : Colors.transparent,
                borderRadius: BorderRadius.circular(8.w),
              ),
              child: Text(
                month,
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 14.sp,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected ? OneNataClassicColors.veronica : Color(0xFF666666),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCalendar() {
    final now = DateTime.now();
    final currentYear = selectedDate?.year ?? now.year;
    final currentMonth = selectedDate?.month ?? now.month;

    final firstDayOfMonth = DateTime(currentYear, currentMonth, 1);
    final startWeekday = firstDayOfMonth.weekday % 7;
    final totalDaysThisMonth = DateTime(currentYear, currentMonth + 1, 0).day;
    final totalDaysLastMonth = DateTime(currentYear, currentMonth, 0).day;

    List<DateTime> visibleDates = [];

    // 上个月的日期
    for (int i = startWeekday - 1; i >= 0; i--) {
      visibleDates.add(DateTime(currentYear, currentMonth - 1, totalDaysLastMonth - i));
    }

    // 当前月的日期
    for (int i = 1; i <= totalDaysThisMonth; i++) {
      visibleDates.add(DateTime(currentYear, currentMonth, i));
    }

    // 下个月的日期
    int remaining = 42 - visibleDates.length;
    for (int i = 1; i <= remaining; i++) {
      visibleDates.add(DateTime(currentYear, currentMonth + 1, i));
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          // 星期标题
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: weekLabels.map((label) {
              return SizedBox(
                width: 42.w,
                child: Center(
                  child: Text(
                    label,
                    style: TextStyle(
                      fontFamily: 'Manrope',
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF999999),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          
          SizedBox(height: 8.w),
          
          // 日历网格
          Expanded(
            child: GridView.builder(
              padding: EdgeInsets.zero,
              itemCount: 42,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 7,
                crossAxisSpacing: 8.w,
                mainAxisSpacing: 8.w,
                childAspectRatio: 1,
              ),
              itemBuilder: (context, index) {
                final date = visibleDates[index];
                final isSelected = selectedDate != null &&
                    selectedDate!.year == date.year &&
                    selectedDate!.month == date.month &&
                    selectedDate!.day == date.day;
                final isCurrentMonth = date.month == currentMonth;
                final isToday = date.year == now.year &&
                    date.month == now.month &&
                    date.day == now.day;
                final isPastDate = date.isBefore(DateTime(now.year, now.month, now.day));
                final isAvailable = !isPastDate;

                return GestureDetector(
                  onTap: isAvailable ? () => _selectDate(date) : null,
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Color(0xFFF2D3A4)
                          : (isToday ? OneNataClassicColors.veronica.withValues(alpha: 0.1) : Colors.transparent),
                      borderRadius: BorderRadius.circular(8.w),
                      border: isCurrentMonth
                          ? Border.all(color: Color(0xFFE5E5E5), width: 1.w)
                          : null,
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      '${date.day}',
                      style: TextStyle(
                        fontFamily: 'Manrope',
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: isSelected
                            ? Colors.white
                            : (!isAvailable
                                ? Color(0xFFCCCCCC)
                                : (isCurrentMonth ? Color(0xFF262626) : Color(0xFFCCCCCC))),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSlotSelection() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 时间选择器标题和下拉按钮
          GestureDetector(
            onTap: () {
              if (timeSlots.isNotEmpty && !isLoadingTimeSlots) {
                setState(() {
                  isTimeSlotExpanded = !isTimeSlotExpanded;
                });
              }
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.w),
                border: Border.all(color: Color(0xFFE5E5E5)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    selectedTimeSlot != null
                        ? selectedTimeSlot!.time
                        : 'Select Time',
                    style: TextStyle(
                      fontFamily: 'Manrope',
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: selectedTimeSlot != null
                          ? Color(0xFF262626)
                          : Color(0xFF999999),
                    ),
                  ),
                  isLoadingTimeSlots
                      ? SizedBox(
                          width: 16.w,
                          height: 16.w,
                          child: CircularProgressIndicator(
                            color: OneNataClassicColors.veronica,
                            strokeWidth: 2.w,
                          ),
                        )
                      : Icon(
                          isTimeSlotExpanded
                              ? Icons.keyboard_arrow_up
                              : Icons.keyboard_arrow_down,
                          color: timeSlots.isNotEmpty
                              ? Color(0xFF262626)
                              : Color(0xFF999999),
                          size: 20.w,
                        ),
                ],
              ),
            ),
          ),
          // 展开的时间选择列表
          if (isTimeSlotExpanded && timeSlots.isNotEmpty)
            Container(
              margin: EdgeInsets.only(top: 8.w),
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.w),
                border: Border.all(color: Color(0xFFE5E5E5)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8.w,
                    offset: Offset(0, 2.w),
                  ),
                ],
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(maxHeight: 120.w),
                child: SingleChildScrollView(
                  child: Wrap(
                    spacing: 8.w,
                    runSpacing: 8.w,
                    children: timeSlots.map((timeSlot) {
                      final isSelected = selectedTimeSlot?.id == timeSlot.id;
                      final isEnabled = selectedDate != null && timeSlot.isAvailable;

                      return GestureDetector(
                        onTap: () => _selectTimeSlot(timeSlot),
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.w),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? OneNataClassicColors.veronica
                                : (isEnabled ? Colors.white : Color(0xFFF5F5F5)),
                            borderRadius: BorderRadius.circular(16.w),
                            border: Border.all(
                              color: isSelected
                                  ? OneNataClassicColors.veronica
                                  : (isEnabled ? Color(0xFFE5E5E5) : Color(0xFFCCCCCC)),
                              width: 1.w,
                            ),
                          ),
                          child: Text(
                            timeSlot.time,
                            style: TextStyle(
                              fontFamily: 'Manrope',
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w500,
                              color: isSelected
                                  ? Colors.white
                                  : (isEnabled ? Color(0xFF262626) : Color(0xFF999999)),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStaffSelection() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Staff',
            style: TextStyle(
              fontFamily: 'Manrope',
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Color(0xFF262626),
            ),
          ),
          SizedBox(height: 12.w),
          staffList.isEmpty
              ? Container(
                  height: 48.w,
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  decoration: BoxDecoration(
                    color: Color(0xFFF5F5F5),
                    borderRadius: BorderRadius.circular(12.w),
                    border: Border.all(color: Color(0xFFE5E5E5)),
                  ),
                  child: Center(
                    child: Text(
                      'No staff available for this service',
                      style: TextStyle(
                        fontFamily: 'Manrope',
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF999999),
                      ),
                    ),
                  ),
                )
              : Column(
                  children: staffList.map((staff) {
                    final isSelected = selectedStaff?.id == staff.id;
                    final isEnabled = selectedDate != null && staff.isAvailable;

                    return Container(
                      margin: EdgeInsets.only(bottom: 8.w),
                      child: GestureDetector(
                        onTap: () => _selectStaff(staff),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? OneNataClassicColors.veronica
                                : (isEnabled ? Colors.white : Color(0xFFF5F5F5)),
                            borderRadius: BorderRadius.circular(8.w),
                            border: Border.all(
                              color: isSelected
                                  ? OneNataClassicColors.veronica
                                  : (isEnabled ? Color(0xFFE5E5E5) : Color(0xFFCCCCCC)),
                              width: 1.w,
                            ),
                          ),
                          child: Text(
                            staff.name,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontFamily: 'Manrope',
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w500,
                              color: isSelected
                                  ? Colors.white
                                  : (isEnabled ? Color(0xFF262626) : Color(0xFF999999)),
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
        ],
      ),
    );
  }
}

/// 显示日期时间选择底部弹窗的静态方法
class DateTimeSelectionBottomSheetHelper {
  static void show({
    required Pet selectedPet,
    required BookableService selectedService,
    VoidCallback? onAppointmentConfirmed,
  }) {
    Get.bottomSheet(
      DateTimeSelectionBottomSheet(
        selectedPet: selectedPet,
        selectedService: selectedService,
        onAppointmentConfirmed: onAppointmentConfirmed,
      ),
      isDismissible: true,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }
}
