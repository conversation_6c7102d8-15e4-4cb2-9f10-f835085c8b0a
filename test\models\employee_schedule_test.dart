import 'package:flutter_test/flutter_test.dart';
import 'package:onenata_app/models/models_i.dart';

void main() {
  group('WorkTime Model Tests', () {
    late WorkTime testWorkTime;

    setUp(() {
      testWorkTime = WorkTime(
        weekday: 'Monday',
        open: true,
        startTime: '09:00',
        endTime: '17:00',
      );
    });

    test('should create WorkTime with required fields', () {
      expect(testWorkTime.weekday, equals('Monday'));
      expect(testWorkTime.open, isTrue);
      expect(testWorkTime.startTime, equals('09:00'));
      expect(testWorkTime.endTime, equals('17:00'));
    });

    test('should calculate duration correctly', () {
      expect(testWorkTime.getDurationInMinutes(), equals(480)); // 8 hours = 480 minutes

      // Test closed day
      testWorkTime.open = false;
      expect(testWorkTime.getDurationInMinutes(), equals(0));

      // Test different times
      testWorkTime.open = true;
      testWorkTime.startTime = '10:30';
      testWorkTime.endTime = '14:45';
      expect(testWorkTime.getDurationInMinutes(), equals(255)); // 4h 15m = 255 minutes
    });

    test('should check if time is in working hours correctly', () {
      expect(testWorkTime.isTimeInWorkingHours('10:00'), isTrue);
      expect(testWorkTime.isTimeInWorkingHours('12:30'), isTrue);
      expect(testWorkTime.isTimeInWorkingHours('16:59'), isTrue);

      // Outside working hours
      expect(testWorkTime.isTimeInWorkingHours('08:59'), isFalse);
      expect(testWorkTime.isTimeInWorkingHours('17:00'), isFalse);
      expect(testWorkTime.isTimeInWorkingHours('18:00'), isFalse);

      // Closed day
      testWorkTime.open = false;
      expect(testWorkTime.isTimeInWorkingHours('12:00'), isFalse);
    });

    test('should copy with new values correctly', () {
      final copied = testWorkTime.copyWith(
        weekday: 'Tuesday',
        startTime: '08:00',
      );

      expect(copied.weekday, equals('Tuesday'));
      expect(copied.startTime, equals('08:00'));
      expect(copied.endTime, equals('17:00')); // unchanged
      expect(copied.open, isTrue); // unchanged
    });

    test('should convert to/from JSON correctly', () {
      final json = testWorkTime.toJson();
      final fromJson = WorkTime.fromJson(json);

      expect(fromJson.weekday, equals(testWorkTime.weekday));
      expect(fromJson.open, equals(testWorkTime.open));
      expect(fromJson.startTime, equals(testWorkTime.startTime));
      expect(fromJson.endTime, equals(testWorkTime.endTime));
    });
  });

  group('EmployeeSchedule Model Tests', () {
    late EmployeeSchedule testSchedule;
    late List<WorkTime> testWorkTimes;

    setUp(() {
      testWorkTimes = [
        WorkTime(weekday: 'Monday', open: true, startTime: '09:00', endTime: '17:00'),
        WorkTime(weekday: 'Tuesday', open: true, startTime: '09:00', endTime: '17:00'),
        WorkTime(weekday: 'Wednesday', open: true, startTime: '09:00', endTime: '17:00'),
        WorkTime(weekday: 'Thursday', open: true, startTime: '09:00', endTime: '17:00'),
        WorkTime(weekday: 'Friday', open: true, startTime: '09:00', endTime: '17:00'),
        WorkTime(weekday: 'Saturday', open: false, startTime: '09:00', endTime: '17:00'),
        WorkTime(weekday: 'Sunday', open: false, startTime: '09:00', endTime: '17:00'),
      ];

      testSchedule = EmployeeSchedule(
        uid: 'test_user_id',
        storeId: 'test_store_id',
        active: true,
        workTimes: testWorkTimes,
        sid: 'test_schedule_id',
        isValid: true,
        isSynced: true,
      );
    });

    test('should create EmployeeSchedule with required fields', () {
      expect(testSchedule.uid, equals('test_user_id'));
      expect(testSchedule.storeId, equals('test_store_id'));
      expect(testSchedule.active, isTrue);
      expect(testSchedule.workTimes.length, equals(7));
    });

    test('should get work time for specific day correctly', () {
      final mondayWorkTime = testSchedule.getWorkTimeForDay('Monday');
      expect(mondayWorkTime, isNotNull);
      expect(mondayWorkTime!.weekday, equals('Monday'));
      expect(mondayWorkTime.open, isTrue);

      final sundayWorkTime = testSchedule.getWorkTimeForDay('Sunday');
      expect(sundayWorkTime, isNotNull);
      expect(sundayWorkTime!.open, isFalse);

      // Non-existent day
      final invalidWorkTime = testSchedule.getWorkTimeForDay('InvalidDay');
      expect(invalidWorkTime, isNull);
    });

    test('should check if day is working day correctly', () {
      expect(testSchedule.isWorkingDay('Monday'), isTrue);
      expect(testSchedule.isWorkingDay('Tuesday'), isTrue);
      expect(testSchedule.isWorkingDay('Friday'), isTrue);
      expect(testSchedule.isWorkingDay('Saturday'), isFalse);
      expect(testSchedule.isWorkingDay('Sunday'), isFalse);

      // Non-existent day
      expect(testSchedule.isWorkingDay('InvalidDay'), isFalse);
    });

    test('should count working days correctly', () {
      expect(testSchedule.getWorkingDaysCount(), equals(5));

      // Add Saturday as working day
      testSchedule.workTimes[5].open = true;
      expect(testSchedule.getWorkingDaysCount(), equals(6));
    });

    test('should calculate total working minutes per week correctly', () {
      // 5 days * 8 hours * 60 minutes = 2400 minutes
      expect(testSchedule.getTotalWorkingMinutesPerWeek(), equals(2400));

      // Add Saturday (8 hours)
      testSchedule.workTimes[5].open = true;
      expect(testSchedule.getTotalWorkingMinutesPerWeek(), equals(2880));

      // Change Friday to half day (4 hours)
      testSchedule.workTimes[4].endTime = '13:00';
      expect(testSchedule.getTotalWorkingMinutesPerWeek(), equals(2640)); // 2880 - 240
    });

    test('should copy with new values correctly', () {
      final newWorkTimes = [
        WorkTime(weekday: 'Monday', open: true, startTime: '08:00', endTime: '16:00'),
      ];

      final copied = testSchedule.copyWith(
        storeId: 'new_store_id',
        active: false,
        workTimes: newWorkTimes,
      );

      expect(copied.uid, equals('test_user_id')); // unchanged
      expect(copied.storeId, equals('new_store_id'));
      expect(copied.active, isFalse);
      expect(copied.workTimes.length, equals(1));
      expect(copied.workTimes[0].startTime, equals('08:00'));
    });

    test('should convert to/from JSON correctly', () {
      final json = testSchedule.toJson();

      // Verify JSON structure
      expect(json['uid'], equals(testSchedule.uid));
      expect(json['storeId'], equals(testSchedule.storeId));
      expect(json['active'], equals(testSchedule.active));
      expect(json['workTimes'], isA<List>());

      final fromJson = EmployeeSchedule.fromJson(json);

      expect(fromJson.uid, equals(testSchedule.uid));
      expect(fromJson.storeId, equals(testSchedule.storeId));
      expect(fromJson.active, equals(testSchedule.active));
      expect(fromJson.workTimes.length, equals(testSchedule.workTimes.length));
      expect(fromJson.workTimes[0].weekday, equals(testSchedule.workTimes[0].weekday));
    });

    test('should convert to Firestore data correctly', () {
      final firestoreData = testSchedule.toFirestoreData();

      expect(firestoreData['uid'], equals('test_user_id'));
      expect(firestoreData['store_id'], equals('test_store_id'));
      expect(firestoreData['active'], isTrue);
      expect(firestoreData['work_times'], isA<List>());
      expect(firestoreData['work_times'].length, equals(7));
    });

    test('should have correct collection name', () {
      expect(EmployeeSchedule.collection, equals('employee-schedule'));
    });

    test('should handle default values correctly', () {
      final defaultSchedule = EmployeeSchedule(
        uid: 'test_uid',
        storeId: 'test_store_id',
      );

      expect(defaultSchedule.active, isTrue);
      expect(defaultSchedule.workTimes, isEmpty);
      expect(defaultSchedule.isValid, isTrue);
      expect(defaultSchedule.isSynced, isTrue);
    });
  });
}
