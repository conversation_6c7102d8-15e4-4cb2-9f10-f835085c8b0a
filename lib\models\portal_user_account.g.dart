// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'portal_user_account.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PortalUserAccount _$PortalUserAccountFromJson(Map<String, dynamic> json) =>
    PortalUserAccount(
      fid: json['fid'] as String,
      email: json['email'] as String,
      phoneNumber: json['phoneNumber'] as String,
      isEmailVerified: json['isEmailVerified'] as bool? ?? false,
      needChangePassword: json['needChangePassword'] as bool? ?? true,
      sid: json['sid'] as String?,
      isValid: json['isValid'] == null
          ? true
          : JsonUtil.boolFromJson(json['isValid']),
      isSynced: json['isSynced'] == null
          ? true
          : JsonUtil.boolFromJson(json['isSynced']),
    )
      ..id = (json['id'] as num?)?.toInt()
      ..name = json['name'] as String?;

Map<String, dynamic> _$PortalUserAccountToJson(PortalUserAccount instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'fid': instance.fid,
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
      'isEmailVerified': instance.isEmailVerified,
      'needChangePassword': instance.needChangePassword,
    };
