// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'store_account.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StoreAccount _$StoreAccountFromJson(Map<String, dynamic> json) => StoreAccount(
      ownerId: json['ownerId'] as String,
      storeName: json['storeName'] as String,
      storeVerifiedStatus: $enumDecodeNullable(
          _$StoreVerifiedStatusEnumMap, json['storeVerifiedStatus']),
      storeStatus: $enumDecode(_$StoreStatusEnumMap, json['storeStatus']),
      googlePlaceId: json['googlePlaceId'] as String,
      salt: json['salt'] as String?,
      hashedCredential: json['hashedCredential'] as String?,
      id: (json['id'] as num?)?.toInt(),
      sid: json['sid'] as String?,
      name: json['name'] as String?,
      isValid: JsonUtil.boolFromJson(json['isValid']),
      isSynced: JsonUtil.boolFromJson(json['isSynced']),
      createdBy: json['createdBy'] as String?,
      createDate: (json['createDate'] as num?)?.toInt(),
      updatedBy: json['updatedBy'] as String?,
      updateDate: (json['updateDate'] as num?)?.toInt(),
      reviewedBy: json['reviewedBy'] as String?,
      reviewDate: (json['reviewDate'] as num?)?.toInt(),
      approveStatus: JsonUtil.boolFromJson(json['approveStatus']),
      notes: json['notes'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$StoreAccountToJson(StoreAccount instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'createdBy': instance.createdBy,
      'createDate': instance.createDate,
      'updatedBy': instance.updatedBy,
      'updateDate': instance.updateDate,
      'reviewedBy': instance.reviewedBy,
      'reviewDate': instance.reviewDate,
      'approveStatus': JsonUtil.boolToJson(instance.approveStatus),
      'notes': instance.notes,
      'tags': instance.tags,
      'ownerId': instance.ownerId,
      'storeName': instance.storeName,
      'storeVerifiedStatus':
          _$StoreVerifiedStatusEnumMap[instance.storeVerifiedStatus],
      'storeStatus': _$StoreStatusEnumMap[instance.storeStatus]!,
      'googlePlaceId': instance.googlePlaceId,
      'salt': instance.salt,
      'hashedCredential': instance.hashedCredential,
    };

const _$StoreVerifiedStatusEnumMap = {
  StoreVerifiedStatus.pending: 'pending',
  StoreVerifiedStatus.approved: 'approved',
  StoreVerifiedStatus.rejected: 'rejected',
  StoreVerifiedStatus.needMoreInfo: 'needMoreInfo',
};

const _$StoreStatusEnumMap = {
  StoreStatus.active: 'active',
  StoreStatus.inactive: 'inactive',
  StoreStatus.suspended: 'suspended',
  StoreStatus.closed: 'closed',
};
