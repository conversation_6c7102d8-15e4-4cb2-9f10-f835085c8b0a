import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:onenata_app/dao/store_dao.dart';
import 'package:onenata_app/models/store_info.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/views/component/appointment/appointment_helper.dart';

/// 店铺选择测试页面
/// 用于测试预约流程，显示数据库中的所有店铺供用户选择
class StoreSelectionTestPage extends StatefulWidget {
  const StoreSelectionTestPage({Key? key}) : super(key: key);

  @override
  State<StoreSelectionTestPage> createState() => _StoreSelectionTestPageState();
}

class _StoreSelectionTestPageState extends State<StoreSelectionTestPage> {
  final StoreDao _storeDao = StoreDao();
  List<StoreInfo> stores = [];
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadStores();
  }

  Future<void> _loadStores() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      print('🔍 开始加载店铺数据...');

      // 先测试直接查询，然后测试DAO查询
      List<StoreInfo> allStores = [];
      try {
        print('📋 第一步：直接查询所有store-info文档...');

        // 直接查询验证数据存在
        final directQuery = await FirebaseFirestore.instance
            .collection('store-info')
            .get();
        print('📄 直接查询找到 ${directQuery.docs.length} 个文档');

        // 打印所有文档的基本信息
        for (int i = 0; i < directQuery.docs.length; i++) {
          final doc = directQuery.docs[i];
          final data = doc.data();
          print('📄 全部文档 ${i + 1} (${doc.id}):');
          print('  - name: ${data['name']}');
          print('  - businessType: ${data['businessType']}');
          print('  - appointmentOpen: ${data['appointmentOpen']}');
          print('  - isValid: ${data['isValid']}');
          print('  - 所有字段: ${data.keys.toList()}');
          print('  ---');
        }

        print('📋 第二步：测试DAO查询...');

        // 测试查询pet_store
        print('🔍 查询条件: businessType = ${BusinessType.petStore.value}');
        final petStores = await _storeDao.getStoreInfosByBusinessType(BusinessType.petStore);
        print('🛍️ 宠物店数量: ${petStores.length}');

        // 测试查询online_store
        print('🔍 查询条件: businessType = ${BusinessType.onlineStore.value}');
        final onlineStores = await _storeDao.getStoreInfosByBusinessType(BusinessType.onlineStore);
        print('💻 在线商店数量: ${onlineStores.length}');

        // 测试直接查询特定值
        print('📋 第三步：直接测试Firestore查询...');
        final testQuery = await FirebaseFirestore.instance
            .collection('store-info')
            .where('businessType', isEqualTo: 'pet_store')
            .where('isValid', isEqualTo: true)
            .get();
        print('🧪 直接查询pet_store结果: ${testQuery.docs.length} 个文档');

        final testQuery2 = await FirebaseFirestore.instance
            .collection('store-info')
            .where('businessType', isEqualTo: 'online_store')
            .where('isValid', isEqualTo: true)
            .get();
        print('🧪 直接查询online_store结果: ${testQuery2.docs.length} 个文档');

        // 查询所有文档进行解析测试
        final testQuery3 = await FirebaseFirestore.instance
            .collection('store-info')
            .get();
        print('🧪 查询所有文档进行解析测试: ${testQuery3.docs.length} 个文档');

        // 解析所有找到的文档
        print('🔧 开始解析所有找到的文档...');
        for (int i = 0; i < testQuery3.docs.length; i++) {
          try {
            final doc = testQuery3.docs[i];
            final data = doc.data();
            print('📋 文档 ${i + 1} (${doc.id}):');
            print('  - name: ${data['name']}');
            print('  - businessType: ${data['businessType']}');
            print('  - appointmentOpen: ${data['appointmentOpen']}');

            final storeInfo = StoreInfo.fromFirestoreData(doc);
            allStores.add(storeInfo);
            print('✅ 解析成功: ${storeInfo.name}');
          } catch (e, stackTrace) {
            print('❌ 解析文档 ${i + 1} 失败: $e');
          }
        }

        // 合并所有店铺（包括手动解析的和DAO查询的）
        allStores.addAll([...petStores, ...onlineStores]);

        print('📊 所有解析成功的店铺数量: ${allStores.length}');
        for (int i = 0; i < allStores.length; i++) {
          final store = allStores[i];
          print('🏪 店铺 ${i + 1}: ${store.name} (预约开放: ${store.appointmentOpen})');
        }
      } catch (e) {
        print('❌ 查询失败: $e');
      }


      // 获取开放预约的店铺
      final appointmentStores = await _storeDao.getAppointmentEnabledStores();
      print('✅ 开放预约的店铺数量: ${appointmentStores.length}');

      // 打印开放预约的店铺
      for (int i = 0; i < appointmentStores.length; i++) {
        final store = appointmentStores[i];
        print('🎯 预约店铺 ${i + 1}: ${store.name} (${store.storeId})');
      }

      setState(() {
        // 显示所有店铺，不仅仅是开放预约的（用于测试）
        stores = allStores;
        isLoading = false;
      });
    } catch (e) {
      print('❌ 加载店铺失败: $e');
      setState(() {
        errorMessage = 'Failed to load stores: $e';
        isLoading = false;
      });
    }
  }

  void _startAppointmentFlow(StoreInfo store) {
    // 显示确认对话框
    Get.dialog(
      AlertDialog(
        title: Text('Start Appointment'),
        content: Text('Start appointment process for ${store.name}?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              // 开始预约流程
              AppointmentHelper.startAppointmentFlow(
                storeId: store.storeId,
                onAppointmentCompleted: () {
                  Get.snackbar(
                    'Success',
                    'Appointment completed successfully!',
                    backgroundColor: Colors.green,
                    colorText: Colors.white,
                  );
                },
              );
            },
            child: Text('Start'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'Store Selection Test',
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Color(0xFF262626)),
          onPressed: () => Get.back(),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh, color: Color(0xFF262626)),
            onPressed: _loadStores,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: Color(0xFFA126FF),
        ),
      );
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.w,
              color: Colors.red,
            ),
            SizedBox(height: 16.w),
            Text(
              errorMessage!,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.w),
            ElevatedButton(
              onPressed: _loadStores,
              child: Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (stores.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.store_outlined,
              size: 64.w,
              color: Color(0xFF999999),
            ),
            SizedBox(height: 16.w),
            Text(
              'No appointment-enabled stores found',
              style: TextStyle(
                fontSize: 16.sp,
                color: Color(0xFF999999),
              ),
            ),
            SizedBox(height: 16.w),
            ElevatedButton(
              onPressed: _loadStores,
              child: Text('Refresh'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: stores.length,
      itemBuilder: (context, index) {
        final store = stores[index];
        return _buildStoreCard(store);
      },
    );
  }

  Widget _buildStoreCard(StoreInfo store) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.w),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 店铺名称和状态
          Row(
            children: [
              Expanded(
                child: Text(
                  store.name ?? 'Unknown Store',
                  style: TextStyle(
                    fontFamily: 'Manrope',
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF262626),
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.w),
                decoration: BoxDecoration(
                  color: store.appointmentOpen == true 
                      ? Color(0xFFE8F5E8) 
                      : Color(0xFFFFF2E8),
                  borderRadius: BorderRadius.circular(12.w),
                ),
                child: Text(
                  store.appointmentOpen == true ? 'Open' : 'Closed',
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                    color: store.appointmentOpen == true 
                        ? Color(0xFF00AA00) 
                        : Color(0xFFFF8800),
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 8.w),
          
          // 业务类型
          Text(
            store.businessType.name.replaceAll('_', ' ').toUpperCase(),
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
              color: Color(0xFFA126FF),
            ),
          ),
          
          SizedBox(height: 8.w),
          
          // 地址
          Text(
            '${store.currentAddress.addressLine1}, ${store.currentAddress.city}, ${store.currentAddress.province}',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
              color: Color(0xFF666666),
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          SizedBox(height: 12.w),
          
          // 服务类型
          Wrap(
            spacing: 8.w,
            runSpacing: 4.w,
            children: _buildServiceTags(store.services),
          ),
          
          SizedBox(height: 16.w),
          
          // 预约按钮
          SizedBox(
            width: double.infinity,
            height: 44.w,
            child: ElevatedButton(
              onPressed: store.appointmentOpen == true 
                  ? () => _startAppointmentFlow(store)
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: store.appointmentOpen == true 
                    ? Color(0xFFF2D3A4) 
                    : Color(0xFFC6C6C6),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.w),
                ),
                elevation: 0,
              ),
              child: Text(
                store.appointmentOpen == true 
                    ? 'Start Appointment' 
                    : 'Appointment Closed',
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildServiceTags(Services services) {
    final List<Widget> tags = [];
    
    if (services.grooming) {
      tags.add(_buildServiceTag('Grooming'));
    }
    if (services.boarding) {
      tags.add(_buildServiceTag('Boarding'));
    }
    if (services.veterinary) {
      tags.add(_buildServiceTag('Veterinary'));
    }
    if (services.training) {
      tags.add(_buildServiceTag('Training'));
    }
    if (services.retail) {
      tags.add(_buildServiceTag('Retail'));
    }
    
    return tags;
  }

  Widget _buildServiceTag(String service) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.w),
      decoration: BoxDecoration(
        color: Color(0xFFF0F0F0),
        borderRadius: BorderRadius.circular(8.w),
      ),
      child: Text(
        service,
        style: TextStyle(
          fontSize: 10.sp,
          fontWeight: FontWeight.w500,
          color: Color(0xFF666666),
        ),
      ),
    );
  }
}
