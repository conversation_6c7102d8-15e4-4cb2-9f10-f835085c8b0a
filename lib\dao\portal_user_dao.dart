import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';

/// Portal User DAO - 商家系统用户数据访问层
/// 支持主集合和子集合的CRUD操作
class PortalUserDao {
  final FirebaseFirestore _db = FirebaseFirestore.instance;

  // Collection references
  final CollectionReference _portalUserAccountCollection =
      FirebaseFirestore.instance.collection(PortalUserAccount.collection);
  final CollectionReference _portalUserDataCollection =
      FirebaseFirestore.instance.collection(PortalUserData.collection);

  // ==================== Portal User Account CRUD ====================

  /// 添加Portal User Account
  Future<void> addPortalUserAccount(PortalUserAccount account) async {
    DocumentReference accountRef = _portalUserAccountCollection.doc(account.sid);

    Map<String, dynamic> accountDoc = account.toFirestoreData();
    accountDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await accountRef.set(accountDoc);
  }

  /// 更新Portal User Account
  Future<void> updatePortalUserAccount(PortalUserAccount account, {bool mergeOption = false}) async {
    DocumentReference accountRef = _portalUserAccountCollection.doc(account.sid!);
    Map<String, dynamic> accountDoc = account.toFirestoreData();
    accountDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await accountRef.set(accountDoc, SetOptions(merge: mergeOption));
  }

  /// 删除Portal User Account
  Future<void> deletePortalUserAccount(String accountId) async {
    DocumentReference accountRef = _portalUserAccountCollection.doc(accountId);
    await accountRef.delete();
  }

  /// 根据ID获取Portal User Account
  Future<PortalUserAccount?> getPortalUserAccountById(String accountId) async {
    DocumentSnapshot doc = await _portalUserAccountCollection.doc(accountId).get();
    if (doc.exists) {
      return PortalUserAccount.fromFirestoreData(doc);
    }
    return null;
  }

  /// 根据Firebase ID获取Portal User Account
  Future<PortalUserAccount?> getPortalUserAccountByFid(String fid) async {
    QuerySnapshot querySnapshot = await _portalUserAccountCollection
        .where('fid', isEqualTo: fid)
        .where(CommonField.isValid, isEqualTo: true)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return PortalUserAccount.fromFirestoreData(querySnapshot.docs.first);
    }
    return null;
  }

  /// 根据邮箱获取Portal User Account
  Future<PortalUserAccount?> getPortalUserAccountByEmail(String email) async {
    QuerySnapshot querySnapshot = await _portalUserAccountCollection
        .where('email', isEqualTo: email)
        .where(CommonField.isValid, isEqualTo: true)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return PortalUserAccount.fromFirestoreData(querySnapshot.docs.first);
    }
    return null;
  }

  // ==================== Portal User Data CRUD ====================

  /// 添加Portal User Data
  Future<void> addPortalUserData(PortalUserData userData) async {
    DocumentReference userDataRef = _portalUserDataCollection.doc(userData.sid);

    Map<String, dynamic> userDataDoc = userData.toFirestoreData();
    userDataDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await userDataRef.set(userDataDoc);
  }

  /// 更新Portal User Data
  Future<void> updatePortalUserData(PortalUserData userData, {bool mergeOption = false}) async {
    DocumentReference userDataRef = _portalUserDataCollection.doc(userData.sid!);
    Map<String, dynamic> userDataDoc = userData.toFirestoreData();
    userDataDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await userDataRef.set(userDataDoc, SetOptions(merge: mergeOption));
  }

  /// 删除Portal User Data
  Future<void> deletePortalUserData(String userDataId) async {
    DocumentReference userDataRef = _portalUserDataCollection.doc(userDataId);
    await userDataRef.delete();
  }

  /// 根据ID获取Portal User Data
  Future<PortalUserData?> getPortalUserDataById(String userDataId) async {
    DocumentSnapshot doc = await _portalUserDataCollection.doc(userDataId).get();
    if (doc.exists) {
      return PortalUserData.fromFirestoreData(doc);
    }
    return null;
  }

  /// 根据UID获取Portal User Data
  Future<PortalUserData?> getPortalUserDataByUid(String uid) async {
    QuerySnapshot querySnapshot = await _portalUserDataCollection
        .where('uid', isEqualTo: uid)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      final doc = querySnapshot.docs.first;
      final data = doc.data() as Map<String, dynamic>;

      // 检查 is_valid 字段：null 或 true 都认为是有效的
      final isValid = data['is_valid'];
      if (isValid == null || isValid == true) {
        return PortalUserData.fromFirestoreData(doc);
      }
    }
    return null;
  }

  /// 获取所有员工类型的Portal User Data
  Future<List<PortalUserData>> getAllStaffUsers() async {
    QuerySnapshot querySnapshot = await _portalUserDataCollection
        .where('userType', isEqualTo: '202')
        .where(CommonField.isValid, isEqualTo: true)
        .orderBy(CommonField.createDate, descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => PortalUserData.fromFirestoreData(doc))
        .toList();
  }

  // ==================== Employee Schedule Sub-collection CRUD ====================

  /// 获取用户的员工排班子集合引用
  CollectionReference _getEmployeeScheduleSubCollection(String userId) {
    return _portalUserDataCollection
        .doc(userId)
        .collection(EmployeeSchedule.collection);
  }

  /// 添加员工排班
  Future<void> addEmployeeSchedule(String userId, EmployeeSchedule schedule) async {
    CollectionReference subCollection = _getEmployeeScheduleSubCollection(userId);
    DocumentReference scheduleRef = subCollection.doc(schedule.sid);

    Map<String, dynamic> scheduleDoc = schedule.toFirestoreData();
    scheduleDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await scheduleRef.set(scheduleDoc);
  }

  /// 更新员工排班
  Future<void> updateEmployeeSchedule(String userId, EmployeeSchedule schedule, {bool mergeOption = false}) async {
    CollectionReference subCollection = _getEmployeeScheduleSubCollection(userId);
    DocumentReference scheduleRef = subCollection.doc(schedule.sid!);
    Map<String, dynamic> scheduleDoc = schedule.toFirestoreData();
    scheduleDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await scheduleRef.set(scheduleDoc, SetOptions(merge: mergeOption));
  }

  /// 删除员工排班
  Future<void> deleteEmployeeSchedule(String userId, String scheduleId) async {
    CollectionReference subCollection = _getEmployeeScheduleSubCollection(userId);
    DocumentReference scheduleRef = subCollection.doc(scheduleId);
    await scheduleRef.delete();
  }

  /// 根据ID获取员工排班
  Future<EmployeeSchedule?> getEmployeeScheduleById(String userId, String scheduleId) async {
    CollectionReference subCollection = _getEmployeeScheduleSubCollection(userId);
    DocumentSnapshot doc = await subCollection.doc(scheduleId).get();
    if (doc.exists) {
      return EmployeeSchedule.fromFirestoreData(doc);
    }
    return null;
  }

  /// 获取用户的所有员工排班
  Future<List<EmployeeSchedule>> getEmployeeSchedulesByUserId(String userId) async {
    CollectionReference subCollection = _getEmployeeScheduleSubCollection(userId);
    QuerySnapshot querySnapshot = await subCollection
        .where(CommonField.isValid, isEqualTo: true)
        .orderBy(CommonField.createDate, descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => EmployeeSchedule.fromFirestoreData(doc))
        .toList();
  }

  /// 获取用户的活跃员工排班
  Future<EmployeeSchedule?> getActiveEmployeeSchedule(String userId) async {
    CollectionReference subCollection = _getEmployeeScheduleSubCollection(userId);
    QuerySnapshot querySnapshot = await subCollection
        .where('active', isEqualTo: true)
        .where(CommonField.isValid, isEqualTo: true)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return EmployeeSchedule.fromFirestoreData(querySnapshot.docs.first);
    }
    return null;
  }

  /// 根据店铺ID获取所有员工排班
  Future<List<EmployeeSchedule>> getEmployeeSchedulesByStoreId(String storeId) async {
    // 需要查询所有用户的子集合，这在Firestore中比较复杂
    // 建议在实际应用中使用Cloud Functions或者维护一个索引集合
    throw UnimplementedError('需要使用Cloud Functions或索引集合来实现跨用户子集合查询');
  }

  // ==================== Employee Service Sub-collection CRUD ====================

  /// 获取用户的员工服务子集合引用
  CollectionReference _getEmployeeServiceSubCollection(String userId) {
    return _portalUserDataCollection
        .doc(userId)
        .collection(StaffServiceOffering.collection);
  }

  /// 添加员工服务
  Future<void> addEmployeeService(String userId, StaffServiceOffering service) async {
    CollectionReference subCollection = _getEmployeeServiceSubCollection(userId);
    DocumentReference serviceRef = subCollection.doc(service.sid);

    Map<String, dynamic> serviceDoc = service.toFirestoreData();
    serviceDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await serviceRef.set(serviceDoc);
  }

  /// 更新员工服务
  Future<void> updateEmployeeService(String userId, StaffServiceOffering service, {bool mergeOption = false}) async {
    CollectionReference subCollection = _getEmployeeServiceSubCollection(userId);
    DocumentReference serviceRef = subCollection.doc(service.sid!);
    Map<String, dynamic> serviceDoc = service.toFirestoreData();
    serviceDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await serviceRef.set(serviceDoc, SetOptions(merge: mergeOption));
  }

  /// 删除员工服务
  Future<void> deleteEmployeeService(String userId, String serviceId) async {
    CollectionReference subCollection = _getEmployeeServiceSubCollection(userId);
    DocumentReference serviceRef = subCollection.doc(serviceId);
    await serviceRef.delete();
  }

  /// 根据ID获取员工服务
  Future<StaffServiceOffering?> getEmployeeServiceById(String userId, String serviceId) async {
    CollectionReference subCollection = _getEmployeeServiceSubCollection(userId);
    DocumentSnapshot doc = await subCollection.doc(serviceId).get();
    if (doc.exists) {
      return StaffServiceOffering.fromFirestoreData(doc);
    }
    return null;
  }

  /// 获取用户的所有员工服务
  Future<List<StaffServiceOffering>> getEmployeeServicesByUserId(String userId) async {
    CollectionReference subCollection = _getEmployeeServiceSubCollection(userId);
    QuerySnapshot querySnapshot = await subCollection
        .where(CommonField.isValid, isEqualTo: true)
        .orderBy(CommonField.createDate, descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => StaffServiceOffering.fromFirestoreData(doc))
        .toList();
  }

  /// 获取用户的活跃员工服务
  Future<List<StaffServiceOffering>> getActiveEmployeeServices(String userId) async {
    CollectionReference subCollection = _getEmployeeServiceSubCollection(userId);
    QuerySnapshot querySnapshot = await subCollection
        .where('serviceStatus', isEqualTo: 'active')
        .where(CommonField.isValid, isEqualTo: true)
        .orderBy(CommonField.createDate, descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => StaffServiceOffering.fromFirestoreData(doc))
        .toList();
  }

  /// 根据服务类别获取员工服务
  Future<List<StaffServiceOffering>> getEmployeeServicesByCategory(String userId, String category) async {
    CollectionReference subCollection = _getEmployeeServiceSubCollection(userId);
    QuerySnapshot querySnapshot = await subCollection
        .where('serviceCategory', isEqualTo: category)
        .where('serviceStatus', isEqualTo: 'active')
        .where(CommonField.isValid, isEqualTo: true)
        .orderBy(CommonField.createDate, descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => StaffServiceOffering.fromFirestoreData(doc))
        .toList();
  }

  // ==================== 组合查询方法 ====================

  /// 获取完整的员工信息（包括账户、数据、排班、服务）
  Future<Map<String, dynamic>?> getCompleteStaffInfo(String accountId) async {
    // 获取账户信息
    PortalUserAccount? account = await getPortalUserAccountById(accountId);
    if (account == null) return null;

    // 获取用户数据
    PortalUserData? userData = await getPortalUserDataByUid(accountId);
    if (userData == null) return null;

    // 获取排班信息
    EmployeeSchedule? schedule = await getActiveEmployeeSchedule(userData.sid!);

    // 获取服务信息
    List<StaffServiceOffering> services = await getActiveEmployeeServices(userData.sid!);

    return {
      'account': account,
      'userData': userData,
      'schedule': schedule,
      'services': services,
    };
  }

  /// 根据店铺ID获取所有员工的完整信息
  Future<List<Map<String, dynamic>>> getCompleteStaffInfoByStoreId(String storeId) async {
    // 首先获取所有员工用户数据
    List<PortalUserData> staffUsers = await getAllStaffUsers();

    List<Map<String, dynamic>> completeInfoList = [];

    for (PortalUserData userData in staffUsers) {
      // 获取账户信息
      PortalUserAccount? account = await getPortalUserAccountById(userData.uid);
      if (account == null) continue;

      // 获取排班信息
      EmployeeSchedule? schedule = await getActiveEmployeeSchedule(userData.sid!);

      // 获取服务信息
      List<StaffServiceOffering> services = await getActiveEmployeeServices(userData.sid!);

      // 过滤属于指定店铺的员工
      if (schedule?.storeId == storeId || services.any((s) => s.storeId == storeId)) {
        completeInfoList.add({
          'account': account,
          'userData': userData,
          'schedule': schedule,
          'services': services,
        });
      }
    }

    return completeInfoList;
  }
}
