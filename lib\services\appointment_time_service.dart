import 'dart:async';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/models/models_i.dart';

/// 可用时间段模型
class AvailableTimeSlot {
  final String date;          // YYYY-MM-DD
  final String startTime;     // HH:MM
  final String endTime;       // HH:MM
  final String staffId;       // 员工ID
  final String serviceId;     // 服务ID
  final int duration;         // 时长（分钟）

  AvailableTimeSlot({
    required this.date,
    required this.startTime,
    required this.endTime,
    required this.staffId,
    required this.serviceId,
    required this.duration,
  });

  @override
  String toString() {
    return 'AvailableTimeSlot(date: $date, startTime: $startTime, endTime: $endTime, staffId: $staffId, serviceId: $serviceId, duration: $duration)';
  }
}

/// 预约时间计算服务
/// 基于员工排班和服务时长计算可用时间段
class AppointmentTimeService {
  final PortalUserDao _portalUserDao = PortalUserDao();
  final AppointmentDao _appointmentDao = AppointmentDao();

  static const int timeSlotInterval = 15; // 时间段间隔（分钟）
  static const int maxBookingDaysAhead = 60; // 最多提前预约天数

  // ==================== 核心时间计算方法 ====================

  /// 获取指定员工和服务的可用时间段
  Future<List<AvailableTimeSlot>> getAvailableTimeSlots({
    required String staffId,
    required String serviceId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    List<AvailableTimeSlot> availableSlots = [];

    // 获取员工排班信息
    EmployeeSchedule? schedule = await _portalUserDao.getActiveEmployeeSchedule(staffId);
    if (schedule == null || !schedule.active) {
      return availableSlots;
    }

    // 获取服务信息
    StaffServiceOffering? service = await _portalUserDao.getEmployeeServiceById(staffId, serviceId);
    if (service == null || !service.isActive()) {
      return availableSlots;
    }

    // 获取指定日期范围内的现有预约
    List<Appointment> existingAppointments = await _appointmentDao.getAppointmentsByDateRange(
      service.storeId,
      startDate,
      endDate,
    );

    // 过滤出该员工的预约
    List<Appointment> staffAppointments = existingAppointments
        .where((appointment) => appointment.staffId == staffId)
        .toList();

    // 遍历每一天
    DateTime currentDate = startDate;
    while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
      List<AvailableTimeSlot> dailySlots = await _getDailyAvailableSlots(
        date: currentDate,
        schedule: schedule,
        service: service,
        existingAppointments: staffAppointments,
      );
      availableSlots.addAll(dailySlots);
      currentDate = currentDate.add(const Duration(days: 1));
    }

    return availableSlots;
  }

  /// 获取指定日期的可用时间段
  Future<List<AvailableTimeSlot>> _getDailyAvailableSlots({
    required DateTime date,
    required EmployeeSchedule schedule,
    required StaffServiceOffering service,
    required List<Appointment> existingAppointments,
  }) async {
    List<AvailableTimeSlot> dailySlots = [];

    // 获取当天的工作时间
    String weekday = _getWeekdayName(date.weekday);
    WorkTime? workTime = schedule.getWorkTimeForDay(weekday);
    
    if (workTime == null || !workTime.open) {
      return dailySlots;
    }

    // 生成当天的所有可能时间段
    List<String> allTimeSlots = _generateTimeSlots(
      workTime.startTime,
      workTime.endTime,
      timeSlotInterval,
    );

    // 获取当天的预约
    String dateStr = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    List<Appointment> dayAppointments = existingAppointments
        .where((appointment) => appointment.timeInfo.date == dateStr)
        .toList();

    // 计算被占用的时间段
    Set<String> occupiedSlots = _getOccupiedTimeSlots(dayAppointments);

    // 查找可用的连续时间段
    List<AvailableTimeSlot> availableSlots = _findAvailableSlots(
      allTimeSlots: allTimeSlots,
      occupiedSlots: occupiedSlots,
      serviceDuration: service.serviceDuration,
      date: dateStr,
      staffId: schedule.uid,
      serviceId: service.sid!,
    );

    return availableSlots;
  }

  /// 生成时间段列表
  List<String> _generateTimeSlots(String startTime, String endTime, int intervalMinutes) {
    List<String> timeSlots = [];
    
    DateTime start = _parseTime(startTime);
    DateTime end = _parseTime(endTime);
    
    DateTime current = start;
    while (current.isBefore(end)) {
      timeSlots.add(_formatTime(current));
      current = current.add(Duration(minutes: intervalMinutes));
    }
    
    return timeSlots;
  }

  /// 获取被占用的时间段
  Set<String> _getOccupiedTimeSlots(List<Appointment> appointments) {
    Set<String> occupiedSlots = {};
    
    for (Appointment appointment in appointments) {
      // 跳过已取消的预约
      if (appointment.status == 'cancelled') continue;
      
      DateTime startTime = _parseTime(appointment.timeInfo.startTime);
      DateTime endTime = _parseTime(appointment.timeInfo.endTime);
      
      DateTime current = startTime;
      while (current.isBefore(endTime)) {
        occupiedSlots.add(_formatTime(current));
        current = current.add(Duration(minutes: timeSlotInterval));
      }
    }
    
    return occupiedSlots;
  }

  /// 查找可用的时间段
  List<AvailableTimeSlot> _findAvailableSlots({
    required List<String> allTimeSlots,
    required Set<String> occupiedSlots,
    required int serviceDuration,
    required String date,
    required String staffId,
    required String serviceId,
  }) {
    List<AvailableTimeSlot> availableSlots = [];
    
    // 计算需要的时间段数量
    int requiredSlots = (serviceDuration / timeSlotInterval).ceil();
    
    for (int i = 0; i <= allTimeSlots.length - requiredSlots; i++) {
      // 检查连续的时间段是否都可用
      bool isAvailable = true;
      for (int j = 0; j < requiredSlots; j++) {
        if (occupiedSlots.contains(allTimeSlots[i + j])) {
          isAvailable = false;
          break;
        }
      }
      
      if (isAvailable) {
        String startTime = allTimeSlots[i];
        String endTime = _calculateEndTime(startTime, serviceDuration);
        
        availableSlots.add(AvailableTimeSlot(
          date: date,
          startTime: startTime,
          endTime: endTime,
          staffId: staffId,
          serviceId: serviceId,
          duration: serviceDuration,
        ));
      }
    }
    
    return availableSlots;
  }

  // ==================== 便捷查询方法 ====================

  /// 获取指定店铺的所有可用时间段
  Future<List<AvailableTimeSlot>> getStoreAvailableTimeSlots({
    required String storeId,
    required String serviceCategory,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    List<AvailableTimeSlot> allSlots = [];

    // 获取店铺的所有员工信息
    List<Map<String, dynamic>> staffInfoList = await _portalUserDao.getCompleteStaffInfoByStoreId(storeId);

    for (Map<String, dynamic> staffInfo in staffInfoList) {
      PortalUserData userData = staffInfo['userData'];
      List<StaffServiceOffering> services = staffInfo['services'];

      // 过滤指定类别的服务
      List<StaffServiceOffering> categoryServices = services
          .where((service) => service.serviceCategory == serviceCategory && service.isActive())
          .toList();

      for (StaffServiceOffering service in categoryServices) {
        List<AvailableTimeSlot> serviceSlots = await getAvailableTimeSlots(
          staffId: userData.sid!,
          serviceId: service.sid!,
          startDate: startDate,
          endDate: endDate,
        );
        allSlots.addAll(serviceSlots);
      }
    }

    // 按日期和时间排序
    allSlots.sort((a, b) {
      int dateComparison = a.date.compareTo(b.date);
      if (dateComparison != 0) return dateComparison;
      return a.startTime.compareTo(b.startTime);
    });

    return allSlots;
  }

  /// 获取下一个可用时间段
  Future<AvailableTimeSlot?> getNextAvailableTimeSlot({
    required String staffId,
    required String serviceId,
  }) async {
    DateTime startDate = DateTime.now();
    DateTime endDate = startDate.add(Duration(days: maxBookingDaysAhead));

    List<AvailableTimeSlot> slots = await getAvailableTimeSlots(
      staffId: staffId,
      serviceId: serviceId,
      startDate: startDate,
      endDate: endDate,
    );

    return slots.isNotEmpty ? slots.first : null;
  }

  /// 检查指定时间段是否可用
  Future<bool> isTimeSlotAvailable({
    required String staffId,
    required String serviceId,
    required DateTime appointmentDate,
    required String startTime,
    required int duration,
  }) async {
    DateTime endDate = appointmentDate.add(const Duration(days: 1));
    
    List<AvailableTimeSlot> slots = await getAvailableTimeSlots(
      staffId: staffId,
      serviceId: serviceId,
      startDate: appointmentDate,
      endDate: endDate,
    );

    String dateStr = '${appointmentDate.year}-${appointmentDate.month.toString().padLeft(2, '0')}-${appointmentDate.day.toString().padLeft(2, '0')}';
    return slots.any((slot) => 
        slot.date == dateStr && 
        slot.startTime == startTime && 
        slot.duration >= duration);
  }

  // ==================== 工具方法 ====================

  /// 解析时间字符串为DateTime
  DateTime _parseTime(String time) {
    List<String> parts = time.split(':');
    int hour = int.parse(parts[0]);
    int minute = int.parse(parts[1]);
    return DateTime(2000, 1, 1, hour, minute);
  }

  /// 格式化DateTime为时间字符串
  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 计算结束时间
  String _calculateEndTime(String startTime, int durationMinutes) {
    DateTime start = _parseTime(startTime);
    DateTime end = start.add(Duration(minutes: durationMinutes));
    return _formatTime(end);
  }

  /// 获取星期名称
  String _getWeekdayName(int weekday) {
    const weekdays = [
      'Monday', 'Tuesday', 'Wednesday', 'Thursday', 
      'Friday', 'Saturday', 'Sunday'
    ];
    return weekdays[weekday - 1];
  }
}
