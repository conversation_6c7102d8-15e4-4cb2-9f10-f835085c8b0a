/// 员工角色枚举
/// 对应 web端 types.ts 中的 StoreRole
enum StoreRole {
  /// 店主
  storeOwner('STORE_OWNER'),
  /// 店铺管理员
  storeManager('STORE_MANAGER'),
  /// 店铺员工
  storeStaff('STORE_STAFF'),
  /// 兼职员工
  partTimeStaff('PART_TIME_STAFF');

  const StoreRole(this.value);
  final String value;

  static StoreRole fromString(String value) {
    return StoreRole.values.firstWhere(
      (role) => role.value == value,
      orElse: () => StoreRole.storeStaff,
    );
  }

  String get displayName {
    switch (this) {
      case StoreRole.storeOwner:
        return '店主';
      case StoreRole.storeManager:
        return '店铺管理员';
      case StoreRole.storeStaff:
        return '店铺员工';
      case StoreRole.partTimeStaff:
        return '兼职员工';
    }
  }
}

/// 员工状态枚举
enum StaffStatus {
  /// 活跃
  active('ACTIVE'),
  /// 非活跃
  inactive('INACTIVE'),
  /// 休假
  onLeave('ON_LEAVE'),
  /// 已离职
  terminated('TERMINATED');

  const StaffStatus(this.value);
  final String value;

  static StaffStatus fromString(String value) {
    return StaffStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => StaffStatus.active,
    );
  }

  String get displayName {
    switch (this) {
      case StaffStatus.active:
        return '活跃';
      case StaffStatus.inactive:
        return '非活跃';
      case StaffStatus.onLeave:
        return '休假';
      case StaffStatus.terminated:
        return '已离职';
    }
  }
}

/// 工作日枚举
enum WorkDay {
  monday('MONDAY'),
  tuesday('TUESDAY'),
  wednesday('WEDNESDAY'),
  thursday('THURSDAY'),
  friday('FRIDAY'),
  saturday('SATURDAY'),
  sunday('SUNDAY');

  const WorkDay(this.value);
  final String value;

  static WorkDay fromString(String value) {
    return WorkDay.values.firstWhere(
      (day) => day.value == value,
      orElse: () => WorkDay.monday,
    );
  }

  String get displayName {
    switch (this) {
      case WorkDay.monday:
        return '周一';
      case WorkDay.tuesday:
        return '周二';
      case WorkDay.wednesday:
        return '周三';
      case WorkDay.thursday:
        return '周四';
      case WorkDay.friday:
        return '周五';
      case WorkDay.saturday:
        return '周六';
      case WorkDay.sunday:
        return '周日';
    }
  }

  int get weekdayNumber {
    switch (this) {
      case WorkDay.monday:
        return 1;
      case WorkDay.tuesday:
        return 2;
      case WorkDay.wednesday:
        return 3;
      case WorkDay.thursday:
        return 4;
      case WorkDay.friday:
        return 5;
      case WorkDay.saturday:
        return 6;
      case WorkDay.sunday:
        return 7;
    }
  }

  static WorkDay fromWeekday(int weekday) {
    switch (weekday) {
      case 1:
        return WorkDay.monday;
      case 2:
        return WorkDay.tuesday;
      case 3:
        return WorkDay.wednesday;
      case 4:
        return WorkDay.thursday;
      case 5:
        return WorkDay.friday;
      case 6:
        return WorkDay.saturday;
      case 7:
        return WorkDay.sunday;
      default:
        return WorkDay.monday;
    }
  }
}