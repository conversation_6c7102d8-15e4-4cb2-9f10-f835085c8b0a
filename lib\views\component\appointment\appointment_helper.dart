import 'package:onenata_app/models/pet.dart';
import 'package:onenata_app/models/bookable_service.dart';
import 'package:onenata_app/views/component/appointment/pet_selection_bottom_sheet.dart';
import 'package:onenata_app/views/component/appointment/service_selection_bottom_sheet.dart';
import 'package:onenata_app/views/component/appointment/datetime_selection_bottom_sheet.dart';
import 'package:onenata_app/views/component/appointment/note_input_bottom_sheet.dart';
import 'package:onenata_app/views/component/appointment/appointment_confirmation_bottom_sheet.dart';

/// 预约流程管理类
/// 统一管理整个预约流程的各个步骤
class AppointmentHelper {

  /// 开始预约流程 - 从宠物选择开始
  static void startAppointmentFlow({
    String? storeId,
    Function()? onAppointmentCompleted,
  }) {
    PetSelectionBottomSheetHelper.show(
      storeId: storeId,
      onAppointmentMade: onAppointmentCompleted,
    );
  }
  
  /// 直接跳转到服务选择（如果已知宠物）
  static void showServiceSelection({
    required Pet selectedPet,
    Function()? onServiceSelected,
  }) {
    ServiceSelectionBottomSheetHelper.show(
      selectedPet: selectedPet,
      onServiceSelected: onServiceSelected,
    );
  }

  /// 直接跳转到日期时间选择（如果已知宠物和服务）
  static void showDateTimeSelection({
    required Pet selectedPet,
    required BookableService selectedService,
    Function()? onAppointmentConfirmed,
  }) {
    DateTimeSelectionBottomSheetHelper.show(
      selectedPet: selectedPet,
      selectedService: selectedService,
      onAppointmentConfirmed: onAppointmentConfirmed,
    );
  }

  /// 直接跳转到备注输入（如果已知所有前置信息）
  static void showNoteInput({
    required Pet selectedPet,
    required BookableService selectedService,
    required DateTime selectedDate,
    required TimeSlot selectedTimeSlot,
    required Staff selectedStaff,
    Function()? onAppointmentSubmitted,
  }) {
    NoteInputBottomSheetHelper.show(
      selectedPet: selectedPet,
      selectedService: selectedService,
      selectedDate: selectedDate,
      selectedTimeSlot: selectedTimeSlot,
      selectedStaff: selectedStaff,
      onAppointmentSubmitted: onAppointmentSubmitted,
    );
  }

  /// 直接显示预约确认页面
  static void showAppointmentConfirmation({
    required Pet selectedPet,
    required BookableService selectedService,
    required DateTime selectedDate,
    required TimeSlot selectedTimeSlot,
    required Staff selectedStaff,
    required String notes,
    Function()? onBackToMain,
  }) {
    AppointmentConfirmationBottomSheetHelper.show(
      selectedPet: selectedPet,
      selectedService: selectedService,
      selectedDate: selectedDate,
      selectedTimeSlot: selectedTimeSlot,
      selectedStaff: selectedStaff,
      notes: notes,
      onBackToMain: onBackToMain,
    );
  }
}
