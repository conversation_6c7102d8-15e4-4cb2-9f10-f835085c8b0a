import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onenata_app/models/pet.dart';
import 'package:onenata_app/models/bookable_service.dart';
import 'package:onenata_app/views/component/appointment/datetime_selection_bottom_sheet.dart';
import 'package:onenata_app/views/component/appointment/appointment_confirmation_bottom_sheet.dart';

class NoteInputBottomSheet extends StatefulWidget {
  final Pet selectedPet;
  final BookableService selectedService;
  final DateTime selectedDate;
  final TimeSlot selectedTimeSlot;
  final Staff selectedStaff;
  final VoidCallback? onAppointmentSubmitted;

  const NoteInputBottomSheet({
    Key? key,
    required this.selectedPet,
    required this.selectedService,
    required this.selectedDate,
    required this.selectedTimeSlot,
    required this.selectedStaff,
    this.onAppointmentSubmitted,
  }) : super(key: key);

  @override
  State<NoteInputBottomSheet> createState() => _NoteInputBottomSheetState();
}

class _NoteInputBottomSheetState extends State<NoteInputBottomSheet> {
  final TextEditingController _noteController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _noteController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _submitAppointment() {
    // 关闭当前弹窗并显示确认页面
    Get.back();
    AppointmentConfirmationBottomSheetHelper.show(
      selectedPet: widget.selectedPet,
      selectedService: widget.selectedService,
      selectedDate: widget.selectedDate,
      selectedTimeSlot: widget.selectedTimeSlot,
      selectedStaff: widget.selectedStaff,
      notes: _noteController.text.trim(),
      onBackToMain: () {
        widget.onAppointmentSubmitted?.call();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 403.w,
      height: 823.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(26.w)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 拖拽指示条
          Container(
            margin: EdgeInsets.only(top: 12.w),
            width: double.infinity,
            alignment: Alignment.center,
            child: Container(
              width: 36.w,
              height: 4.w,
              decoration: BoxDecoration(
                color: Color(0xFFE0E0E0),
                borderRadius: BorderRadius.circular(2.w),
              ),
            ),
          ),
          
          // 标题
          Padding(
            padding: EdgeInsets.only(top: 32.w, left: 24.w, right: 24.w),
            child: Text(
              'Add a note (optional)',
              style: TextStyle(
                fontFamily: 'Manrope',
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: Color(0xFF262626),
              ),
            ),
          ),
          
          // 描述文字
          Padding(
            padding: EdgeInsets.only(top: 8.w, left: 24.w, right: 24.w),
            child: Text(
              'Let us know anything special we should be aware of. (max 150 words)',
              style: TextStyle(
                fontFamily: 'Manrope',
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                color: Color(0xFF666666),
              ),
            ),
          ),
          
          // 输入框
          Expanded(
            child: Container(
              margin: EdgeInsets.all(24.w),
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Color(0xFFF8F8F8),
                borderRadius: BorderRadius.circular(12.w),
                border: Border.all(
                  color: Color(0xFFE5E5E5),
                  width: 1.w,
                ),
              ),
              child: TextField(
                controller: _noteController,
                focusNode: _focusNode,
                maxLines: null,
                expands: true,
                textAlignVertical: TextAlignVertical.top,
                decoration: InputDecoration(
                  hintText: 'Add Notes...',
                  hintStyle: TextStyle(
                    fontFamily: 'Manrope',
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF999999),
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                ),
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF262626),
                ),
              ),
            ),
          ),
          
          // Submit 按钮 - 始终激活
          Container(
            margin: EdgeInsets.only(bottom: 20.w, left: 24.w, right: 24.w),
            width: double.infinity,
            height: 48.w,
            child: ElevatedButton(
              onPressed: _submitAppointment,
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFFF2D3A4),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.w),
                ),
                elevation: 0,
              ),
              child: Text(
                'Submit',
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 显示备注输入底部弹窗的静态方法
class NoteInputBottomSheetHelper {
  static void show({
    required Pet selectedPet,
    required BookableService selectedService,
    required DateTime selectedDate,
    required TimeSlot selectedTimeSlot,
    required Staff selectedStaff,
    VoidCallback? onAppointmentSubmitted,
  }) {
    Get.bottomSheet(
      NoteInputBottomSheet(
        selectedPet: selectedPet,
        selectedService: selectedService,
        selectedDate: selectedDate,
        selectedTimeSlot: selectedTimeSlot,
        selectedStaff: selectedStaff,
        onAppointmentSubmitted: onAppointmentSubmitted,
      ),
      isDismissible: true,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }
}
